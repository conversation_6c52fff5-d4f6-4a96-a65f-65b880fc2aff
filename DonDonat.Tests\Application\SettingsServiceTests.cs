using System;
using System.IO;
using System.Threading.Tasks;
using DonDonat.Tests.Helpers;
using DonDonat.WPF.Application.Services;
using DonDonat.WPF.Domain.Entities;

namespace DonDonat.Tests.Application
{
    /// <summary>
    /// SettingsService için unit testler
    /// Mock'lar kull<PERSON>rak dosya I/O işlemlerini test eder
    /// </summary>
    public class SettingsServiceTests : TestBase
    {
        private readonly SettingsService _settingsService;

        public SettingsServiceTests()
        {
            _settingsService = new SettingsService();
        }

        [Fact]
        public async Task LoadSettingsAsync_ShouldReturnTrue_WhenSettingsFileExists()
        {
            // Arrange
            var settingsJson = """
                {
                  "theme": "Dark",
                  "language": "tr-TR",
                  "fontSize": 14.0,
                  "autoSaveEnabled": true,
                  "version": "1.0"
                }
                """;

            var settingsFilePath = CreateTempFile("settings.json", settingsJson);

            // Act
            var result = await _settingsService.LoadSettingsAsync();

            // Assert
            result.Should().BeTrue();
            _settingsService.CurrentSettings.Should().NotBeNull();
        }

        [Fact]
        public async Task LoadSettingsAsync_ShouldCreateDefaultSettings_WhenFileNotExists()
        {
            // Act
            var result = await _settingsService.LoadSettingsAsync();

            // Assert
            result.Should().BeTrue();
            _settingsService.CurrentSettings.Should().NotBeNull();
            _settingsService.CurrentSettings.Theme.Should().Be("Light"); // Varsayılan değer
        }

        [Fact]
        public async Task SaveSettingsAsync_ShouldReturnTrue_WhenValidSettings()
        {
            // Arrange
            await _settingsService.LoadSettingsAsync();
            _settingsService.CurrentSettings.Theme = "Dark";
            _settingsService.CurrentSettings.FontSize = 16.0;

            // Act
            var result = await _settingsService.SaveSettingsAsync();

            // Assert
            result.Should().BeTrue();
        }

        [Fact]
        public async Task UpdateSettingsAsync_ShouldUpdateCurrentSettings()
        {
            // Arrange
            await _settingsService.LoadSettingsAsync();
            var newSettings = new UserSettings
            {
                Theme = "Dark",
                Language = "en-US",
                FontSize = 18.0,
                AutoSaveEnabled = false
            };

            // Act
            var result = await _settingsService.UpdateSettingsAsync(newSettings);

            // Assert
            result.Should().BeTrue();
            _settingsService.CurrentSettings.Theme.Should().Be("Dark");
            _settingsService.CurrentSettings.Language.Should().Be("en-US");
            _settingsService.CurrentSettings.FontSize.Should().Be(18.0);
            _settingsService.CurrentSettings.AutoSaveEnabled.Should().BeFalse();
        }

        [Fact]
        public async Task UpdateSettingsAsync_ShouldReturnFalse_WhenNullSettings()
        {
            // Act
            var result = await _settingsService.UpdateSettingsAsync(null!);

            // Assert
            result.Should().BeFalse();
        }

        [Fact]
        public async Task UpdateSettingAsync_ShouldUpdateSpecificProperty()
        {
            // Arrange
            await _settingsService.LoadSettingsAsync();
            var originalTheme = _settingsService.CurrentSettings.Theme;

            // Act
            var result = await _settingsService.UpdateSettingAsync("Theme", "Dark");

            // Assert
            result.Should().BeTrue();
            _settingsService.CurrentSettings.Theme.Should().Be("Dark");
            _settingsService.CurrentSettings.Theme.Should().NotBe(originalTheme);
        }

        [Fact]
        public async Task UpdateSettingAsync_ShouldReturnFalse_WhenInvalidProperty()
        {
            // Arrange
            await _settingsService.LoadSettingsAsync();

            // Act
            var result = await _settingsService.UpdateSettingAsync("NonExistentProperty", "Value");

            // Assert
            result.Should().BeFalse();
        }

        [Fact]
        public async Task ResetToDefaultsAsync_ShouldResetAllSettings()
        {
            // Arrange
            await _settingsService.LoadSettingsAsync();
            _settingsService.CurrentSettings.Theme = "Dark";
            _settingsService.CurrentSettings.FontSize = 20.0;
            _settingsService.CurrentSettings.AutoSaveEnabled = false;

            // Act
            var result = await _settingsService.ResetToDefaultsAsync();

            // Assert
            result.Should().BeTrue();
            _settingsService.CurrentSettings.Theme.Should().Be("Light");
            _settingsService.CurrentSettings.FontSize.Should().Be(12.0);
            _settingsService.CurrentSettings.AutoSaveEnabled.Should().BeTrue();
        }

        [Fact]
        public async Task ExportSettingsAsync_ShouldCreateFile_WhenValidPath()
        {
            // Arrange
            await _settingsService.LoadSettingsAsync();
            var exportPath = GetTempFilePath("exported_settings.json");

            // Act
            var result = await _settingsService.ExportSettingsAsync(exportPath);

            // Assert
            result.Should().BeTrue();
            File.Exists(exportPath).Should().BeTrue();
            
            var exportedContent = await File.ReadAllTextAsync(exportPath);
            exportedContent.Should().NotBeEmpty();
            exportedContent.Should().Contain("theme");
        }

        [Fact]
        public async Task ExportSettingsAsync_ShouldReturnFalse_WhenInvalidPath()
        {
            // Arrange
            await _settingsService.LoadSettingsAsync();

            // Act
            var result = await _settingsService.ExportSettingsAsync("");

            // Assert
            result.Should().BeFalse();
        }

        [Fact]
        public async Task ImportSettingsAsync_ShouldLoadSettings_WhenValidFile()
        {
            // Arrange
            var importSettings = """
                {
                  "theme": "Dark",
                  "language": "en-US",
                  "fontSize": 16.0,
                  "autoSaveEnabled": false,
                  "showGridLines": false,
                  "version": "1.0"
                }
                """;

            var importPath = CreateTempFile("import_settings.json", importSettings);

            // Act
            var result = await _settingsService.ImportSettingsAsync(importPath);

            // Assert
            result.Should().BeTrue();
            _settingsService.CurrentSettings.Theme.Should().Be("Dark");
            _settingsService.CurrentSettings.Language.Should().Be("en-US");
            _settingsService.CurrentSettings.FontSize.Should().Be(16.0);
            _settingsService.CurrentSettings.AutoSaveEnabled.Should().BeFalse();
            _settingsService.CurrentSettings.ShowGridLines.Should().BeFalse();
        }

        [Fact]
        public async Task ImportSettingsAsync_ShouldReturnFalse_WhenFileNotExists()
        {
            // Arrange
            var nonExistentPath = GetTempFilePath("non_existent.json");

            // Act
            var result = await _settingsService.ImportSettingsAsync(nonExistentPath);

            // Assert
            result.Should().BeFalse();
        }

        [Fact]
        public async Task ImportSettingsAsync_ShouldReturnFalse_WhenInvalidJson()
        {
            // Arrange
            var invalidJson = "{ invalid json content }";
            var invalidPath = CreateTempFile("invalid_settings.json", invalidJson);

            // Act
            var result = await _settingsService.ImportSettingsAsync(invalidPath);

            // Assert
            result.Should().BeFalse();
        }

        [Fact]
        public void SettingsFileExists_ShouldReturnTrue_WhenFileExists()
        {
            // Act
            var exists = _settingsService.SettingsFileExists();

            // Assert
            // Bu test environment'a bağlı olarak değişebilir
            // Genellikle ilk çalıştırmada false, sonrasında true olur
            exists.Should().BeOfType<bool>();
        }

        [Fact]
        public void GetSettingsFilePath_ShouldReturnValidPath()
        {
            // Act
            var path = _settingsService.GetSettingsFilePath();

            // Assert
            path.Should().NotBeNullOrEmpty();
            path.Should().EndWith("settings.json");
            path.Should().Contain("DonDonat");
        }

        [Fact]
        public async Task AddRecentFileAsync_ShouldAddFileToList()
        {
            // Arrange
            await _settingsService.LoadSettingsAsync();
            var filePath = @"C:\Test\TestFile.dwg";

            // Act
            var result = await _settingsService.AddRecentFileAsync(filePath);

            // Assert
            result.Should().BeTrue();
            _settingsService.CurrentSettings.RecentFiles.Should().Contain(filePath);
        }

        [Fact]
        public async Task RemoveRecentFileAsync_ShouldRemoveFileFromList()
        {
            // Arrange
            await _settingsService.LoadSettingsAsync();
            var filePath = @"C:\Test\TestFile.dwg";
            await _settingsService.AddRecentFileAsync(filePath);

            // Act
            var result = await _settingsService.RemoveRecentFileAsync(filePath);

            // Assert
            result.Should().BeTrue();
            _settingsService.CurrentSettings.RecentFiles.Should().NotContain(filePath);
        }

        [Fact]
        public async Task ClearRecentFilesAsync_ShouldClearAllFiles()
        {
            // Arrange
            await _settingsService.LoadSettingsAsync();
            await _settingsService.AddRecentFileAsync(@"C:\Test\File1.dwg");
            await _settingsService.AddRecentFileAsync(@"C:\Test\File2.dwg");

            // Act
            var result = await _settingsService.ClearRecentFilesAsync();

            // Assert
            result.Should().BeTrue();
            _settingsService.CurrentSettings.RecentFiles.Should().BeEmpty();
        }

        [Fact]
        public async Task ChangeThemeAsync_ShouldUpdateTheme_WhenValidTheme()
        {
            // Arrange
            await _settingsService.LoadSettingsAsync();

            // Act
            var result = await _settingsService.ChangeThemeAsync("Dark");

            // Assert
            result.Should().BeTrue();
            _settingsService.CurrentSettings.Theme.Should().Be("Dark");
        }

        [Theory]
        [InlineData("InvalidTheme")]
        [InlineData("")]
        [InlineData("light")] // Case sensitive
        public async Task ChangeThemeAsync_ShouldReturnFalse_WhenInvalidTheme(string invalidTheme)
        {
            // Arrange
            await _settingsService.LoadSettingsAsync();

            // Act
            var result = await _settingsService.ChangeThemeAsync(invalidTheme);

            // Assert
            result.Should().BeFalse();
        }

        [Fact]
        public void ValidateSettings_ShouldReturnValid_ForValidSettings()
        {
            // Arrange
            var validSettings = new UserSettings
            {
                Theme = "Light",
                FontSize = 12.0,
                WindowWidth = 1200,
                WindowHeight = 800,
                AutoSaveIntervalMinutes = 5,
                MaxRecentFiles = 10
            };

            // Act
            var (isValid, errorMessages) = _settingsService.ValidateSettings(validSettings);

            // Assert
            isValid.Should().BeTrue();
            errorMessages.Should().BeEmpty();
        }

        [Theory]
        [InlineData("InvalidTheme", 12.0, 1200, 800, 5, 10)]
        [InlineData("Light", 5.0, 1200, 800, 5, 10)] // Font size too small
        [InlineData("Light", 12.0, 200, 800, 5, 10)] // Window width too small
        [InlineData("Light", 12.0, 1200, 100, 5, 10)] // Window height too small
        [InlineData("Light", 12.0, 1200, 800, 0, 10)] // Auto save interval too small
        [InlineData("Light", 12.0, 1200, 800, 5, 0)] // Max recent files too small
        public void ValidateSettings_ShouldReturnInvalid_ForInvalidSettings(
            string theme, double fontSize, double windowWidth, double windowHeight, 
            int autoSaveInterval, int maxRecentFiles)
        {
            // Arrange
            var invalidSettings = new UserSettings
            {
                Theme = theme,
                FontSize = fontSize,
                WindowWidth = windowWidth,
                WindowHeight = windowHeight,
                AutoSaveIntervalMinutes = autoSaveInterval,
                MaxRecentFiles = maxRecentFiles
            };

            // Act
            var (isValid, errorMessages) = _settingsService.ValidateSettings(invalidSettings);

            // Assert
            isValid.Should().BeFalse();
            errorMessages.Should().NotBeEmpty();
        }
    }
}

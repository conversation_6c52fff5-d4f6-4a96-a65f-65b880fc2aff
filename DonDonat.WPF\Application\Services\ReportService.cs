using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using DonDonat.WPF.Application.Interfaces;
using DonDonat.WPF.Domain.Entities;
using DonDonat.WPF.Infrastructure.Exporters;

namespace DonDonat.WPF.Application.Services
{
    /// <summary>
    /// Raporlama servisi implementasyonu
    /// Excel ve PDF export işlemlerini yönetir
    /// </summary>
    public class ReportService : IReportService
    {
        private readonly ExcelExporter _excelExporter;
        private readonly PdfExporter _pdfExporter;

        public ReportService()
        {
            _excelExporter = new ExcelExporter();
            _pdfExporter = new PdfExporter();
        }

        /// <summary>
        /// Donatı listesini Excel formatında export eder
        /// </summary>
        public async Task<bool> ExportToExcelAsync(List<Donati> donatilar, ProjeBilgileri? projeBilgileri, string filePath)
        {
            try
            {
                // Validation kontrolü
                var (isValid, errorMessages) = ValidateExportData(donatilar);
                if (!isValid)
                {
                    throw new ArgumentException($"Export verileri geçersiz: {string.Join(", ", errorMessages)}");
                }

                // Dosya yolu kontrolü
                if (!IsValidFilePath(filePath))
                {
                    throw new ArgumentException($"Geçersiz dosya yolu: {filePath}");
                }

                // Dizin yoksa oluştur
                var directory = Path.GetDirectoryName(filePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // Excel export işlemi
                var result = await _excelExporter.CreateDonatiReportAsync(donatilar, projeBilgileri, filePath);
                
                if (result)
                {
                    System.Diagnostics.Debug.WriteLine($"Excel raporu başarıyla oluşturuldu: {filePath}");
                }

                return result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Excel export hatası: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Donatı listesini PDF formatında export eder
        /// </summary>
        public async Task<bool> ExportToPdfAsync(List<Donati> donatilar, ProjeBilgileri? projeBilgileri, string filePath)
        {
            try
            {
                // Validation kontrolü
                var (isValid, errorMessages) = ValidateExportData(donatilar);
                if (!isValid)
                {
                    throw new ArgumentException($"Export verileri geçersiz: {string.Join(", ", errorMessages)}");
                }

                // Dosya yolu kontrolü
                if (!IsValidFilePath(filePath))
                {
                    throw new ArgumentException($"Geçersiz dosya yolu: {filePath}");
                }

                // Dizin yoksa oluştur
                var directory = Path.GetDirectoryName(filePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // PDF export işlemi
                var result = await _pdfExporter.CreateDonatiReportAsync(donatilar, projeBilgileri, filePath);
                
                if (result)
                {
                    System.Diagnostics.Debug.WriteLine($"PDF raporu başarıyla oluşturuldu: {filePath}");
                }

                return result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"PDF export hatası: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Excel export için varsayılan dosya yolunu oluşturur
        /// </summary>
        public string GetDefaultExcelFilePath(string projeAdi = "")
        {
            var documentsPath = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
            var donDonatPath = Path.Combine(documentsPath, "DonDonat", "Raporlar");

            // Dizin yoksa oluştur
            if (!Directory.Exists(donDonatPath))
            {
                Directory.CreateDirectory(donDonatPath);
            }

            var fileName = string.IsNullOrWhiteSpace(projeAdi) 
                ? $"Donati_Raporu_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx"
                : $"{SanitizeFileName(projeAdi)}_Donati_Raporu_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";

            return Path.Combine(donDonatPath, fileName);
        }

        /// <summary>
        /// PDF export için varsayılan dosya yolunu oluşturur
        /// </summary>
        public string GetDefaultPdfFilePath(string projeAdi = "")
        {
            var documentsPath = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
            var donDonatPath = Path.Combine(documentsPath, "DonDonat", "Raporlar");

            // Dizin yoksa oluştur
            if (!Directory.Exists(donDonatPath))
            {
                Directory.CreateDirectory(donDonatPath);
            }

            var fileName = string.IsNullOrWhiteSpace(projeAdi) 
                ? $"Donati_Raporu_{DateTime.Now:yyyyMMdd_HHmmss}.pdf"
                : $"{SanitizeFileName(projeAdi)}_Donati_Raporu_{DateTime.Now:yyyyMMdd_HHmmss}.pdf";

            return Path.Combine(donDonatPath, fileName);
        }

        /// <summary>
        /// Export işlemi için donatı listesini doğrular
        /// </summary>
        public (bool IsValid, string[] ErrorMessages) ValidateExportData(List<Donati> donatilar)
        {
            var errors = new List<string>();

            if (donatilar == null)
            {
                errors.Add("Donatı listesi null olamaz");
                return (false, errors.ToArray());
            }

            if (!donatilar.Any())
            {
                errors.Add("Export edilecek donatı bulunamadı");
                return (false, errors.ToArray());
            }

            // Her donatıyı kontrol et
            for (int i = 0; i < donatilar.Count; i++)
            {
                var donati = donatilar[i];
                
                if (donati == null)
                {
                    errors.Add($"Donatı {i + 1} null olamaz");
                    continue;
                }

                if (donati.Cap <= 0)
                    errors.Add($"Donatı {i + 1}: Geçersiz çap değeri ({donati.Cap})");

                if (donati.Boy <= 0)
                    errors.Add($"Donatı {i + 1}: Geçersiz boy değeri ({donati.Boy})");

                if (donati.Adet <= 0)
                    errors.Add($"Donatı {i + 1}: Geçersiz adet değeri ({donati.Adet})");

                if (string.IsNullOrWhiteSpace(donati.Tur))
                    errors.Add($"Donatı {i + 1}: Donatı türü boş olamaz");
            }

            return (errors.Count == 0, errors.ToArray());
        }

        /// <summary>
        /// Export işlemi için rapor özeti oluşturur
        /// </summary>
        public ReportSummary CreateReportSummary(List<Donati> donatilar)
        {
            if (donatilar == null || !donatilar.Any())
            {
                return new ReportSummary();
            }

            var capDagilimi = donatilar.GroupBy(d => d.Cap)
                .ToDictionary(g => g.Key, g => g.Sum(d => d.Adet));

            var yapiElemaniDagilimi = donatilar.GroupBy(d => d.YapiElemani)
                .ToDictionary(g => g.Key, g => g.Sum(d => d.Adet));

            var enCokKullanilanCap = capDagilimi.OrderByDescending(kvp => kvp.Value)
                .FirstOrDefault().Key;

            return new ReportSummary
            {
                ToplamAdet = donatilar.Sum(d => d.Adet),
                ToplamUzunluk = donatilar.Sum(d => d.ToplamUzunluk),
                ToplamAgirlik = donatilar.Sum(d => d.ToplamAgirlik),
                FarkliCapSayisi = donatilar.Select(d => d.Cap).Distinct().Count(),
                FarkliYapiElemaniSayisi = donatilar.Select(d => d.YapiElemani).Distinct().Count(),
                EnCokKullanilanCap = enCokKullanilanCap,
                CapDagilimi = capDagilimi,
                YapiElemaniDagilimi = yapiElemaniDagilimi,
                RaporTarihi = DateTime.Now
            };
        }

        /// <summary>
        /// Dosya yolunun geçerli olup olmadığını kontrol eder
        /// </summary>
        public bool IsValidFilePath(string filePath)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(filePath))
                    return false;

                // Geçersiz karakterleri kontrol et
                var invalidChars = Path.GetInvalidPathChars();
                if (filePath.Any(c => invalidChars.Contains(c)))
                    return false;

                // Dosya adını kontrol et
                var fileName = Path.GetFileName(filePath);
                if (string.IsNullOrWhiteSpace(fileName))
                    return false;

                var invalidFileNameChars = Path.GetInvalidFileNameChars();
                if (fileName.Any(c => invalidFileNameChars.Contains(c)))
                    return false;

                // Uzantıyı kontrol et
                var extension = Path.GetExtension(filePath).ToLowerInvariant();
                if (extension != ".xlsx" && extension != ".pdf")
                    return false;

                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Dosya yazma izni olup olmadığını kontrol eder
        /// </summary>
        public bool HasWritePermission(string filePath)
        {
            try
            {
                var directory = Path.GetDirectoryName(filePath);
                if (string.IsNullOrEmpty(directory))
                    return false;

                // Dizin yoksa oluşturmayı dene
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // Test dosyası oluşturmayı dene
                var testFile = Path.Combine(directory, $"test_{Guid.NewGuid()}.tmp");
                File.WriteAllText(testFile, "test");
                File.Delete(testFile);

                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Dosya adını temizler (geçersiz karakterleri kaldırır)
        /// </summary>
        private string SanitizeFileName(string fileName)
        {
            if (string.IsNullOrWhiteSpace(fileName))
                return "Proje";

            var invalidChars = Path.GetInvalidFileNameChars();
            var sanitized = new string(fileName.Where(c => !invalidChars.Contains(c)).ToArray());
            
            // Boşlukları alt çizgi ile değiştir
            sanitized = sanitized.Replace(' ', '_');
            
            // Çok uzunsa kısalt
            if (sanitized.Length > 50)
                sanitized = sanitized.Substring(0, 50);

            return string.IsNullOrWhiteSpace(sanitized) ? "Proje" : sanitized;
        }
    }
}

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using DonDonat.WPF.Domain.Entities;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using System.Drawing;

namespace DonDonat.WPF.Infrastructure.Exporters
{
    /// <summary>
    /// Excel dosyası oluşturma servisi
    /// EPPlus kütüphanesi kullanılarak Excel raporları oluşturur
    /// </summary>
    public class ExcelExporter
    {
        static ExcelExporter()
        {
            // EPPlus lisans ayarı (non-commercial use)
            ExcelPackage.License = OfficeOpenXml.LicenseContext.NonCommercial;
        }

        public ExcelExporter()
        {
        }

        /// <summary>
        /// Donatı listesinden Excel raporu oluşturur
        /// </summary>
        /// <param name="donatilar">Donatı listesi</param>
        /// <param name="projeBilgileri">Proje bilgileri</param>
        /// <param name="filePath">Kaydedilecek dosya yolu</param>
        /// <returns>İşlem başarılı mı</returns>
        public async Task<bool> CreateDonatiReportAsync(
            List<Donati> donatilar, 
            ProjeBilgileri? projeBilgileri, 
            string filePath)
        {
            try
            {
                using var package = new ExcelPackage();
                var worksheet = package.Workbook.Worksheets.Add("Donatı Metraj Raporu");

                // Sayfa ayarları
                SetupWorksheetSettings(worksheet);

                // Başlık ve proje bilgileri
                await AddHeaderSectionAsync(worksheet, projeBilgileri);

                // Donatı tablosu
                await AddDonatiTableAsync(worksheet, donatilar);

                // Özet bilgiler
                await AddSummarySection(worksheet, donatilar);

                // Dosyayı kaydet
                var fileInfo = new FileInfo(filePath);
                await package.SaveAsAsync(fileInfo);

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Excel export hatası: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Çalışma sayfası ayarlarını yapar
        /// </summary>
        private void SetupWorksheetSettings(ExcelWorksheet worksheet)
        {
            // Sayfa yönü ve boyutu
            worksheet.PrinterSettings.Orientation = eOrientation.Landscape;
            worksheet.PrinterSettings.PaperSize = ePaperSize.A4;

            // Kenar boşlukları
            worksheet.PrinterSettings.LeftMargin = 0.5;
            worksheet.PrinterSettings.RightMargin = 0.5;
            worksheet.PrinterSettings.TopMargin = 0.75;
            worksheet.PrinterSettings.BottomMargin = 0.75;

            // Başlık satırlarını her sayfada tekrarla
            worksheet.PrinterSettings.RepeatRows = new ExcelAddress("1:8");

            // Izgara çizgilerini yazdır
            worksheet.PrinterSettings.ShowGridLines = true;
        }

        /// <summary>
        /// Başlık ve proje bilgileri bölümünü ekler
        /// </summary>
        private Task AddHeaderSectionAsync(ExcelWorksheet worksheet, ProjeBilgileri? projeBilgileri)
        {
            // Ana başlık
            worksheet.Cells["A1:I1"].Merge = true;
            worksheet.Cells["A1"].Value = "BETONARME DONATI METRAJ RAPORU";
            worksheet.Cells["A1"].Style.Font.Size = 18;
            worksheet.Cells["A1"].Style.Font.Bold = true;
            worksheet.Cells["A1"].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
            worksheet.Cells["A1"].Style.Fill.PatternType = ExcelFillStyle.Solid;
            worksheet.Cells["A1"].Style.Fill.BackgroundColor.SetColor(Color.FromArgb(79, 129, 189));
            worksheet.Cells["A1"].Style.Font.Color.SetColor(Color.White);
            worksheet.Row(1).Height = 30;

            // Proje bilgileri
            if (projeBilgileri != null)
            {
                worksheet.Cells["A3"].Value = "Proje Adı:";
                worksheet.Cells["B3"].Value = projeBilgileri.ProjeAdi;
                worksheet.Cells["A3"].Style.Font.Bold = true;

                worksheet.Cells["A4"].Value = "Proje Lokasyonu:";
                worksheet.Cells["B4"].Value = projeBilgileri.ProjeLokasyonu;
                worksheet.Cells["A4"].Style.Font.Bold = true;

                worksheet.Cells["A5"].Value = "Müteahhit Firma:";
                worksheet.Cells["B5"].Value = projeBilgileri.MuteahhitFirma;
                worksheet.Cells["A5"].Style.Font.Bold = true;

                worksheet.Cells["F3"].Value = "Temel Türü:";
                worksheet.Cells["G3"].Value = projeBilgileri.TemelTuru;
                worksheet.Cells["F3"].Style.Font.Bold = true;

                worksheet.Cells["F4"].Value = "Kat Sayısı:";
                worksheet.Cells["G4"].Value = $"{projeBilgileri.KatSayisi} + {projeBilgileri.BodrumKatSayisi} Bodrum";
                worksheet.Cells["F4"].Style.Font.Bold = true;

                worksheet.Cells["F5"].Value = "Proje Mühendisi:";
                worksheet.Cells["G5"].Value = projeBilgileri.ProjeMuhendisi;
                worksheet.Cells["F5"].Style.Font.Bold = true;
            }

            // Tarih bilgisi
            worksheet.Cells["A6"].Value = "Rapor Tarihi:";
            worksheet.Cells["B6"].Value = DateTime.Now.ToString("dd.MM.yyyy HH:mm");
            worksheet.Cells["A6"].Style.Font.Bold = true;

            // Boş satır
            worksheet.Row(7).Height = 10;

            return Task.CompletedTask;
        }

        /// <summary>
        /// Donatı tablosunu ekler
        /// </summary>
        private Task AddDonatiTableAsync(ExcelWorksheet worksheet, List<Donati> donatilar)
        {
            int startRow = 8;

            // Tablo başlıkları
            var headers = new[]
            {
                "Sıra No", "Çap (mm)", "Boy (m)", "Adet", "Tür", 
                "Yapı Elemanı", "Konum", "Sınıf", "Toplam Uzunluk (m)", "Ağırlık (kg)"
            };

            for (int i = 0; i < headers.Length; i++)
            {
                var cell = worksheet.Cells[startRow, i + 1];
                cell.Value = headers[i];
                cell.Style.Font.Bold = true;
                cell.Style.Fill.PatternType = ExcelFillStyle.Solid;
                cell.Style.Fill.BackgroundColor.SetColor(Color.FromArgb(217, 217, 217));
                cell.Style.Border.BorderAround(ExcelBorderStyle.Thin);
                cell.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                cell.Style.VerticalAlignment = ExcelVerticalAlignment.Center;
            }

            worksheet.Row(startRow).Height = 25;

            // Donatı verileri
            int currentRow = startRow + 1;
            int siraNo = 1;

            foreach (var donati in donatilar.OrderBy(d => d.YapiElemani).ThenBy(d => d.Cap))
            {
                worksheet.Cells[currentRow, 1].Value = siraNo++;
                worksheet.Cells[currentRow, 2].Value = donati.Cap;
                worksheet.Cells[currentRow, 3].Value = donati.Boy;
                worksheet.Cells[currentRow, 4].Value = donati.Adet;
                worksheet.Cells[currentRow, 5].Value = donati.Tur;
                worksheet.Cells[currentRow, 6].Value = donati.YapiElemani;
                worksheet.Cells[currentRow, 7].Value = donati.Konum;
                worksheet.Cells[currentRow, 8].Value = donati.DonatiSinifi;
                worksheet.Cells[currentRow, 9].Value = donati.ToplamUzunluk;
                worksheet.Cells[currentRow, 10].Value = donati.ToplamAgirlik;

                // Satır formatlaması
                for (int col = 1; col <= 10; col++)
                {
                    var cell = worksheet.Cells[currentRow, col];
                    cell.Style.Border.BorderAround(ExcelBorderStyle.Thin);
                    
                    // Sayısal değerler için hizalama
                    if (col == 2 || col == 3 || col == 4 || col == 9 || col == 10)
                    {
                        cell.Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
                        
                        // Ondalık formatı
                        if (col == 3 || col == 9 || col == 10)
                        {
                            cell.Style.Numberformat.Format = "#,##0.00";
                        }
                    }
                    else
                    {
                        cell.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                    }
                }

                // Alternatif satır rengi
                if (currentRow % 2 == 0)
                {
                    for (int col = 1; col <= 10; col++)
                    {
                        worksheet.Cells[currentRow, col].Style.Fill.PatternType = ExcelFillStyle.Solid;
                        worksheet.Cells[currentRow, col].Style.Fill.BackgroundColor.SetColor(Color.FromArgb(242, 242, 242));
                    }
                }

                currentRow++;
            }

            // Kolon genişlikleri
            worksheet.Column(1).Width = 8;   // Sıra No
            worksheet.Column(2).Width = 10;  // Çap
            worksheet.Column(3).Width = 10;  // Boy
            worksheet.Column(4).Width = 8;   // Adet
            worksheet.Column(5).Width = 12;  // Tür
            worksheet.Column(6).Width = 15;  // Yapı Elemanı
            worksheet.Column(7).Width = 10;  // Konum
            worksheet.Column(8).Width = 8;   // Sınıf
            worksheet.Column(9).Width = 15;  // Toplam Uzunluk
            worksheet.Column(10).Width = 12; // Ağırlık

            return Task.CompletedTask;
        }

        /// <summary>
        /// Özet bilgiler bölümünü ekler
        /// </summary>
        private Task AddSummarySection(ExcelWorksheet worksheet, List<Donati> donatilar)
        {
            // Son satırı bul
            int lastRow = worksheet.Dimension?.End.Row ?? 8;
            int summaryStartRow = lastRow + 3;

            // Özet başlığı
            worksheet.Cells[summaryStartRow, 1, summaryStartRow, 4].Merge = true;
            worksheet.Cells[summaryStartRow, 1].Value = "ÖZET BİLGİLER";
            worksheet.Cells[summaryStartRow, 1].Style.Font.Size = 14;
            worksheet.Cells[summaryStartRow, 1].Style.Font.Bold = true;
            worksheet.Cells[summaryStartRow, 1].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
            worksheet.Cells[summaryStartRow, 1].Style.Fill.PatternType = ExcelFillStyle.Solid;
            worksheet.Cells[summaryStartRow, 1].Style.Fill.BackgroundColor.SetColor(Color.FromArgb(146, 208, 80));

            summaryStartRow += 2;

            // Toplam değerler
            var toplamAdet = donatilar.Sum(d => d.Adet);
            var toplamUzunluk = donatilar.Sum(d => d.ToplamUzunluk);
            var toplamAgirlik = donatilar.Sum(d => d.ToplamAgirlik);

            worksheet.Cells[summaryStartRow, 1].Value = "Toplam Donatı Adedi:";
            worksheet.Cells[summaryStartRow, 2].Value = toplamAdet;
            worksheet.Cells[summaryStartRow, 1].Style.Font.Bold = true;

            worksheet.Cells[summaryStartRow + 1, 1].Value = "Toplam Uzunluk (m):";
            worksheet.Cells[summaryStartRow + 1, 2].Value = toplamUzunluk;
            worksheet.Cells[summaryStartRow + 1, 2].Style.Numberformat.Format = "#,##0.00";
            worksheet.Cells[summaryStartRow + 1, 1].Style.Font.Bold = true;

            worksheet.Cells[summaryStartRow + 2, 1].Value = "Toplam Ağırlık (kg):";
            worksheet.Cells[summaryStartRow + 2, 2].Value = toplamAgirlik;
            worksheet.Cells[summaryStartRow + 2, 2].Style.Numberformat.Format = "#,##0.00";
            worksheet.Cells[summaryStartRow + 2, 1].Style.Font.Bold = true;

            // Çap bazında özet
            var capOzeti = donatilar.GroupBy(d => d.Cap)
                .Select(g => new
                {
                    Cap = g.Key,
                    Adet = g.Sum(d => d.Adet),
                    Uzunluk = g.Sum(d => d.ToplamUzunluk),
                    Agirlik = g.Sum(d => d.ToplamAgirlik)
                })
                .OrderBy(x => x.Cap)
                .ToList();

            if (capOzeti.Any())
            {
                int capOzetiStartRow = summaryStartRow + 5;

                worksheet.Cells[capOzetiStartRow, 1].Value = "ÇAP BAZINDA ÖZET";
                worksheet.Cells[capOzetiStartRow, 1].Style.Font.Bold = true;
                worksheet.Cells[capOzetiStartRow, 1].Style.Font.Size = 12;

                capOzetiStartRow += 2;

                // Başlıklar
                worksheet.Cells[capOzetiStartRow, 1].Value = "Çap (mm)";
                worksheet.Cells[capOzetiStartRow, 2].Value = "Adet";
                worksheet.Cells[capOzetiStartRow, 3].Value = "Uzunluk (m)";
                worksheet.Cells[capOzetiStartRow, 4].Value = "Ağırlık (kg)";

                for (int i = 1; i <= 4; i++)
                {
                    worksheet.Cells[capOzetiStartRow, i].Style.Font.Bold = true;
                    worksheet.Cells[capOzetiStartRow, i].Style.Fill.PatternType = ExcelFillStyle.Solid;
                    worksheet.Cells[capOzetiStartRow, i].Style.Fill.BackgroundColor.SetColor(Color.FromArgb(217, 217, 217));
                    worksheet.Cells[capOzetiStartRow, i].Style.Border.BorderAround(ExcelBorderStyle.Thin);
                }

                capOzetiStartRow++;

                foreach (var item in capOzeti)
                {
                    worksheet.Cells[capOzetiStartRow, 1].Value = $"Ø{item.Cap}";
                    worksheet.Cells[capOzetiStartRow, 2].Value = item.Adet;
                    worksheet.Cells[capOzetiStartRow, 3].Value = item.Uzunluk;
                    worksheet.Cells[capOzetiStartRow, 4].Value = item.Agirlik;

                    for (int i = 1; i <= 4; i++)
                    {
                        worksheet.Cells[capOzetiStartRow, i].Style.Border.BorderAround(ExcelBorderStyle.Thin);
                        if (i >= 3)
                        {
                            worksheet.Cells[capOzetiStartRow, i].Style.Numberformat.Format = "#,##0.00";
                        }
                    }

                    capOzetiStartRow++;
                }
            }

            // Alt bilgi
            int footerRow = worksheet.Dimension?.End.Row + 3 ?? summaryStartRow + 10;
            worksheet.Cells[footerRow, 1].Value = $"Bu rapor DonDonat v1.0 tarafından {DateTime.Now:dd.MM.yyyy HH:mm} tarihinde oluşturulmuştur.";
            worksheet.Cells[footerRow, 1].Style.Font.Size = 9;
            worksheet.Cells[footerRow, 1].Style.Font.Italic = true;
            worksheet.Cells[footerRow, 1].Style.Font.Color.SetColor(Color.Gray);

            return Task.CompletedTask;
        }
    }
}

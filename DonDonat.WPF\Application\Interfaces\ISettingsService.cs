using System;
using System.Threading.Tasks;
using DonDonat.WPF.Domain.Entities;

namespace DonDonat.WPF.Application.Interfaces
{
    /// <summary>
    /// Kullanıcı ayarları servisi interface
    /// Ayarların yüklenmesi, kaydedilmesi ve yönetimi için
    /// </summary>
    public interface ISettingsService
    {
        /// <summary>
        /// Mevcut kullanıcı ayarları
        /// </summary>
        UserSettings CurrentSettings { get; }

        /// <summary>
        /// Ayarlar değiştiğinde tetiklenen event
        /// </summary>
        event EventHandler<UserSettings>? SettingsChanged;

        /// <summary>
        /// Ayarları dosyadan yükler
        /// </summary>
        /// <returns>Yükleme işleminin başarılı olup olmadığı</returns>
        Task<bool> LoadSettingsAsync();

        /// <summary>
        /// Ayarları dosyaya kaydeder
        /// </summary>
        /// <returns>Kaydetme işleminin başarılı olup olmadığı</returns>
        Task<bool> SaveSettingsAsync();

        /// <summary>
        /// Ayarları günceller ve kaydeder
        /// </summary>
        /// <param name="settings">Güncellenmiş ayarlar</param>
        /// <returns>İşlemin başarılı olup olmadığı</returns>
        Task<bool> UpdateSettingsAsync(UserSettings settings);

        /// <summary>
        /// Belirli bir ayarı günceller
        /// </summary>
        /// <param name="propertyName">Ayar özellik adı</param>
        /// <param name="value">Yeni değer</param>
        /// <returns>İşlemin başarılı olup olmadığı</returns>
        Task<bool> UpdateSettingAsync(string propertyName, object value);

        /// <summary>
        /// Ayarları varsayılan değerlere sıfırlar
        /// </summary>
        /// <returns>İşlemin başarılı olup olmadığı</returns>
        Task<bool> ResetToDefaultsAsync();

        /// <summary>
        /// Ayarları JSON dosyasına export eder
        /// </summary>
        /// <param name="filePath">Export edilecek dosya yolu</param>
        /// <returns>İşlemin başarılı olup olmadığı</returns>
        Task<bool> ExportSettingsAsync(string filePath);

        /// <summary>
        /// JSON dosyasından ayarları import eder
        /// </summary>
        /// <param name="filePath">Import edilecek dosya yolu</param>
        /// <returns>İşlemin başarılı olup olmadığı</returns>
        Task<bool> ImportSettingsAsync(string filePath);

        /// <summary>
        /// Ayarlar dosyasının var olup olmadığını kontrol eder
        /// </summary>
        /// <returns>Dosya var mı</returns>
        bool SettingsFileExists();

        /// <summary>
        /// Ayarlar dosyasının yolunu döndürür
        /// </summary>
        /// <returns>Dosya yolu</returns>
        string GetSettingsFilePath();

        /// <summary>
        /// Ayarları yedekler
        /// </summary>
        /// <returns>İşlemin başarılı olup olmadığı</returns>
        Task<bool> BackupSettingsAsync();

        /// <summary>
        /// Ayarları yedekten geri yükler
        /// </summary>
        /// <returns>İşlemin başarılı olup olmadığı</returns>
        Task<bool> RestoreSettingsFromBackupAsync();

        /// <summary>
        /// Son dosyalar listesine dosya ekler
        /// </summary>
        /// <param name="filePath">Dosya yolu</param>
        /// <returns>İşlemin başarılı olup olmadığı</returns>
        Task<bool> AddRecentFileAsync(string filePath);

        /// <summary>
        /// Son dosyalar listesinden dosya kaldırır
        /// </summary>
        /// <param name="filePath">Dosya yolu</param>
        /// <returns>İşlemin başarılı olup olmadığı</returns>
        Task<bool> RemoveRecentFileAsync(string filePath);

        /// <summary>
        /// Son dosyalar listesini temizler
        /// </summary>
        /// <returns>İşlemin başarılı olup olmadığı</returns>
        Task<bool> ClearRecentFilesAsync();

        /// <summary>
        /// Pencere boyutunu günceller
        /// </summary>
        /// <param name="width">Genişlik</param>
        /// <param name="height">Yükseklik</param>
        /// <param name="isMaximized">Maksimize durumu</param>
        /// <returns>İşlemin başarılı olup olmadığı</returns>
        Task<bool> UpdateWindowSizeAsync(double width, double height, bool isMaximized);

        /// <summary>
        /// Tema değiştirir
        /// </summary>
        /// <param name="theme">Yeni tema (Light/Dark)</param>
        /// <returns>İşlemin başarılı olup olmadığı</returns>
        Task<bool> ChangeThemeAsync(string theme);

        /// <summary>
        /// Ayarların geçerli olup olmadığını kontrol eder
        /// </summary>
        /// <param name="settings">Kontrol edilecek ayarlar</param>
        /// <returns>Geçerlilik durumu ve hata mesajları</returns>
        (bool IsValid, string[] ErrorMessages) ValidateSettings(UserSettings settings);

        /// <summary>
        /// Ayarları otomatik kaydetme özelliğini başlatır/durdurur
        /// </summary>
        /// <param name="enabled">Etkin mi</param>
        void SetAutoSave(bool enabled);
    }
}

using System;
using System.Threading.Tasks;
using DonDonat.WPF.Domain.Entities;

namespace DonDonat.WPF.Application.Interfaces
{
    /// <summary>
    /// Proje bilgileri servisi için interface
    /// </summary>
    public interface IProjeBilgileriService
    {
        /// <summary>
        /// Proje bilgilerini JSON dosyasına kaydeder
        /// </summary>
        /// <param name="projeBilgileri">Kaydedilecek proje bilgileri</param>
        /// <param name="filePath">Dosya yolu (opsiyonel, null ise varsayılan konum kullanılır)</param>
        /// <returns>Kaydetme işleminin başarılı olup olmadığı</returns>
        Task<bool> SaveProjeBilgileriAsync(ProjeBilgileri projeBilgileri, string? filePath = null);

        /// <summary>
        /// JSON dosyasından proje bilgilerini yükler
        /// </summary>
        /// <param name="filePath">Do<PERSON>a yolu (opsiyonel, null ise varsayılan konum kullanılır)</param>
        /// <returns>Yüklenen proje bilgileri, dosya yoksa null</returns>
        Task<ProjeBilgileri?> LoadProjeBilgileriAsync(string? filePath = null);

        /// <summary>
        /// Proje bilgilerini doğrular
        /// </summary>
        /// <param name="projeBilgileri">Doğrulanacak proje bilgileri</param>
        /// <returns>Doğrulama sonucu ve hata mesajları</returns>
        (bool IsValid, string[] ErrorMessages) ValidateProjeBilgileri(ProjeBilgileri projeBilgileri);

        /// <summary>
        /// Varsayılan proje bilgileri oluşturur
        /// </summary>
        /// <returns>Varsayılan değerlerle dolu proje bilgileri</returns>
        ProjeBilgileri CreateDefaultProjeBilgileri();

        /// <summary>
        /// Proje bilgilerini JSON string'e dönüştürür
        /// </summary>
        /// <param name="projeBilgileri">Dönüştürülecek proje bilgileri</param>
        /// <returns>JSON string</returns>
        string SerializeToJson(ProjeBilgileri projeBilgileri);

        /// <summary>
        /// JSON string'den proje bilgilerini oluşturur
        /// </summary>
        /// <param name="jsonString">JSON string</param>
        /// <returns>Proje bilgileri nesnesi</returns>
        ProjeBilgileri? DeserializeFromJson(string jsonString);

        /// <summary>
        /// Varsayılan dosya yolunu döndürür
        /// </summary>
        /// <returns>Varsayılan dosya yolu</returns>
        string GetDefaultFilePath();

        /// <summary>
        /// Dosya var mı kontrol eder
        /// </summary>
        /// <param name="filePath">Kontrol edilecek dosya yolu</param>
        /// <returns>Dosya varsa true</returns>
        bool FileExists(string filePath);
    }
}

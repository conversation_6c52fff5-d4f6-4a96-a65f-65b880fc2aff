using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using DonDonat.WPF.Application.Interfaces;
using DonDonat.WPF.Domain.Entities;
using Microsoft.Win32;

namespace DonDonat.WPF.Presentation.ViewModels
{
    /// <summary>
    /// Ayarlar penceresi için ViewModel
    /// Kullanıcı ayarlarını yönetir
    /// </summary>
    public class SettingsViewModel : INotifyPropertyChanged
    {
        #region Private Fields

        private readonly ISettingsService _settingsService;
        private UserSettings _settings;
        private bool _isLoading;
        private string _statusMessage = "";
        private bool _hasUnsavedChanges;

        #endregion

        #region Constructor

        public SettingsViewModel(ISettingsService settingsService)
        {
            _settingsService = settingsService ?? throw new ArgumentNullException(nameof(settingsService));
            _settings = new UserSettings();

            // Commands
            SaveCommand = new RelayCommand(async () => await SaveSettingsAsync(), CanSave);
            CancelCommand = new RelayCommand(Cancel);
            ResetToDefaultsCommand = new RelayCommand(async () => await ResetToDefaultsAsync(), CanResetToDefaults);
            ExportSettingsCommand = new RelayCommand(async () => await ExportSettingsAsync());
            ImportSettingsCommand = new RelayCommand(async () => await ImportSettingsAsync());
            BrowseDefaultFolderCommand = new RelayCommand(BrowseDefaultFolder);
            BrowseBackupFolderCommand = new RelayCommand(BrowseBackupFolder);
            ClearRecentFilesCommand = new RelayCommand(async () => await ClearRecentFilesAsync());
            TestNotificationCommand = new RelayCommand(TestNotification);

            // Ayarları yükle
            LoadSettingsAsync();

            // Property changed event'ini dinle
            PropertyChanged += OnPropertyChanged;
        }

        #endregion

        #region Public Properties

        /// <summary>
        /// Mevcut ayarlar
        /// </summary>
        public UserSettings Settings
        {
            get => _settings;
            set
            {
                _settings = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(AvailableThemes));
                OnPropertyChanged(nameof(AvailableLanguages));
                OnPropertyChanged(nameof(AvailableFontFamilies));
                OnPropertyChanged(nameof(AvailableExportFormats));
                OnPropertyChanged(nameof(RecentFilesList));
            }
        }

        /// <summary>
        /// Yükleme durumu
        /// </summary>
        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                _isLoading = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Durum mesajı
        /// </summary>
        public string StatusMessage
        {
            get => _statusMessage;
            set
            {
                _statusMessage = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Kaydedilmemiş değişiklikler var mı
        /// </summary>
        public bool HasUnsavedChanges
        {
            get => _hasUnsavedChanges;
            set
            {
                _hasUnsavedChanges = value;
                OnPropertyChanged();
                ((RelayCommand)SaveCommand).RaiseCanExecuteChanged();
            }
        }

        /// <summary>
        /// Mevcut temalar
        /// </summary>
        public ObservableCollection<string> AvailableThemes { get; } = new()
        {
            "Light",
            "Dark"
        };

        /// <summary>
        /// Mevcut diller
        /// </summary>
        public ObservableCollection<string> AvailableLanguages { get; } = new()
        {
            "tr-TR",
            "en-US"
        };

        /// <summary>
        /// Mevcut font aileleri
        /// </summary>
        public ObservableCollection<string> AvailableFontFamilies { get; } = new()
        {
            "Segoe UI",
            "Arial",
            "Calibri",
            "Times New Roman",
            "Verdana",
            "Tahoma"
        };

        /// <summary>
        /// Mevcut export formatları
        /// </summary>
        public ObservableCollection<string> AvailableExportFormats { get; } = new()
        {
            "Excel",
            "PDF"
        };

        /// <summary>
        /// Son dosyalar listesi
        /// </summary>
        public ObservableCollection<string> RecentFilesList
        {
            get
            {
                var list = new ObservableCollection<string>();
                foreach (var file in Settings.RecentFiles)
                {
                    list.Add(file);
                }
                return list;
            }
        }

        /// <summary>
        /// Font boyutu seçenekleri
        /// </summary>
        public ObservableCollection<double> FontSizeOptions { get; } = new()
        {
            8, 9, 10, 11, 12, 14, 16, 18, 20, 22, 24, 26, 28, 36, 48, 72
        };

        /// <summary>
        /// Otomatik kaydetme aralığı seçenekleri (dakika)
        /// </summary>
        public ObservableCollection<int> AutoSaveIntervalOptions { get; } = new()
        {
            1, 2, 5, 10, 15, 30, 60
        };

        #endregion

        #region Commands

        public ICommand SaveCommand { get; }
        public ICommand CancelCommand { get; }
        public ICommand ResetToDefaultsCommand { get; }
        public ICommand ExportSettingsCommand { get; }
        public ICommand ImportSettingsCommand { get; }
        public ICommand BrowseDefaultFolderCommand { get; }
        public ICommand BrowseBackupFolderCommand { get; }
        public ICommand ClearRecentFilesCommand { get; }
        public ICommand TestNotificationCommand { get; }

        #endregion

        #region Command Methods

        /// <summary>
        /// Ayarları kaydeder
        /// </summary>
        private async Task SaveSettingsAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "Ayarlar kaydediliyor...";

                var result = await _settingsService.UpdateSettingsAsync(Settings);
                
                if (result)
                {
                    HasUnsavedChanges = false;
                    StatusMessage = "Ayarlar başarıyla kaydedildi.";
                    
                    MessageBox.Show("Ayarlar başarıyla kaydedildi.", 
                        "Başarılı", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    StatusMessage = "Ayarlar kaydedilemedi.";
                    MessageBox.Show("Ayarlar kaydedilirken bir hata oluştu.", 
                        "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Hata: {ex.Message}";
                MessageBox.Show($"Ayarlar kaydedilirken hata oluştu:\n{ex.Message}", 
                    "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// Değişiklikleri iptal eder
        /// </summary>
        private void Cancel()
        {
            if (HasUnsavedChanges)
            {
                var result = MessageBox.Show(
                    "Kaydedilmemiş değişiklikler var. Çıkmak istediğinizden emin misiniz?",
                    "Onay", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.No)
                    return;
            }

            // Ayarları yeniden yükle
            LoadSettingsAsync();
        }

        /// <summary>
        /// Ayarları varsayılan değerlere sıfırlar
        /// </summary>
        private async Task ResetToDefaultsAsync()
        {
            var result = MessageBox.Show(
                "Tüm ayarları varsayılan değerlere sıfırlamak istediğinizden emin misiniz?",
                "Onay", MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    IsLoading = true;
                    StatusMessage = "Ayarlar sıfırlanıyor...";

                    var resetResult = await _settingsService.ResetToDefaultsAsync();
                    
                    if (resetResult)
                    {
                        await LoadSettingsAsync();
                        StatusMessage = "Ayarlar varsayılan değerlere sıfırlandı.";
                        MessageBox.Show("Ayarlar varsayılan değerlere sıfırlandı.", 
                            "Başarılı", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        StatusMessage = "Ayarlar sıfırlanamadı.";
                        MessageBox.Show("Ayarlar sıfırlanırken bir hata oluştu.", 
                            "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                catch (Exception ex)
                {
                    StatusMessage = $"Hata: {ex.Message}";
                    MessageBox.Show($"Ayarlar sıfırlanırken hata oluştu:\n{ex.Message}", 
                        "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
                }
                finally
                {
                    IsLoading = false;
                }
            }
        }

        /// <summary>
        /// Ayarları export eder
        /// </summary>
        private async Task ExportSettingsAsync()
        {
            try
            {
                var saveFileDialog = new SaveFileDialog
                {
                    Title = "Ayarları Export Et",
                    Filter = "JSON Dosyaları (*.json)|*.json|Tüm Dosyalar (*.*)|*.*",
                    DefaultExt = "json",
                    FileName = $"DonDonat_Settings_{DateTime.Now:yyyyMMdd_HHmmss}.json"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    IsLoading = true;
                    StatusMessage = "Ayarlar export ediliyor...";

                    var result = await _settingsService.ExportSettingsAsync(saveFileDialog.FileName);
                    
                    if (result)
                    {
                        StatusMessage = "Ayarlar başarıyla export edildi.";
                        MessageBox.Show($"Ayarlar başarıyla export edildi:\n{saveFileDialog.FileName}", 
                            "Başarılı", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        StatusMessage = "Ayarlar export edilemedi.";
                        MessageBox.Show("Ayarlar export edilirken bir hata oluştu.", 
                            "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Hata: {ex.Message}";
                MessageBox.Show($"Ayarlar export edilirken hata oluştu:\n{ex.Message}", 
                    "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// Ayarları import eder
        /// </summary>
        private async Task ImportSettingsAsync()
        {
            try
            {
                var openFileDialog = new OpenFileDialog
                {
                    Title = "Ayarları Import Et",
                    Filter = "JSON Dosyaları (*.json)|*.json|Tüm Dosyalar (*.*)|*.*",
                    DefaultExt = "json"
                };

                if (openFileDialog.ShowDialog() == true)
                {
                    IsLoading = true;
                    StatusMessage = "Ayarlar import ediliyor...";

                    var result = await _settingsService.ImportSettingsAsync(openFileDialog.FileName);
                    
                    if (result)
                    {
                        await LoadSettingsAsync();
                        StatusMessage = "Ayarlar başarıyla import edildi.";
                        MessageBox.Show("Ayarlar başarıyla import edildi.", 
                            "Başarılı", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        StatusMessage = "Ayarlar import edilemedi.";
                        MessageBox.Show("Ayarlar import edilirken bir hata oluştu.", 
                            "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Hata: {ex.Message}";
                MessageBox.Show($"Ayarlar import edilirken hata oluştu:\n{ex.Message}", 
                    "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// Varsayılan klasör seçer
        /// </summary>
        private void BrowseDefaultFolder()
        {
            var dialog = new OpenFileDialog
            {
                Title = "Varsayılan Klasör Seç",
                CheckFileExists = false,
                CheckPathExists = true,
                FileName = "Klasör Seç"
            };

            if (dialog.ShowDialog() == true)
            {
                Settings.DefaultFolderPath = Path.GetDirectoryName(dialog.FileName) ?? "";
                HasUnsavedChanges = true;
            }
        }

        /// <summary>
        /// Yedek klasörü seçer
        /// </summary>
        private void BrowseBackupFolder()
        {
            var dialog = new OpenFileDialog
            {
                Title = "Yedek Klasörü Seç",
                CheckFileExists = false,
                CheckPathExists = true,
                FileName = "Klasör Seç"
            };

            if (dialog.ShowDialog() == true)
            {
                Settings.BackupFolderPath = Path.GetDirectoryName(dialog.FileName) ?? "";
                HasUnsavedChanges = true;
            }
        }

        /// <summary>
        /// Son dosyalar listesini temizler
        /// </summary>
        private async Task ClearRecentFilesAsync()
        {
            var result = MessageBox.Show(
                "Son dosyalar listesini temizlemek istediğinizden emin misiniz?",
                "Onay", MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    var clearResult = await _settingsService.ClearRecentFilesAsync();
                    
                    if (clearResult)
                    {
                        Settings.ClearRecentFiles();
                        OnPropertyChanged(nameof(RecentFilesList));
                        StatusMessage = "Son dosyalar listesi temizlendi.";
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Son dosyalar listesi temizlenirken hata oluştu:\n{ex.Message}", 
                        "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        /// <summary>
        /// Test bildirimi gösterir
        /// </summary>
        private void TestNotification()
        {
            MessageBox.Show("Bu bir test bildirimidir.", 
                "Test Bildirimi", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        #endregion

        #region Command Can Execute Methods

        private bool CanSave() => HasUnsavedChanges && !IsLoading;
        private bool CanResetToDefaults() => !IsLoading;

        #endregion

        #region Helper Methods

        /// <summary>
        /// Ayarları yükler
        /// </summary>
        private async Task LoadSettingsAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "Ayarlar yükleniyor...";

                await _settingsService.LoadSettingsAsync();
                Settings = _settingsService.CurrentSettings;
                HasUnsavedChanges = false;
                StatusMessage = "Ayarlar yüklendi.";
            }
            catch (Exception ex)
            {
                StatusMessage = $"Hata: {ex.Message}";
                MessageBox.Show($"Ayarlar yüklenirken hata oluştu:\n{ex.Message}", 
                    "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// Property değişikliklerini izler
        /// </summary>
        private void OnPropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName != nameof(HasUnsavedChanges) && 
                e.PropertyName != nameof(IsLoading) && 
                e.PropertyName != nameof(StatusMessage))
            {
                HasUnsavedChanges = true;
            }
        }

        #endregion

        #region INotifyPropertyChanged Implementation

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }
}

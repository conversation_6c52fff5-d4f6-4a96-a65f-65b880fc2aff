<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Condition="'$(VisualStudioVersion)' == '' or '$(VisualStudioVersion)' &lt; '15.0'">
    <VisualStudioVersion>15.0</VisualStudioVersion>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x86">
      <Configuration>Debug</Configuration>
      <Platform>x86</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x86">
      <Configuration>Release</Configuration>
      <Platform>x86</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|ARM64">
      <Configuration>Debug</Configuration>
      <Platform>ARM64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|ARM64">
      <Configuration>Release</Configuration>
      <Platform>ARM64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup>
    <WapProjPath Condition="'$(WapProjPath)'==''">$(MSBuildExtensionsPath)\Microsoft\DesktopBridge\</WapProjPath>
    <PathToXAMLWinRTImplementations>DonDonat.WPF\</PathToXAMLWinRTImplementations>
  </PropertyGroup>
  <Import Project="$(WapProjPath)\Microsoft.DesktopBridge.props" />
  <PropertyGroup>
    <ProjectGuid>6233c7dd-cc67-4cd1-84b2-8d58c4d662ff</ProjectGuid>
    <TargetPlatformVersion>10.0.22621.0</TargetPlatformVersion>
    <TargetPlatformMinVersion>10.0.19041.0</TargetPlatformMinVersion>
    <AssetTargetFallback>net6.0-windows$(TargetPlatformVersion);$(AssetTargetFallback)</AssetTargetFallback>
    <DefaultLanguage>tr-TR</DefaultLanguage>
    <AppxPackageSigningEnabled>false</AppxPackageSigningEnabled>
    <EntryPointProjectUniqueName>..\DonDonat.WPF\DonDonat.WPF.csproj</EntryPointProjectUniqueName>
    <GenerateAppInstallerFile>true</GenerateAppInstallerFile>
    <AppxAutoIncrementPackageRevision>true</AppxAutoIncrementPackageRevision>
    <GenerateTestArtifacts>true</GenerateTestArtifacts>
    <AppxBundlePlatforms>x64</AppxBundlePlatforms>
    <HoursBetweenUpdateChecks>0</HoursBetweenUpdateChecks>
    <AppInstallerUri>https://your-domain.com/dondonat/</AppInstallerUri>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <AppxBundle>Always</AppxBundle>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <AppxBundle>Always</AppxBundle>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x86'">
    <AppxBundle>Always</AppxBundle>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x86'">
    <AppxBundle>Always</AppxBundle>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|ARM64'">
    <AppxBundle>Always</AppxBundle>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|ARM64'">
    <AppxBundle>Always</AppxBundle>
  </PropertyGroup>
  <ItemGroup>
    <AppxManifest Include="Package.appxmanifest">
      <SubType>Designer</SubType>
    </AppxManifest>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Images\SplashScreen.scale-200.png" />
    <Content Include="Images\LockScreenLogo.scale-200.png" />
    <Content Include="Images\Square150x150Logo.scale-200.png" />
    <Content Include="Images\Square44x44Logo.scale-200.png" />
    <Content Include="Images\Square44x44Logo.targetsize-24_altform-unplated.png" />
    <Content Include="Images\StoreLogo.png" />
    <Content Include="Images\Wide310x150Logo.scale-200.png" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\DonDonat.WPF\DonDonat.WPF.csproj">
      <SkipGetTargetFrameworkProperties>False</SkipGetTargetFrameworkProperties>
      <PublishProfile>Properties\PublishProfiles\win-x64.pubxml</PublishProfile>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.WindowsAppSDK" Version="1.4.231008000" />
    <PackageReference Include="Microsoft.Windows.SDK.BuildTools" Version="10.0.22621.2428" />
  </ItemGroup>
  <Import Project="$(WapProjPath)\Microsoft.DesktopBridge.targets" />
</Project>

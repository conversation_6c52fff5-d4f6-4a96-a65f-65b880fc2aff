{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\DonDonat\\DonDonat.WPF\\DonDonat.WPF.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\DonDonat\\DonDonat.WPF\\DonDonat.WPF.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\DonDonat\\DonDonat.WPF\\DonDonat.WPF.csproj", "projectName": "DonDonat.WPF", "projectPath": "C:\\Users\\<USER>\\Desktop\\DonDonat\\DonDonat.WPF\\DonDonat.WPF.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\DonDonat\\DonDonat.WPF\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "dependencies": {"EPPlus": {"target": "Package", "version": "[8.0.5, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.5, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.Extensions.Logging.Console": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.Extensions.Logging.Debug": {"target": "Package", "version": "[9.0.5, )"}, "Serilog.Extensions.Hosting": {"target": "Package", "version": "[8.0.0, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[6.0.0, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[6.0.0, )"}, "iTextSharp.LGPLv2.Core": {"target": "Package", "version": "[3.7.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}}}
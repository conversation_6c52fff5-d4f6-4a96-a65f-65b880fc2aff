{"Logging": {"LogLevel": {"Default": "Debug", "Microsoft": "Information", "Microsoft.Hosting.Lifetime": "Information", "System": "Information", "DonDonat": "Trace"}, "Console": {"IncludeScopes": true, "TimestampFormat": "HH:mm:ss.fff ", "LogToStandardErrorThreshold": "Warning"}, "Debug": {"IncludeScopes": true}, "File": {"Path": "Logs/<PERSON><PERSON><PERSON><PERSON>-<PERSON>-{Date}.log", "MinLevel": "Trace", "RollingInterval": "Hour", "RetainedFileCountLimit": 48, "FileSizeLimitBytes": 52428800, "IncludeScopes": true}}, "AppSettings": {"Environment": "Development", "EnableDebugMode": true, "EnablePerformanceCounters": true, "EnableDetailedLogging": true, "ShowDebugInfo": true, "EnableHotReload": true, "SkipLicenseCheck": true, "EnableTestData": true}, "Database": {"ConnectionString": "Data Source=Data/DonDonat-Dev.db;Cache=Shared", "EnableSensitiveDataLogging": true, "EnableDetailedErrors": true, "LogQueries": true, "EnableMigrations": true}, "FileSettings": {"MaxFileSizeMB": 500, "EnableFileWatcher": true, "TempDirectory": "Temp/Dev", "ExportDirectory": "Exports/Dev", "BackupDirectory": "Backups/Dev"}, "PerformanceSettings": {"MaxConcurrentOperations": 8, "EnableProfiling": true, "DetailedPerformanceLogging": true, "MemoryUsageLogging": true}, "SecuritySettings": {"EnableAuditLogging": true, "SessionTimeoutMinutes": 1440, "MaxLoginAttempts": 10, "BypassSecurity": true}, "UpdateSettings": {"CheckForUpdatesOnStartup": false, "UpdateCheckIntervalHours": 168, "EnableBetaUpdates": true, "AutoDownloadUpdates": false, "UseLocalUpdateServer": true}, "DebugSettings": {"EnableMockData": true, "SimulateSlowOperations": false, "EnableMemoryLeakDetection": true, "LogMemoryUsage": true, "EnableExceptionSimulation": false, "DetailedStackTraces": true}}
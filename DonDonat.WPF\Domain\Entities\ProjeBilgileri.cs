using System;
using System.ComponentModel.DataAnnotations;

namespace DonDonat.WPF.Domain.Entities
{
    /// <summary>
    /// Proje bilgilerini temsil eden domain sınıfı
    /// </summary>
    public class ProjeBilgileri
    {
        /// <summary>
        /// Proje benzersiz kimliği
        /// </summary>
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// Proje adı
        /// </summary>
        [Required(ErrorMessage = "Proje adı zorunludur")]
        [StringLength(200, ErrorMessage = "Proje adı en fazla 200 karakter olabilir")]
        public string ProjeAdi { get; set; } = string.Empty;

        /// <summary>
        /// Temel türü (Radye, Tekil, Sürekli, vb.)
        /// </summary>
        [Required(ErrorMessage = "Temel türü zorunludur")]
        [StringLength(50, ErrorMessage = "Temel türü en fazla 50 karakter olabilir")]
        public string TemelTuru { get; set; } = string.Empty;

        /// <summary>
        /// Temel derinliği (metre cinsinden)
        /// </summary>
        [Range(0.5, 20.0, ErrorMessage = "Temel derinliği 0.5-20 metre arasında olmalıdır")]
        public double TemelDerinligi { get; set; } = 1.5;

        /// <summary>
        /// Kat sayısı (bodrum hariç)
        /// </summary>
        [Required(ErrorMessage = "Kat sayısı zorunludur")]
        [Range(1, 50, ErrorMessage = "Kat sayısı 1-50 arasında olmalıdır")]
        public int KatSayisi { get; set; } = 1;

        /// <summary>
        /// Kat yüksekliği (metre cinsinden)
        /// </summary>
        [Range(2.0, 6.0, ErrorMessage = "Kat yüksekliği 2.0-6.0 metre arasında olmalıdır")]
        public double KatYuksekligi { get; set; } = 3.0;

        /// <summary>
        /// Bodrum var mı?
        /// </summary>
        public bool BodrumVarMi { get; set; } = false;

        /// <summary>
        /// Bodrum kat sayısı
        /// </summary>
        [Range(0, 5, ErrorMessage = "Bodrum kat sayısı 0-5 arasında olmalıdır")]
        public int BodrumKatSayisi { get; set; } = 0;

        /// <summary>
        /// Döşeme kalınlığı (cm cinsinden)
        /// </summary>
        [Range(10, 50, ErrorMessage = "Döşeme kalınlığı 10-50 cm arasında olmalıdır")]
        public int DosemeKalinligi { get; set; } = 20;

        /// <summary>
        /// Uzunluk birimi (m, cm, mm)
        /// </summary>
        [Required(ErrorMessage = "Uzunluk birimi zorunludur")]
        [StringLength(10, ErrorMessage = "Uzunluk birimi en fazla 10 karakter olabilir")]
        public string UzunlukBirimi { get; set; } = "m";

        /// <summary>
        /// Alan birimi (m², cm², mm²)
        /// </summary>
        [Required(ErrorMessage = "Alan birimi zorunludur")]
        [StringLength(10, ErrorMessage = "Alan birimi en fazla 10 karakter olabilir")]
        public string AlanBirimi { get; set; } = "m²";

        /// <summary>
        /// Proje açıklaması
        /// </summary>
        [StringLength(1000, ErrorMessage = "Proje açıklaması en fazla 1000 karakter olabilir")]
        public string ProjeAciklamasi { get; set; } = string.Empty;

        /// <summary>
        /// Proje lokasyonu
        /// </summary>
        [StringLength(200, ErrorMessage = "Proje lokasyonu en fazla 200 karakter olabilir")]
        public string ProjeLokasyonu { get; set; } = string.Empty;

        /// <summary>
        /// Müteahhit firma
        /// </summary>
        [StringLength(200, ErrorMessage = "Müteahhit firma en fazla 200 karakter olabilir")]
        public string MuteahhitFirma { get; set; } = string.Empty;

        /// <summary>
        /// Proje mühendisi
        /// </summary>
        [StringLength(100, ErrorMessage = "Proje mühendisi en fazla 100 karakter olabilir")]
        public string ProjeMuhendisi { get; set; } = string.Empty;

        /// <summary>
        /// Oluşturulma tarihi
        /// </summary>
        public DateTime OlusturulmaTarihi { get; set; } = DateTime.Now;

        /// <summary>
        /// Güncellenme tarihi
        /// </summary>
        public DateTime GuncellenmeTarihi { get; set; } = DateTime.Now;

        /// <summary>
        /// Toplam kat sayısını hesaplar (bodrum + normal katlar)
        /// </summary>
        public int ToplamKatSayisi => KatSayisi + (BodrumVarMi ? BodrumKatSayisi : 0);

        /// <summary>
        /// Toplam yapı yüksekliğini hesaplar (metre cinsinden)
        /// </summary>
        public double ToplamYapiYuksekligi => 
            (KatSayisi * KatYuksekligi) + (BodrumVarMi ? BodrumKatSayisi * KatYuksekligi : 0);

        /// <summary>
        /// Proje bilgilerinin string temsilini döndürür
        /// </summary>
        public override string ToString()
        {
            return $"{ProjeAdi} - {KatSayisi} Kat" + 
                   (BodrumVarMi ? $" + {BodrumKatSayisi} Bodrum" : "") +
                   $" - {TemelTuru} Temel";
        }

        /// <summary>
        /// İki proje bilgisinin eşitliğini kontrol eder
        /// </summary>
        public override bool Equals(object? obj)
        {
            if (obj is not ProjeBilgileri other) return false;
            return Id == other.Id;
        }

        /// <summary>
        /// Hash code döndürür
        /// </summary>
        public override int GetHashCode()
        {
            return Id.GetHashCode();
        }

        /// <summary>
        /// Proje bilgilerini klonlar
        /// </summary>
        public ProjeBilgileri Clone()
        {
            return new ProjeBilgileri
            {
                Id = Guid.NewGuid(), // Yeni ID oluştur
                ProjeAdi = this.ProjeAdi,
                TemelTuru = this.TemelTuru,
                TemelDerinligi = this.TemelDerinligi,
                KatSayisi = this.KatSayisi,
                KatYuksekligi = this.KatYuksekligi,
                BodrumVarMi = this.BodrumVarMi,
                BodrumKatSayisi = this.BodrumKatSayisi,
                DosemeKalinligi = this.DosemeKalinligi,
                UzunlukBirimi = this.UzunlukBirimi,
                AlanBirimi = this.AlanBirimi,
                ProjeAciklamasi = this.ProjeAciklamasi,
                ProjeLokasyonu = this.ProjeLokasyonu,
                MuteahhitFirma = this.MuteahhitFirma,
                ProjeMuhendisi = this.ProjeMuhendisi,
                OlusturulmaTarihi = DateTime.Now,
                GuncellenmeTarihi = DateTime.Now
            };
        }
    }
}

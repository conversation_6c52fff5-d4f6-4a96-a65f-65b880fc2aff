# DonDonat MSIX Paket Oluşturucu
# Bu script, Don<PERSON>onat uygulamasını MSIX paketi olarak derler ve paketler

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("Debug", "Release")]
    [string]$Configuration = "Release",
    
    [Parameter(Mandatory=$false)]
    [ValidateSet("x64", "x86", "ARM64")]
    [string]$Platform = "x64",
    
    [Parameter(Mandatory=$false)]
    [string]$OutputPath = "..\AppPackages",
    
    [Parameter(Mandatory=$false)]
    [switch]$SkipBuild,
    
    [Parameter(Mandatory=$false)]
    [switch]$CreateAppInstaller
)

# Renkli çıktı için fonksiyonlar
function Write-Success { param($Message) Write-Host $Message -ForegroundColor Green }
function Write-Info { param($Message) Write-Host $Message -ForegroundColor Cyan }
function Write-Warning { param($Message) Write-Host $Message -ForegroundColor Yellow }
function Write-Error { param($Message) Write-Host $Message -ForegroundColor Red }

Write-Info "DonDonat MSIX Paket Oluşturucu"
Write-Info "================================"
Write-Info "Konfigürasyon: $Configuration"
Write-Info "Platform: $Platform"
Write-Info "Çıktı Yolu: $OutputPath"

# Proje yollarını belirle
$SolutionPath = "..\..\DonDonat.sln"
$PackageProjectPath = "..\DonDonat.Package.wapproj"
$WpfProjectPath = "..\..\DonDonat.WPF\DonDonat.WPF.csproj"

# Çıktı klasörünü oluştur
if (-not (Test-Path $OutputPath)) {
    New-Item -ItemType Directory -Path $OutputPath -Force | Out-Null
    Write-Info "Çıktı klasörü oluşturuldu: $OutputPath"
}

# MSBuild yolunu bul
$MSBuildPath = ""
$VSWhere = "${env:ProgramFiles(x86)}\Microsoft Visual Studio\Installer\vswhere.exe"

if (Test-Path $VSWhere) {
    $VSPath = & $VSWhere -latest -products * -requires Microsoft.Component.MSBuild -property installationPath
    if ($VSPath) {
        $MSBuildPath = Join-Path $VSPath "MSBuild\Current\Bin\MSBuild.exe"
        if (-not (Test-Path $MSBuildPath)) {
            $MSBuildPath = Join-Path $VSPath "MSBuild\15.0\Bin\MSBuild.exe"
        }
    }
}

if (-not (Test-Path $MSBuildPath)) {
    Write-Error "MSBuild bulunamadı. Visual Studio 2019 veya 2022 yüklü olduğundan emin olun."
    exit 1
}

Write-Info "MSBuild yolu: $MSBuildPath"

try {
    # 1. WPF projesini derle
    if (-not $SkipBuild) {
        Write-Info "WPF projesi derleniyor..."
        
        & $MSBuildPath $WpfProjectPath `
            /p:Configuration=$Configuration `
            /p:Platform="Any CPU" `
            /p:OutputPath="bin\$Configuration\" `
            /verbosity:minimal
            
        if ($LASTEXITCODE -ne 0) {
            throw "WPF projesi derlenemedi"
        }
        Write-Success "✓ WPF projesi başarıyla derlendi"
    }

    # 2. MSIX paketini oluştur
    Write-Info "MSIX paketi oluşturuluyor..."
    
    $MSIXArgs = @(
        $PackageProjectPath
        "/p:Configuration=$Configuration"
        "/p:Platform=$Platform"
        "/p:AppxPackageDir=$OutputPath\"
        "/p:AppxBundle=Always"
        "/p:UapAppxPackageBuildMode=StoreUpload"
        "/verbosity:minimal"
    )
    
    & $MSBuildPath @MSIXArgs
    
    if ($LASTEXITCODE -ne 0) {
        throw "MSIX paketi oluşturulamadı"
    }
    Write-Success "✓ MSIX paketi başarıyla oluşturuldu"

    # 3. App Installer dosyası oluştur
    if ($CreateAppInstaller) {
        Write-Info "App Installer dosyası oluşturuluyor..."
        
        $AppInstallerContent = @"
<?xml version="1.0" encoding="utf-8"?>
<AppInstaller
    xmlns="http://schemas.microsoft.com/appx/appinstaller/2018"
    Version="1.0.0.0"
    Uri="https://your-domain.com/dondonat/DonDonat.appinstaller">
    
    <MainBundle
        Name="DonDonat.App"
        Publisher="CN=YourCompany"
        Version="1.0.0.0"
        Uri="https://your-domain.com/dondonat/DonDonat_1.0.0.0_x64.msixbundle" />
    
    <UpdateSettings>
        <OnLaunch
            HoursBetweenUpdateChecks="24"
            ShowPrompt="true"
            UpdateBlocksActivation="true" />
    </UpdateSettings>
    
</AppInstaller>
"@
        
        $AppInstallerPath = Join-Path $OutputPath "DonDonat.appinstaller"
        $AppInstallerContent | Out-File -FilePath $AppInstallerPath -Encoding UTF8
        Write-Success "✓ App Installer dosyası oluşturuldu: $AppInstallerPath"
    }

    # 4. Sonuçları göster
    Write-Success "`n🎉 MSIX paketleme tamamlandı!"
    Write-Info "Çıktı klasörü: $OutputPath"
    
    # Oluşturulan dosyaları listele
    $PackageFiles = Get-ChildItem -Path $OutputPath -Recurse -Include "*.msix", "*.msixbundle", "*.appinstaller"
    if ($PackageFiles) {
        Write-Info "`nOluşturulan dosyalar:"
        foreach ($file in $PackageFiles) {
            $size = [math]::Round($file.Length / 1MB, 2)
            Write-Host "  📦 $($file.Name) - $size MB" -ForegroundColor White
        }
    }
    
    # Kurulum talimatları
    Write-Info "`n📋 Kurulum Talimatları:"
    Write-Host "1. MSIX dosyasını çift tıklayarak yükleyin" -ForegroundColor White
    Write-Host "2. Veya PowerShell'de: Add-AppxPackage -Path 'paket_yolu.msix'" -ForegroundColor White
    Write-Host "3. App Installer dosyası ile: DonDonat.appinstaller dosyasını çift tıklayın" -ForegroundColor White
    
    Write-Warning "`n⚠️  Not: İlk kurulumda 'Güvenilmeyen yayımcı' uyarısı alabilirsiniz."
    Write-Info "Çözüm: Sertifika imzalama veya Developer Mode etkinleştirme"

} catch {
    Write-Error "❌ Hata: $($_.Exception.Message)"
    Write-Error "Detaylar: $($_.Exception.StackTrace)"
    exit 1
}

Write-Success "`n✅ İşlem tamamlandı!"

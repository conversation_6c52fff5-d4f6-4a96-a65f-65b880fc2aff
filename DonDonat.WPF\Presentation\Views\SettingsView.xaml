<UserControl x:Class="DonDonat.WPF.Presentation.Views.SettingsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="800">
    
    <UserControl.Resources>
        <!-- Stil tanımları -->
        <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#2C3E50"/>
            <Setter Property="Margin" Value="0,10,0,5"/>
        </Style>
        
        <Style x:Key="SettingLabelStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Margin" Value="0,5,10,5"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="MinWidth" Value="150"/>
        </Style>
        
        <Style x:Key="SettingControlStyle" TargetType="FrameworkElement">
            <Setter Property="Margin" Value="0,5,0,5"/>
            <Setter Property="MinWidth" Value="200"/>
        </Style>
        
        <Style x:Key="ButtonStyle" TargetType="Button">
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Background" Value="#3498DB"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#2980B9"/>
                </Trigger>
                <Trigger Property="IsEnabled" Value="False">
                    <Setter Property="Background" Value="#BDC3C7"/>
                    <Setter Property="Cursor" Value="Arrow"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Ana içerik -->
        <ScrollViewer Grid.Row="0" VerticalScrollBarVisibility="Auto" Padding="20">
            <StackPanel>
                
                <!-- Genel Ayarlar -->
                <TextBlock Text="Genel Ayarlar" Style="{StaticResource SectionHeaderStyle}"/>
                <Border BorderBrush="#E0E0E0" BorderThickness="1" Padding="15" Margin="0,0,0,20">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="Tema:" Style="{StaticResource SettingLabelStyle}"/>
                        <ComboBox Grid.Row="0" Grid.Column="1" 
                                  ItemsSource="{Binding AvailableThemes}"
                                  SelectedItem="{Binding Settings.Theme}"
                                  Style="{StaticResource SettingControlStyle}"/>
                        
                        <TextBlock Grid.Row="1" Grid.Column="0" Text="Dil:" Style="{StaticResource SettingLabelStyle}"/>
                        <ComboBox Grid.Row="1" Grid.Column="1" 
                                  ItemsSource="{Binding AvailableLanguages}"
                                  SelectedItem="{Binding Settings.Language}"
                                  Style="{StaticResource SettingControlStyle}"/>
                        
                        <TextBlock Grid.Row="2" Grid.Column="0" Text="Varsayılan Klasör:" Style="{StaticResource SettingLabelStyle}"/>
                        <Grid Grid.Row="2" Grid.Column="1">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBox Grid.Column="0" Text="{Binding Settings.DefaultFolderPath}" 
                                     Style="{StaticResource SettingControlStyle}"/>
                            <Button Grid.Column="1" Content="..." 
                                    Command="{Binding BrowseDefaultFolderCommand}"
                                    Width="30" Margin="5,5,0,5"/>
                        </Grid>
                        
                        <TextBlock Grid.Row="3" Grid.Column="0" Text="Bildirimler:" Style="{StaticResource SettingLabelStyle}"/>
                        <CheckBox Grid.Row="3" Grid.Column="1" 
                                  IsChecked="{Binding Settings.EnableNotifications}"
                                  Content="Bildirimleri etkinleştir"
                                  Style="{StaticResource SettingControlStyle}"/>
                    </Grid>
                </Border>
                
                <!-- Görünüm Ayarları -->
                <TextBlock Text="Görünüm Ayarları" Style="{StaticResource SectionHeaderStyle}"/>
                <Border BorderBrush="#E0E0E0" BorderThickness="1" Padding="15" Margin="0,0,0,20">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="Font Ailesi:" Style="{StaticResource SettingLabelStyle}"/>
                        <ComboBox Grid.Row="0" Grid.Column="1" 
                                  ItemsSource="{Binding AvailableFontFamilies}"
                                  SelectedItem="{Binding Settings.FontFamily}"
                                  Style="{StaticResource SettingControlStyle}"/>
                        
                        <TextBlock Grid.Row="1" Grid.Column="0" Text="Font Boyutu:" Style="{StaticResource SettingLabelStyle}"/>
                        <ComboBox Grid.Row="1" Grid.Column="1" 
                                  ItemsSource="{Binding FontSizeOptions}"
                                  SelectedItem="{Binding Settings.FontSize}"
                                  Style="{StaticResource SettingControlStyle}"/>
                        
                        <TextBlock Grid.Row="2" Grid.Column="0" Text="Izgara Çizgileri:" Style="{StaticResource SettingLabelStyle}"/>
                        <CheckBox Grid.Row="2" Grid.Column="1" 
                                  IsChecked="{Binding Settings.ShowGridLines}"
                                  Content="Tablo ızgara çizgilerini göster"
                                  Style="{StaticResource SettingControlStyle}"/>
                        
                        <TextBlock Grid.Row="3" Grid.Column="0" Text="Satır Numaraları:" Style="{StaticResource SettingLabelStyle}"/>
                        <CheckBox Grid.Row="3" Grid.Column="1" 
                                  IsChecked="{Binding Settings.ShowRowNumbers}"
                                  Content="Satır numaralarını göster"
                                  Style="{StaticResource SettingControlStyle}"/>
                        
                        <TextBlock Grid.Row="4" Grid.Column="0" Text="Alternatif Satır Renkleri:" Style="{StaticResource SettingLabelStyle}"/>
                        <CheckBox Grid.Row="4" Grid.Column="1" 
                                  IsChecked="{Binding Settings.EnableAlternatingRowColors}"
                                  Content="Alternatif satır renklerini etkinleştir"
                                  Style="{StaticResource SettingControlStyle}"/>
                        
                        <TextBlock Grid.Row="5" Grid.Column="0" Text="Durum Çubuğu:" Style="{StaticResource SettingLabelStyle}"/>
                        <CheckBox Grid.Row="5" Grid.Column="1" 
                                  IsChecked="{Binding Settings.ShowStatusBar}"
                                  Content="Durum çubuğunu göster"
                                  Style="{StaticResource SettingControlStyle}"/>
                        
                        <TextBlock Grid.Row="6" Grid.Column="0" Text="Araç Çubuğu:" Style="{StaticResource SettingLabelStyle}"/>
                        <CheckBox Grid.Row="6" Grid.Column="1" 
                                  IsChecked="{Binding Settings.ShowToolbar}"
                                  Content="Araç çubuğunu göster"
                                  Style="{StaticResource SettingControlStyle}"/>
                    </Grid>
                </Border>
                
                <!-- Pencere Ayarları -->
                <TextBlock Text="Pencere Ayarları" Style="{StaticResource SectionHeaderStyle}"/>
                <Border BorderBrush="#E0E0E0" BorderThickness="1" Padding="15" Margin="0,0,0,20">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="Pencere Boyutunu Hatırla:" Style="{StaticResource SettingLabelStyle}"/>
                        <CheckBox Grid.Row="0" Grid.Column="1" 
                                  IsChecked="{Binding Settings.RememberWindowSize}"
                                  Content="Pencere boyutunu ve konumunu hatırla"
                                  Style="{StaticResource SettingControlStyle}"/>
                        
                        <TextBlock Grid.Row="1" Grid.Column="0" Text="Pencere Genişliği:" Style="{StaticResource SettingLabelStyle}"/>
                        <TextBox Grid.Row="1" Grid.Column="1" 
                                 Text="{Binding Settings.WindowWidth}"
                                 IsEnabled="{Binding Settings.RememberWindowSize}"
                                 Style="{StaticResource SettingControlStyle}"/>
                        
                        <TextBlock Grid.Row="2" Grid.Column="0" Text="Pencere Yüksekliği:" Style="{StaticResource SettingLabelStyle}"/>
                        <TextBox Grid.Row="2" Grid.Column="1" 
                                 Text="{Binding Settings.WindowHeight}"
                                 IsEnabled="{Binding Settings.RememberWindowSize}"
                                 Style="{StaticResource SettingControlStyle}"/>
                        
                        <TextBlock Grid.Row="3" Grid.Column="0" Text="Çıkış Onayı:" Style="{StaticResource SettingLabelStyle}"/>
                        <CheckBox Grid.Row="3" Grid.Column="1" 
                                  IsChecked="{Binding Settings.ConfirmOnExit}"
                                  Content="Çıkış yaparken onay iste"
                                  Style="{StaticResource SettingControlStyle}"/>
                    </Grid>
                </Border>
                
                <!-- Otomatik Kaydetme -->
                <TextBlock Text="Otomatik Kaydetme" Style="{StaticResource SectionHeaderStyle}"/>
                <Border BorderBrush="#E0E0E0" BorderThickness="1" Padding="15" Margin="0,0,0,20">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="Otomatik Kaydetme:" Style="{StaticResource SettingLabelStyle}"/>
                        <CheckBox Grid.Row="0" Grid.Column="1" 
                                  IsChecked="{Binding Settings.AutoSaveEnabled}"
                                  Content="Otomatik kaydetmeyi etkinleştir"
                                  Style="{StaticResource SettingControlStyle}"/>
                        
                        <TextBlock Grid.Row="1" Grid.Column="0" Text="Kaydetme Aralığı (dk):" Style="{StaticResource SettingLabelStyle}"/>
                        <ComboBox Grid.Row="1" Grid.Column="1" 
                                  ItemsSource="{Binding AutoSaveIntervalOptions}"
                                  SelectedItem="{Binding Settings.AutoSaveIntervalMinutes}"
                                  IsEnabled="{Binding Settings.AutoSaveEnabled}"
                                  Style="{StaticResource SettingControlStyle}"/>
                    </Grid>
                </Border>
                
                <!-- Export Ayarları -->
                <TextBlock Text="Export Ayarları" Style="{StaticResource SectionHeaderStyle}"/>
                <Border BorderBrush="#E0E0E0" BorderThickness="1" Padding="15" Margin="0,0,0,20">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="Varsayılan Format:" Style="{StaticResource SettingLabelStyle}"/>
                        <ComboBox Grid.Row="0" Grid.Column="1" 
                                  ItemsSource="{Binding AvailableExportFormats}"
                                  SelectedItem="{Binding Settings.ExportDefaultFormat}"
                                  Style="{StaticResource SettingControlStyle}"/>
                        
                        <TextBlock Grid.Row="1" Grid.Column="0" Text="Export Sonrası:" Style="{StaticResource SettingLabelStyle}"/>
                        <CheckBox Grid.Row="1" Grid.Column="1" 
                                  IsChecked="{Binding Settings.OpenFileAfterExport}"
                                  Content="Export sonrası dosyayı aç"
                                  Style="{StaticResource SettingControlStyle}"/>
                    </Grid>
                </Border>
                
                <!-- Son Dosyalar -->
                <TextBlock Text="Son Dosyalar" Style="{StaticResource SectionHeaderStyle}"/>
                <Border BorderBrush="#E0E0E0" BorderThickness="1" Padding="15" Margin="0,0,0,20">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <Grid Grid.Row="0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Column="0" Text="Maksimum Dosya Sayısı:" Style="{StaticResource SettingLabelStyle}"/>
                            <TextBox Grid.Column="1" Text="{Binding Settings.MaxRecentFiles}" 
                                     Style="{StaticResource SettingControlStyle}"/>
                            <Button Grid.Column="2" Content="Temizle" 
                                    Command="{Binding ClearRecentFilesCommand}"
                                    Style="{StaticResource ButtonStyle}"
                                    Background="#E74C3C"/>
                        </Grid>
                        
                        <TextBlock Grid.Row="1" Text="Son Dosyalar:" Style="{StaticResource SettingLabelStyle}" Margin="0,10,0,5"/>
                        <ListBox Grid.Row="2" ItemsSource="{Binding RecentFilesList}" 
                                 MaxHeight="100" ScrollViewer.VerticalScrollBarVisibility="Auto"/>
                    </Grid>
                </Border>
                
            </StackPanel>
        </ScrollViewer>
        
        <!-- Durum çubuğu -->
        <Border Grid.Row="1" BorderBrush="#E0E0E0" BorderThickness="0,1,0,0" Background="#F8F9FA" Padding="10,5">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" Text="{Binding StatusMessage}" VerticalAlignment="Center"/>
                <ProgressBar Grid.Column="1" Width="100" Height="20"
                             IsIndeterminate="{Binding IsLoading}">
                    <ProgressBar.Style>
                        <Style TargetType="ProgressBar">
                            <Setter Property="Visibility" Value="Collapsed"/>
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding IsLoading}" Value="True">
                                    <Setter Property="Visibility" Value="Visible"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </ProgressBar.Style>
                </ProgressBar>
            </Grid>
        </Border>
        
        <!-- Butonlar -->
        <Border Grid.Row="2" BorderBrush="#E0E0E0" BorderThickness="0,1,0,0" Background="#F8F9FA" Padding="20,10">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Content="Kaydet" Command="{Binding SaveCommand}" Style="{StaticResource ButtonStyle}" Background="#27AE60"/>
                <Button Content="İptal" Command="{Binding CancelCommand}" Style="{StaticResource ButtonStyle}" Background="#95A5A6"/>
                <Button Content="Varsayılana Sıfırla" Command="{Binding ResetToDefaultsCommand}" Style="{StaticResource ButtonStyle}" Background="#E67E22"/>
                <Button Content="Export" Command="{Binding ExportSettingsCommand}" Style="{StaticResource ButtonStyle}" Background="#9B59B6"/>
                <Button Content="Import" Command="{Binding ImportSettingsCommand}" Style="{StaticResource ButtonStyle}" Background="#3498DB"/>
            </StackPanel>
        </Border>
        
    </Grid>
</UserControl>

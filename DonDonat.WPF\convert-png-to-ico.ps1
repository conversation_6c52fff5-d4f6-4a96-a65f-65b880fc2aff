# PNG'den ICO'ya Dönüştürücü
# DonDonat.png dosyasını DonDonat.ico'ya dönüştürür

param(
    [Parameter(Mandatory=$false)]
    [string]$InputPath = "Resources\DonDonat.png",
    
    [Parameter(Mandatory=$false)]
    [string]$OutputPath = "Resources\DonDonat.ico"
)

# Renkli çıktı fonksiyonları
function Write-Success { param($Message) Write-Host $Message -ForegroundColor Green }
function Write-Info { param($Message) Write-Host $Message -ForegroundColor Cyan }
function Write-Warning { param($Message) Write-Host $Message -ForegroundColor Yellow }
function Write-Error { param($Message) Write-Host $Message -ForegroundColor Red }

Write-Info "🔄 PNG'den ICO'ya Dönüştürücü"
Write-Info "=============================="
Write-Info "Giriş: $InputPath"
Write-Info "Çıkış: $OutputPath"

try {
    # .NET Drawing kütüphanesini yükle
    Add-Type -AssemblyName System.Drawing
    Add-Type -AssemblyName System.Windows.Forms

    # PNG dosyasının varlığını kontrol et
    if (-not (Test-Path $InputPath)) {
        throw "PNG dosyası bulunamadı: $InputPath"
    }

    Write-Info "📂 PNG dosyası bulundu"
    
    # PNG dosyasını yükle
    $originalImage = [System.Drawing.Image]::FromFile((Resolve-Path $InputPath).Path)
    Write-Info "🖼️ PNG dosyası yüklendi: $($originalImage.Width)x$($originalImage.Height)"
    
    # ICO için farklı boyutlar oluştur
    $iconSizes = @(16, 32, 48, 64, 128, 256)
    Write-Info "🔧 ICO için $($iconSizes.Count) farklı boyut oluşturuluyor..."
    
    # Her boyut için bitmap oluştur
    $bitmaps = @()
    foreach ($size in $iconSizes) {
        Write-Info "   📏 $size x $size boyutu oluşturuluyor..."
        
        $bitmap = New-Object System.Drawing.Bitmap($size, $size)
        $graphics = [System.Drawing.Graphics]::FromImage($bitmap)
        
        # Yüksek kalite ayarları
        $graphics.InterpolationMode = [System.Drawing.Drawing2D.InterpolationMode]::HighQualityBicubic
        $graphics.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::AntiAlias
        $graphics.PixelOffsetMode = [System.Drawing.Drawing2D.PixelOffsetMode]::HighQuality
        
        # Orijinal resmi yeniden boyutlandır
        $graphics.DrawImage($originalImage, 0, 0, $size, $size)
        
        $bitmaps += $bitmap
        $graphics.Dispose()
    }
    
    Write-Success "✅ Tüm boyutlar oluşturuldu"
    
    # ICO dosyası oluştur
    Write-Info "🔨 ICO dosyası oluşturuluyor..."
    
    # Çıkış klasörünü oluştur
    $outputDir = Split-Path $OutputPath -Parent
    if (-not (Test-Path $outputDir)) {
        New-Item -ItemType Directory -Path $outputDir -Force | Out-Null
    }
    
    # ICO dosyası header'ı oluştur
    $iconData = New-Object System.Collections.Generic.List[byte]
    
    # ICO header (6 bytes)
    $iconData.AddRange([byte[]](0, 0))  # Reserved (2 bytes)
    $iconData.AddRange([byte[]](1, 0))  # Type: ICO (2 bytes)
    $iconData.AddRange([byte[]]($iconSizes.Count, 0))  # Image count (2 bytes)
    
    # Directory entries (16 bytes each)
    $dataOffset = 6 + ($iconSizes.Count * 16)
    $imageDataList = @()
    
    for ($i = 0; $i -lt $iconSizes.Count; $i++) {
        $size = $iconSizes[$i]
        $bitmap = $bitmaps[$i]
        
        # PNG formatında kaydet (ICO içinde PNG kullanabiliriz)
        $ms = New-Object System.IO.MemoryStream
        $bitmap.Save($ms, [System.Drawing.Imaging.ImageFormat]::Png)
        $imageData = $ms.ToArray()
        $imageDataList += ,$imageData
        $ms.Dispose()
        
        # Directory entry
        $width = if ($size -eq 256) { 0 } else { $size }
        $height = if ($size -eq 256) { 0 } else { $size }
        $iconData.Add($width)  # Width
        $iconData.Add($height)  # Height
        $iconData.Add(0)  # Color count (0 for PNG)
        $iconData.Add(0)  # Reserved
        $iconData.AddRange([byte[]](1, 0))  # Planes
        $iconData.AddRange([byte[]](32, 0))  # Bits per pixel
        
        # Data size (4 bytes, little endian)
        $dataSize = $imageData.Length
        $iconData.Add($dataSize -band 0xFF)
        $iconData.Add(($dataSize -shr 8) -band 0xFF)
        $iconData.Add(($dataSize -shr 16) -band 0xFF)
        $iconData.Add(($dataSize -shr 24) -band 0xFF)
        
        # Data offset (4 bytes, little endian)
        $iconData.Add($dataOffset -band 0xFF)
        $iconData.Add(($dataOffset -shr 8) -band 0xFF)
        $iconData.Add(($dataOffset -shr 16) -band 0xFF)
        $iconData.Add(($dataOffset -shr 24) -band 0xFF)
        
        $dataOffset += $dataSize
    }
    
    # Image data'ları ekle
    foreach ($imageData in $imageDataList) {
        $iconData.AddRange($imageData)
    }
    
    # ICO dosyasını kaydet
    [System.IO.File]::WriteAllBytes((Resolve-Path $outputDir).Path + "\" + (Split-Path $OutputPath -Leaf), $iconData.ToArray())
    
    Write-Success "✅ ICO dosyası başarıyla oluşturuldu!"
    
    # Cleanup
    $originalImage.Dispose()
    foreach ($bitmap in $bitmaps) {
        $bitmap.Dispose()
    }
    
    # Dosya bilgilerini göster
    if (Test-Path $OutputPath) {
        $icoInfo = Get-Item $OutputPath
        $pngInfo = Get-Item $InputPath
        
        Write-Info "📊 Dosya Bilgileri:"
        Write-Info "   🖼️ PNG: $([math]::Round($pngInfo.Length / 1KB, 2)) KB"
        Write-Info "   📄 ICO: $([math]::Round($icoInfo.Length / 1KB, 2)) KB"
        Write-Info "   📏 Boyutlar: $($iconSizes -join ', ') piksel"
        
        Write-Success "🎉 Dönüştürme başarıyla tamamlandı!"
        Write-Info "📁 ICO dosyası: $OutputPath"
        
        # Test önerisi
        Write-Info "`n🧪 Test Etmek İçin:"
        Write-Host "   dotnet build" -ForegroundColor Yellow
        Write-Host "   dotnet run" -ForegroundColor Yellow
    } else {
        throw "ICO dosyası oluşturulamadı"
    }
    
} catch {
    Write-Error "❌ Dönüştürme hatası: $($_.Exception.Message)"
    Write-Error "📋 Detaylar: $($_.Exception.StackTrace)"
    
    Write-Warning "`n💡 Alternatif Çözümler:"
    Write-Host "1. Online dönüştürücü kullanın: convertio.co/png-ico" -ForegroundColor Yellow
    Write-Host "2. GIMP veya Paint.NET kullanın" -ForegroundColor Yellow
    Write-Host "3. ImageMagick yükleyin: winget install ImageMagick.ImageMagick" -ForegroundColor Yellow
    
    exit 1
}

Write-Success "`n✅ Script tamamlandı!"

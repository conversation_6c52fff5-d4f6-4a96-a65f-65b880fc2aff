# 🚀 DonDonat Hızlı Dağıtım Kılavuzu

## ⚡ 5 Dakikada Dağıtım

### 1️⃣ <PERSON><PERSON><PERSON>rl<PERSON><PERSON> (2 dakika)

```powershell
# PowerShell'i yönetici olarak açın
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# Proje klasörüne gidin
cd "C:\Users\<USER>\Desktop\DonDonat"

# Gerekli araçları kontrol edin
dotnet --version  # .NET 9.0 olmalı
```

### 2️⃣ Hızlı MSIX Dağıtımı (2 dakika)

```powershell
# Master build script'i çalıştırın
.\Build-DonDonat.ps1 -PackageType MSIX -Configuration Release -OpenOutput

# Veya sadece ClickOnce için
.\Build-DonDonat.ps1 -PackageType ClickOnce -Configuration Release -OpenOutput
```

### 3️⃣ Test Kurulumu (1 dakika)

```powershell
# MSIX paketini test edin
Add-AppxPackage -Path ".\Packages\MSIX\DonDonat_1.0.0.0_x64.msix"

# Veya ClickOnce setup.exe'yi çalıştırın
.\Packages\ClickOnce\setup.exe
```

## 🎯 Seçenekler

### Option A: MSIX (Modern Windows)
```powershell
# Tam otomatik MSIX dağıtımı
.\Build-DonDonat.ps1 -PackageType MSIX -Configuration Release
```

**Avantajları:**
- ✅ Microsoft Store uyumlu
- ✅ Güvenli kurulum/kaldırma
- ✅ Otomatik güncelleme
- ✅ Modern Windows deneyimi

### Option B: ClickOnce (Web Dağıtımı)
```powershell
# Web tabanlı ClickOnce dağıtımı
.\Build-DonDonat.ps1 -PackageType ClickOnce -Configuration Release
```

**Avantajları:**
- ✅ Web üzerinden kolay dağıtım
- ✅ Otomatik güncelleme
- ✅ Eski Windows sürümleri desteği
- ✅ Kurumsal ağ dağıtımı

### Option C: Her İkisi (Maksimum Uyumluluk)
```powershell
# Hem MSIX hem ClickOnce oluştur
.\Build-DonDonat.ps1 -PackageType Both -Configuration Release
```

## 📦 Çıktı Dosyaları

### MSIX Paketi
```
Packages/MSIX/
├── DonDonat_1.0.0.0_x64.msix          # Ana kurulum paketi
├── DonDonat_1.0.0.0_x64.msixbundle    # Bundle paketi
└── DonDonat.appinstaller              # Web kurulum dosyası
```

### ClickOnce Paketi
```
Packages/ClickOnce/
├── setup.exe                          # Kurulum dosyası
├── DonDonat.WPF.application           # ClickOnce manifest
├── index.html                         # Web kurulum sayfası
└── Application Files/                  # Uygulama dosyaları
```

## 🌐 Web Dağıtımı

### ClickOnce Web Kurulumu
1. `Packages/ClickOnce/` klasörünü web sunucunuza yükleyin
2. `index.html` sayfasını kullanıcılarla paylaşın
3. Kullanıcılar "Şimdi Yükle" butonuna tıklayarak kurulum yapar

### MSIX Web Kurulumu
1. `DonDonat.appinstaller` dosyasını web sunucunuza yükleyin
2. Kullanıcılar dosyayı çift tıklayarak kurulum yapar
3. Otomatik güncelleme aktif olur

## 🔧 Özelleştirme

### Versiyon Güncelleme
```powershell
# Yeni versiyon ile paketleme
.\Build-DonDonat.ps1 -Version "*******" -Configuration Release
```

### URL Değiştirme
```powershell
# Kendi domain'inizi kullanın
# DonDonat.WPF/DonDonat.WPF.csproj dosyasında:
<PublishUrl>https://yourdomain.com/dondonat/</PublishUrl>
```

### İmaj Güncelleme
```powershell
# Kendi logo'nuzla imajları oluşturun
.\DonDonat.Package\Scripts\Generate-AppImages.ps1 -SourceIconPath "YourLogo.ico"
```

## 🚨 Sorun Giderme

### Yaygın Hatalar

#### "MSBuild bulunamadı"
```powershell
# Visual Studio 2022 yükleyin veya Build Tools indirin
winget install Microsoft.VisualStudio.2022.BuildTools
```

#### "Windows App SDK bulunamadı"
```powershell
# Windows App SDK yükleyin
winget install Microsoft.WindowsAppSDK
```

#### "Güvenilmeyen yayımcı" uyarısı
```powershell
# Developer Mode'u etkinleştirin (Settings > Update & Security > For developers)
# Veya kod imzalama sertifikası kullanın
```

### Log Kontrolü
```powershell
# Build loglarını kontrol edin
Get-Content ".\build.log" -Tail 50
```

## 📋 Checklist

### Dağıtım Öncesi
- [ ] .NET 9.0 SDK yüklü
- [ ] Visual Studio 2022 yüklü
- [ ] Windows App SDK yüklü (MSIX için)
- [ ] DonDonat.ico dosyası mevcut
- [ ] Proje başarıyla derleniyor

### Dağıtım Sonrası
- [ ] Paket dosyaları oluşturuldu
- [ ] Test kurulumu başarılı
- [ ] Uygulama çalışıyor
- [ ] Dosya ilişkilendirmeleri aktif
- [ ] Masaüstü kısayolu oluşturuldu

### Web Dağıtımı
- [ ] Dosyalar web sunucusuna yüklendi
- [ ] HTTPS aktif
- [ ] MIME types yapılandırıldı
- [ ] Kurulum sayfası erişilebilir

## 🎉 Başarı!

Tebrikler! DonDonat uygulamanız artık dağıtıma hazır. 

### Sonraki Adımlar:
1. **Üretim Sertifikası:** Kod imzalama sertifikası alın
2. **Otomatik Güncelleme:** Güncelleme sunucusunu kurun
3. **Analytics:** Kurulum ve kullanım istatistiklerini takip edin
4. **Feedback:** Kullanıcı geri bildirimlerini toplayın

### Destek:
- 📖 Detaylı dokümantasyon: `DEPLOYMENT-REQUIREMENTS.md`
- 🔧 Script dosyaları: `Scripts/` klasörü
- 🌐 Microsoft Docs: [MSIX](https://docs.microsoft.com/windows/msix/) | [ClickOnce](https://docs.microsoft.com/visualstudio/deployment/clickonce-security-and-deployment)

**Happy Deploying! 🚀**

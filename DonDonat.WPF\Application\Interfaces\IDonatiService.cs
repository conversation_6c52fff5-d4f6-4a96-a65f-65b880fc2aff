using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using DonDonat.WPF.Application.Services;
using DonDonat.WPF.Domain.Entities;

namespace DonDonat.WPF.Application.Interfaces
{
    /// <summary>
    /// Donatı servisi için interface
    /// </summary>
    public interface IDonatiService
    {
        /// <summary>
        /// Tüm donatıları getirir
        /// </summary>
        Task<List<Donati>> GetAllDonatiAsync();

        /// <summary>
        /// ID'ye göre donatı getirir
        /// </summary>
        Task<Donati?> GetDonatiByIdAsync(Guid id);

        /// <summary>
        /// Proje ID'sine göre donatıları getirir
        /// </summary>
        Task<List<Donati>> GetDonatiByProjeIdAsync(Guid projeId);

        /// <summary>
        /// Yeni donatı ekler
        /// </summary>
        Task<Donati> AddDonatiAsync(Donati donati);

        /// <summary>
        /// Donatı günceller
        /// </summary>
        Task<Donati> UpdateDonatiAsync(Donati donati);

        /// <summary>
        /// Donatı siler
        /// </summary>
        Task<bool> DeleteDonatiAsync(Guid id);

        /// <summary>
        /// Birden fazla donatı ekler
        /// </summary>
        Task<List<Donati>> AddMultipleDonatiAsync(List<Donati> donatilar);

        /// <summary>
        /// Çapa göre donatıları getirir
        /// </summary>
        Task<List<Donati>> GetDonatiByCapAsync(int cap);

        /// <summary>
        /// Yapı elemanına göre donatıları getirir
        /// </summary>
        Task<List<Donati>> GetDonatiByYapiElemaniAsync(string yapiElemani);

        /// <summary>
        /// Donatı türüne göre donatıları getirir
        /// </summary>
        Task<List<Donati>> GetDonatiByTurAsync(string tur);

        /// <summary>
        /// Arama yapar
        /// </summary>
        Task<List<Donati>> SearchDonatiAsync(string searchTerm);

        /// <summary>
        /// Donatı istatistiklerini getirir
        /// </summary>
        Task<DonatiMetrajOzeti> GetDonatiIstatistikleriAsync(Guid? projeId = null);

        /// <summary>
        /// Donatıları doğrular
        /// </summary>
        (bool IsValid, string[] ErrorMessages) ValidateDonati(Donati donati);

        /// <summary>
        /// Varsayılan donatı oluşturur
        /// </summary>
        Donati CreateDefaultDonati();

        /// <summary>
        /// Benzer donatıları birleştirir
        /// </summary>
        List<Donati> MergeSimilarDonatilar(List<Donati> donatilar);

        /// <summary>
        /// Sayfalama ile donatıları getirir
        /// </summary>
        Task<(List<Donati> Items, int TotalCount)> GetPagedDonatiAsync(
            int pageNumber, int pageSize, Guid? projeId = null);
    }
}

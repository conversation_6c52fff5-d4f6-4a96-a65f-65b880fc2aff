﻿// <auto-generated />
using System;
using DonDonat.WPF.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace DonDonat.WPF.Migrations
{
    [DbContext(typeof(DonDonatDbContext))]
    partial class DonDonatDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder.HasAnnotation("ProductVersion", "9.0.5");

            modelBuilder.Entity("DonDonat.WPF.Persistence.Entities.DonatiEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT");

                    b.Property<int>("Adet")
                        .HasColumnType("INTEGER")
                        .HasComment("Donatı adedi");

                    b.Property<double>("Boy")
                        .HasColumnType("REAL")
                        .HasComment("Donatı boyu (m)");

                    b.Property<int>("Cap")
                        .HasColumnType("INTEGER")
                        .HasComment("Donatı çapı (mm)");

                    b.Property<string>("DonatiSinifi")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT")
                        .HasDefaultValue("S420")
                        .HasComment("Donatı sınıfı");

                    b.Property<DateTime>("GuncellenmeTarihi")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT")
                        .HasDefaultValueSql("datetime('now')")
                        .HasComment("Güncellenme tarihi");

                    b.Property<string>("Konum")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT")
                        .HasComment("Donatı konumu");

                    b.Property<DateTime>("OlusturulmaTarihi")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT")
                        .HasDefaultValueSql("datetime('now')")
                        .HasComment("Oluşturulma tarihi");

                    b.Property<Guid?>("ProjeId")
                        .HasColumnType("TEXT");

                    b.Property<string>("Tur")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT")
                        .HasComment("Donatı türü");

                    b.Property<string>("YapiElemani")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT")
                        .HasComment("Yapı elemanı");

                    b.HasKey("Id");

                    b.HasIndex("Cap")
                        .HasDatabaseName("IX_Donatilar_Cap");

                    b.HasIndex("ProjeId")
                        .HasDatabaseName("IX_Donatilar_ProjeId");

                    b.HasIndex("Tur")
                        .HasDatabaseName("IX_Donatilar_Tur");

                    b.HasIndex("YapiElemani")
                        .HasDatabaseName("IX_Donatilar_YapiElemani");

                    b.ToTable("Donatilar");

                    b.HasData(
                        new
                        {
                            Id = new Guid("efb04067-7902-4383-85e9-e5aff9e6372b"),
                            Adet = 10,
                            Boy = 3.5,
                            Cap = 12,
                            DonatiSinifi = "S420",
                            GuncellenmeTarihi = new DateTime(2025, 6, 2, 22, 18, 22, 329, DateTimeKind.Local).AddTicks(1768),
                            Konum = "ÜST",
                            OlusturulmaTarihi = new DateTime(2025, 6, 2, 22, 18, 22, 329, DateTimeKind.Local).AddTicks(1249),
                            ProjeId = new Guid("434b86a9-04d1-4506-9223-cf27de60bd22"),
                            Tur = "NERVÜRLÜ",
                            YapiElemani = "KİRİŞ"
                        },
                        new
                        {
                            Id = new Guid("b7900934-2c91-434a-bf49-e6ef15166e77"),
                            Adet = 8,
                            Boy = 2.7999999999999998,
                            Cap = 16,
                            DonatiSinifi = "S420",
                            GuncellenmeTarihi = new DateTime(2025, 6, 2, 22, 18, 22, 329, DateTimeKind.Local).AddTicks(2277),
                            Konum = "BOYUNA",
                            OlusturulmaTarihi = new DateTime(2025, 6, 2, 22, 18, 22, 329, DateTimeKind.Local).AddTicks(2276),
                            ProjeId = new Guid("434b86a9-04d1-4506-9223-cf27de60bd22"),
                            Tur = "NERVÜRLÜ",
                            YapiElemani = "KOLON"
                        },
                        new
                        {
                            Id = new Guid("fdc54211-1ffc-4b35-9818-fe209f053c38"),
                            Adet = 15,
                            Boy = 4.2000000000000002,
                            Cap = 8,
                            DonatiSinifi = "S420",
                            GuncellenmeTarihi = new DateTime(2025, 6, 2, 22, 18, 22, 329, DateTimeKind.Local).AddTicks(2287),
                            Konum = "ETRIYE",
                            OlusturulmaTarihi = new DateTime(2025, 6, 2, 22, 18, 22, 329, DateTimeKind.Local).AddTicks(2286),
                            ProjeId = new Guid("434b86a9-04d1-4506-9223-cf27de60bd22"),
                            Tur = "ETRIYE",
                            YapiElemani = "KİRİŞ"
                        });
                });

            modelBuilder.Entity("DonDonat.WPF.Persistence.Entities.ProjeBilgileriEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT");

                    b.Property<bool>("AktifProje")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(true)
                        .HasComment("Aktif proje mi");

                    b.Property<string>("AlanBirimi")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(10)
                        .HasColumnType("TEXT")
                        .HasDefaultValue("m²")
                        .HasComment("Alan birimi");

                    b.Property<int>("BodrumKatSayisi")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(0)
                        .HasComment("Bodrum kat sayısı");

                    b.Property<bool>("BodrumVarMi")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(false)
                        .HasComment("Bodrum var mı");

                    b.Property<int>("DosemeKalinligi")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(20)
                        .HasComment("Döşeme kalınlığı (cm)");

                    b.Property<DateTime>("GuncellenmeTarihi")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT")
                        .HasDefaultValueSql("datetime('now')")
                        .HasComment("Güncellenme tarihi");

                    b.Property<int>("KatSayisi")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(1)
                        .HasComment("Kat sayısı");

                    b.Property<double>("KatYuksekligi")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("REAL")
                        .HasDefaultValue(3.0)
                        .HasComment("Kat yüksekliği (m)");

                    b.Property<string>("MuteahhitFirma")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT")
                        .HasComment("Müteahhit firma");

                    b.Property<DateTime>("OlusturulmaTarihi")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT")
                        .HasDefaultValueSql("datetime('now')")
                        .HasComment("Oluşturulma tarihi");

                    b.Property<string>("ProjeAciklamasi")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT")
                        .HasComment("Proje açıklaması");

                    b.Property<string>("ProjeAdi")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT")
                        .HasComment("Proje adı");

                    b.Property<string>("ProjeLokasyonu")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT")
                        .HasComment("Proje lokasyonu");

                    b.Property<string>("ProjeMuhendisi")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT")
                        .HasComment("Proje mühendisi");

                    b.Property<double>("TemelDerinligi")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("REAL")
                        .HasDefaultValue(1.5)
                        .HasComment("Temel derinliği (m)");

                    b.Property<string>("TemelTuru")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT")
                        .HasComment("Temel türü");

                    b.Property<string>("UzunlukBirimi")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(10)
                        .HasColumnType("TEXT")
                        .HasDefaultValue("m")
                        .HasComment("Uzunluk birimi");

                    b.HasKey("Id");

                    b.HasIndex("AktifProje")
                        .HasDatabaseName("IX_ProjeBilgileri_AktifProje");

                    b.HasIndex("OlusturulmaTarihi")
                        .HasDatabaseName("IX_ProjeBilgileri_OlusturulmaTarihi");

                    b.HasIndex("ProjeAdi")
                        .HasDatabaseName("IX_ProjeBilgileri_ProjeAdi");

                    b.ToTable("ProjeBilgileri");

                    b.HasData(
                        new
                        {
                            Id = new Guid("434b86a9-04d1-4506-9223-cf27de60bd22"),
                            AktifProje = true,
                            AlanBirimi = "m²",
                            BodrumKatSayisi = 1,
                            BodrumVarMi = true,
                            DosemeKalinligi = 25,
                            GuncellenmeTarihi = new DateTime(2025, 6, 2, 22, 18, 22, 327, DateTimeKind.Local).AddTicks(871),
                            KatSayisi = 3,
                            KatYuksekligi = 3.2000000000000002,
                            MuteahhitFirma = "Örnek İnşaat Ltd.",
                            OlusturulmaTarihi = new DateTime(2025, 6, 2, 22, 18, 22, 327, DateTimeKind.Local).AddTicks(246),
                            ProjeAciklamasi = "Örnek test projesi",
                            ProjeAdi = "Örnek Betonarme Proje",
                            ProjeLokasyonu = "İstanbul",
                            ProjeMuhendisi = "Test Mühendisi",
                            TemelDerinligi = 2.0,
                            TemelTuru = "Radye",
                            UzunlukBirimi = "m"
                        });
                });

            modelBuilder.Entity("DonDonat.WPF.Persistence.Entities.DonatiEntity", b =>
                {
                    b.HasOne("DonDonat.WPF.Persistence.Entities.ProjeBilgileriEntity", "Proje")
                        .WithMany("Donatilar")
                        .HasForeignKey("ProjeId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Proje");
                });

            modelBuilder.Entity("DonDonat.WPF.Persistence.Entities.ProjeBilgileriEntity", b =>
                {
                    b.Navigation("Donatilar");
                });
#pragma warning restore 612, 618
        }
    }
}

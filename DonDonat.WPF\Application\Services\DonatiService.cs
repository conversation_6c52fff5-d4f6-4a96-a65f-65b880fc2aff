using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using DonDonat.WPF.Application.Interfaces;
using DonDonat.WPF.Application.Mappings;
using DonDonat.WPF.Domain.Entities;
using DonDonat.WPF.Infrastructure.Parsers;
using DonDonat.WPF.Infrastructure.Repositories;
using DonDonat.WPF.Persistence;

namespace DonDonat.WPF.Application.Services
{
    /// <summary>
    /// Donatı işlemleri için uygulama servisi
    /// Business logic'i içerir
    /// </summary>
    public class DonatiService : IDonatiService
    {
        private readonly DWGParser _dwgParser;
        private readonly DonatiRepository _repository;
        private readonly DonDonatDbContext _context;

        public DonatiService()
        {
            _dwgParser = new DWGParser();

            // Veritabanı bağlantısı ve repository
            _context = new DonDonatDbContext();
            _repository = new DonatiRepository(_context);

            // Veritabanını oluştur
            InitializeDatabaseAsync().Wait();
        }

        /// <summary>
        /// Veritabanını initialize eder
        /// </summary>
        private async Task InitializeDatabaseAsync()
        {
            try
            {
                await _context.Database.EnsureCreatedAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Veritabanı oluşturulurken hata: {ex.Message}");
            }
        }

        #region IDonatiService Implementation

        /// <summary>
        /// Tüm donatıları getirir
        /// </summary>
        public async Task<List<Donati>> GetAllDonatiAsync()
        {
            try
            {
                var entities = await _repository.GetAllDonatiAsync();
                return DonatiMapper.ToModelList(entities);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Donatılar getirilirken hata: {ex.Message}");
                return new List<Donati>();
            }
        }

        /// <summary>
        /// ID'ye göre donatı getirir
        /// </summary>
        public async Task<Donati?> GetDonatiByIdAsync(Guid id)
        {
            try
            {
                var entity = await _repository.GetDonatiByIdAsync(id);
                return entity != null ? DonatiMapper.ToModel(entity) : null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Donatı getirilirken hata: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Proje ID'sine göre donatıları getirir
        /// </summary>
        public async Task<List<Donati>> GetDonatiByProjeIdAsync(Guid projeId)
        {
            try
            {
                var entities = await _repository.GetDonatiByProjeIdAsync(projeId);
                return DonatiMapper.ToModelList(entities);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Proje donatıları getirilirken hata: {ex.Message}");
                return new List<Donati>();
            }
        }

        /// <summary>
        /// Yeni donatı ekler
        /// </summary>
        public async Task<Donati> AddDonatiAsync(Donati donati)
        {
            try
            {
                if (donati == null)
                    throw new ArgumentNullException(nameof(donati));

                // Validation kontrolü
                var (isValid, errorMessages) = ValidateDonati(donati);
                if (!isValid)
                {
                    throw new ValidationException($"Donatı geçersiz: {string.Join(", ", errorMessages)}");
                }

                var entity = DonatiMapper.ToEntity(donati);
                var savedEntity = await _repository.AddDonatiAsync(entity);
                return DonatiMapper.ToModel(savedEntity);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Donatı eklenirken hata: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Donatı günceller
        /// </summary>
        public async Task<Donati> UpdateDonatiAsync(Donati donati)
        {
            try
            {
                if (donati == null)
                    throw new ArgumentNullException(nameof(donati));

                // Validation kontrolü
                var (isValid, errorMessages) = ValidateDonati(donati);
                if (!isValid)
                {
                    throw new ValidationException($"Donatı geçersiz: {string.Join(", ", errorMessages)}");
                }

                var entity = DonatiMapper.ToEntity(donati);
                var updatedEntity = await _repository.UpdateDonatiAsync(entity);
                return DonatiMapper.ToModel(updatedEntity);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Donatı güncellenirken hata: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Donatı siler
        /// </summary>
        public async Task<bool> DeleteDonatiAsync(Guid id)
        {
            try
            {
                return await _repository.DeleteDonatiAsync(id);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Donatı silinirken hata: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Birden fazla donatı ekler
        /// </summary>
        public async Task<List<Donati>> AddMultipleDonatiAsync(List<Donati> donatilar)
        {
            try
            {
                if (donatilar == null || !donatilar.Any())
                    throw new ArgumentException("Donatı listesi boş olamaz", nameof(donatilar));

                // Tüm donatıları validate et
                foreach (var donati in donatilar)
                {
                    var (isValid, errorMessages) = ValidateDonati(donati);
                    if (!isValid)
                    {
                        throw new ValidationException($"Donatı geçersiz: {string.Join(", ", errorMessages)}");
                    }
                }

                var entities = DonatiMapper.ToEntityList(donatilar);
                var savedEntities = await _repository.AddMultipleDonatiAsync(entities);
                return DonatiMapper.ToModelList(savedEntities);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Çoklu donatı eklenirken hata: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Çapa göre donatıları getirir
        /// </summary>
        public async Task<List<Donati>> GetDonatiByCapAsync(int cap)
        {
            try
            {
                var entities = await _repository.GetDonatiByCapAsync(cap);
                return DonatiMapper.ToModelList(entities);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Çap bazlı donatılar getirilirken hata: {ex.Message}");
                return new List<Donati>();
            }
        }

        /// <summary>
        /// Yapı elemanına göre donatıları getirir
        /// </summary>
        public async Task<List<Donati>> GetDonatiByYapiElemaniAsync(string yapiElemani)
        {
            try
            {
                var entities = await _repository.GetDonatiByYapiElemaniAsync(yapiElemani);
                return DonatiMapper.ToModelList(entities);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Yapı elemanı bazlı donatılar getirilirken hata: {ex.Message}");
                return new List<Donati>();
            }
        }

        /// <summary>
        /// Donatı türüne göre donatıları getirir
        /// </summary>
        public async Task<List<Donati>> GetDonatiByTurAsync(string tur)
        {
            try
            {
                var entities = await _repository.GetDonatiByTurAsync(tur);
                return DonatiMapper.ToModelList(entities);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Tür bazlı donatılar getirilirken hata: {ex.Message}");
                return new List<Donati>();
            }
        }

        /// <summary>
        /// Arama yapar
        /// </summary>
        public async Task<List<Donati>> SearchDonatiAsync(string searchTerm)
        {
            try
            {
                var entities = await _repository.SearchDonatiAsync(searchTerm);
                return DonatiMapper.ToModelList(entities);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Donatı arama yapılırken hata: {ex.Message}");
                return new List<Donati>();
            }
        }

        /// <summary>
        /// Sayfalama ile donatıları getirir
        /// </summary>
        public async Task<(List<Donati> Items, int TotalCount)> GetPagedDonatiAsync(
            int pageNumber, int pageSize, Guid? projeId = null)
        {
            try
            {
                var (entities, totalCount) = await _repository.GetPagedDonatiAsync(pageNumber, pageSize, projeId);
                var models = DonatiMapper.ToModelList(entities);
                return (models, totalCount);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Sayfalı donatılar getirilirken hata: {ex.Message}");
                return (new List<Donati>(), 0);
            }
        }

        #endregion

        /// <summary>
        /// DXF dosyasından donatıları yükler
        /// </summary>
        public async Task<List<Donati>> LoadDonatilarFromDXFAsync(string filePath)
        {
            if (string.IsNullOrWhiteSpace(filePath))
                throw new ArgumentException("Dosya yolu boş olamaz", nameof(filePath));

            var donatilar = await _dwgParser.ParseDXFFileAsync(filePath);

            // Business rules uygula
            ValidateDonatilar(donatilar);
            OptimizeDonatilar(donatilar);

            return donatilar;
        }

        /// <summary>
        /// Donatı istatistiklerini getirir
        /// </summary>
        public async Task<DonatiMetrajOzeti> GetDonatiIstatistikleriAsync(Guid? projeId = null)
        {
            try
            {
                var istatistikler = await _repository.GetDonatiIstatistikleriAsync(projeId);

                return new DonatiMetrajOzeti
                {
                    ToplamAdet = istatistikler.ToplamAdet,
                    ToplamUzunluk = istatistikler.ToplamUzunluk,
                    ToplamAgirlik = istatistikler.ToplamAgirlik,
                    CapBazindaOzet = new Dictionary<int, CapOzeti>(), // Şimdilik boş, gerekirse ayrı sorgu yapılabilir
                    YapiElemaniBazindaOzet = istatistikler.YapiElemaniDagilimi.ToDictionary(
                        kvp => kvp.Key,
                        kvp => new YapiElemaniOzeti
                        {
                            YapiElemani = kvp.Key,
                            ToplamAdet = kvp.Value,
                            ToplamUzunluk = 0,
                            ToplamAgirlik = 0
                        })
                };
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Donatı istatistikleri getirilirken hata: {ex.Message}");
                return new DonatiMetrajOzeti();
            }
        }

        /// <summary>
        /// Donatı listesinin toplam metrajını hesaplar
        /// </summary>
        public DonatiMetrajOzeti CalculateMetrajOzeti(IEnumerable<Donati> donatilar)
        {
            if (donatilar == null)
                return new DonatiMetrajOzeti();

            var donatiList = donatilar.ToList();

            return new DonatiMetrajOzeti
            {
                ToplamAdet = donatiList.Sum(d => d.Adet),
                ToplamUzunluk = donatiList.Sum(d => d.ToplamUzunluk),
                ToplamAgirlik = donatiList.Sum(d => d.ToplamAgirlik),
                CapBazindaOzet = CalculateCapBazindaOzet(donatiList),
                YapiElemaniBazindaOzet = CalculateYapiElemaniBazindaOzet(donatiList)
            };
        }

        /// <summary>
        /// Donatıları doğrular
        /// </summary>
        public (bool IsValid, string[] ErrorMessages) ValidateDonati(Donati donati)
        {
            if (donati == null)
                return (false, new[] { "Donatı null olamaz" });

            var errors = new List<string>();

            if (donati.Cap < 6 || donati.Cap > 50)
                errors.Add($"Geçersiz donatı çapı: {donati.Cap}mm. Çap 6-50mm arasında olmalıdır.");

            if (donati.Boy <= 0 || donati.Boy > 20)
                errors.Add($"Geçersiz donatı boyu: {donati.Boy}m. Boy 0-20m arasında olmalıdır.");

            if (donati.Adet <= 0)
                errors.Add($"Geçersiz donatı adedi: {donati.Adet}. Adet 0'dan büyük olmalıdır.");

            if (string.IsNullOrWhiteSpace(donati.Tur))
                errors.Add("Donatı türü boş olamaz.");

            return (errors.Count == 0, errors.ToArray());
        }

        /// <summary>
        /// Varsayılan donatı oluşturur
        /// </summary>
        public Donati CreateDefaultDonati()
        {
            return new Donati
            {
                Id = Guid.NewGuid(),
                Cap = 12,
                Boy = 1.0,
                Adet = 1,
                Tur = "NERVÜRLÜ",
                YapiElemani = "KİRİŞ",
                Konum = "ÜST",
                DonatiSinifi = "S420",
                OlusturulmaTarihi = DateTime.Now,
                GuncellenmeTarihi = DateTime.Now
            };
        }

        /// <summary>
        /// Yeni donatı oluşturur
        /// </summary>
        public Donati CreateDonati(int cap, double boy, int adet, string tur, string yapiElemani, string konum = "")
        {
            var donati = new Donati
            {
                Id = Guid.NewGuid(),
                Cap = cap,
                Boy = boy,
                Adet = adet,
                Tur = tur,
                YapiElemani = yapiElemani,
                Konum = konum,
                DonatiSinifi = "S420",
                OlusturulmaTarihi = DateTime.Now,
                GuncellenmeTarihi = DateTime.Now
            };

            var (isValid, errorMessages) = ValidateDonati(donati);
            if (!isValid)
            {
                throw new ValidationException($"Donatı geçersiz: {string.Join(", ", errorMessages)}");
            }
            return donati;
        }

        /// <summary>
        /// Donatı günceller (legacy method)
        /// </summary>
        public void UpdateDonati(Donati donati)
        {
            if (donati == null)
                throw new ArgumentNullException(nameof(donati));

            donati.GuncellenmeTarihi = DateTime.Now;
            var (isValid, errorMessages) = ValidateDonati(donati);
            if (!isValid)
            {
                throw new ValidationException($"Donatı geçersiz: {string.Join(", ", errorMessages)}");
            }
        }

        /// <summary>
        /// Benzer donatıları birleştirir
        /// </summary>
        public List<Donati> MergeSimilarDonatilar(List<Donati> donatilar)
        {
            var mergedDonatilar = new List<Donati>();

            var groups = donatilar.GroupBy(d => new 
            { 
                d.Cap, 
                d.Boy, 
                d.Tur, 
                d.YapiElemani, 
                d.DonatiSinifi 
            });

            foreach (var group in groups)
            {
                var firstDonati = group.First();
                var totalAdet = group.Sum(d => d.Adet);

                var mergedDonati = new Donati
                {
                    Id = Guid.NewGuid(),
                    Cap = firstDonati.Cap,
                    Boy = firstDonati.Boy,
                    Adet = totalAdet,
                    Tur = firstDonati.Tur,
                    YapiElemani = firstDonati.YapiElemani,
                    Konum = firstDonati.Konum,
                    DonatiSinifi = firstDonati.DonatiSinifi,
                    OlusturulmaTarihi = DateTime.Now,
                    GuncellenmeTarihi = DateTime.Now
                };

                mergedDonatilar.Add(mergedDonati);
            }

            return mergedDonatilar;
        }

        #region Private Methods

        /// <summary>
        /// Donatı listesini doğrular
        /// </summary>
        private void ValidateDonatilar(List<Donati> donatilar)
        {
            foreach (var donati in donatilar)
            {
                var (isValid, errorMessages) = ValidateDonati(donati);
                if (!isValid)
                {
                    throw new ValidationException($"Donatı geçersiz: {string.Join(", ", errorMessages)}");
                }
            }
        }



        /// <summary>
        /// Donatı listesini optimize eder
        /// </summary>
        private void OptimizeDonatilar(List<Donati> donatilar)
        {
            // Standart boyları kontrol et ve düzelt
            foreach (var donati in donatilar)
            {
                // 12m'den uzun donatıları standart uzunluklara böl
                if (donati.Boy > 12.0)
                {
                    var standartBoy = 12.0;
                    var yeniAdet = (int)Math.Ceiling(donati.Boy / standartBoy) * donati.Adet;
                    donati.Boy = standartBoy;
                    donati.Adet = yeniAdet;
                }
            }
        }

        /// <summary>
        /// Çap bazında özet hesaplar
        /// </summary>
        private Dictionary<int, CapOzeti> CalculateCapBazindaOzet(List<Donati> donatilar)
        {
            return donatilar
                .GroupBy(d => d.Cap)
                .ToDictionary(
                    g => g.Key,
                    g => new CapOzeti
                    {
                        Cap = g.Key,
                        ToplamAdet = g.Sum(d => d.Adet),
                        ToplamUzunluk = g.Sum(d => d.ToplamUzunluk),
                        ToplamAgirlik = g.Sum(d => d.ToplamAgirlik)
                    }
                );
        }

        /// <summary>
        /// Yapı elemanı bazında özet hesaplar
        /// </summary>
        private Dictionary<string, YapiElemaniOzeti> CalculateYapiElemaniBazindaOzet(List<Donati> donatilar)
        {
            return donatilar
                .GroupBy(d => d.YapiElemani)
                .ToDictionary(
                    g => g.Key,
                    g => new YapiElemaniOzeti
                    {
                        YapiElemani = g.Key,
                        ToplamAdet = g.Sum(d => d.Adet),
                        ToplamUzunluk = g.Sum(d => d.ToplamUzunluk),
                        ToplamAgirlik = g.Sum(d => d.ToplamAgirlik)
                    }
                );
        }

        #endregion

        /// <summary>
        /// Dispose pattern implementation
        /// </summary>
        public void Dispose()
        {
            _context?.Dispose();
        }
    }

    /// <summary>
    /// Donatı metraj özeti
    /// </summary>
    public class DonatiMetrajOzeti
    {
        public int ToplamAdet { get; set; }
        public double ToplamUzunluk { get; set; }
        public double ToplamAgirlik { get; set; }
        public Dictionary<int, CapOzeti> CapBazindaOzet { get; set; } = new();
        public Dictionary<string, YapiElemaniOzeti> YapiElemaniBazindaOzet { get; set; } = new();
    }

    /// <summary>
    /// Çap bazında özet
    /// </summary>
    public class CapOzeti
    {
        public int Cap { get; set; }
        public int ToplamAdet { get; set; }
        public double ToplamUzunluk { get; set; }
        public double ToplamAgirlik { get; set; }
    }

    /// <summary>
    /// Yapı elemanı bazında özet
    /// </summary>
    public class YapiElemaniOzeti
    {
        public string YapiElemani { get; set; } = "";
        public int ToplamAdet { get; set; }
        public double ToplamUzunluk { get; set; }
        public double ToplamAgirlik { get; set; }
    }
}

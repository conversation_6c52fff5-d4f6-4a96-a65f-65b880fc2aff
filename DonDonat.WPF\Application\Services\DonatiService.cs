using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using DonDonat.WPF.Domain.Entities;
using DonDonat.WPF.Infrastructure.Parsers;

namespace DonDonat.WPF.Application.Services
{
    /// <summary>
    /// Donatı işlemleri için uygulama servisi
    /// Business logic'i içerir
    /// </summary>
    public class DonatiService
    {
        private readonly DWGParser _dwgParser;

        public DonatiService()
        {
            _dwgParser = new DWGParser();
        }

        /// <summary>
        /// DXF dosyasından donatıları yükler
        /// </summary>
        public async Task<List<Donati>> LoadDonatilarFromDXFAsync(string filePath)
        {
            if (string.IsNullOrWhiteSpace(filePath))
                throw new ArgumentException("Dosya yolu boş olamaz", nameof(filePath));

            var donatilar = await _dwgParser.ParseDXFFileAsync(filePath);
            
            // Business rules uygula
            ValidateDonatilar(donatilar);
            OptimizeDonatilar(donatilar);
            
            return donatilar;
        }

        /// <summary>
        /// Donatı listesinin toplam metrajını hesaplar
        /// </summary>
        public DonatiMetrajOzeti CalculateMetrajOzeti(IEnumerable<Donati> donatilar)
        {
            if (donatilar == null)
                return new DonatiMetrajOzeti();

            var donatiList = donatilar.ToList();

            return new DonatiMetrajOzeti
            {
                ToplamAdet = donatiList.Sum(d => d.Adet),
                ToplamUzunluk = donatiList.Sum(d => d.ToplamUzunluk),
                ToplamAgirlik = donatiList.Sum(d => d.ToplamAgirlik),
                CapBazindaOzet = CalculateCapBazindaOzet(donatiList),
                YapiElemaniBazindaOzet = CalculateYapiElemaniBazindaOzet(donatiList)
            };
        }

        /// <summary>
        /// Yeni donatı oluşturur
        /// </summary>
        public Donati CreateDonati(int cap, double boy, int adet, string tur, string yapiElemani, string konum = "")
        {
            var donati = new Donati
            {
                Id = Guid.NewGuid(),
                Cap = cap,
                Boy = boy,
                Adet = adet,
                Tur = tur,
                YapiElemani = yapiElemani,
                Konum = konum,
                DonatiSinifi = "S420",
                OlusturulmaTarihi = DateTime.Now,
                GuncellenmeTarihi = DateTime.Now
            };

            ValidateDonati(donati);
            return donati;
        }

        /// <summary>
        /// Donatı günceller
        /// </summary>
        public void UpdateDonati(Donati donati)
        {
            if (donati == null)
                throw new ArgumentNullException(nameof(donati));

            donati.GuncellenmeTarihi = DateTime.Now;
            ValidateDonati(donati);
        }

        /// <summary>
        /// Benzer donatıları birleştirir
        /// </summary>
        public List<Donati> MergeSimilarDonatilar(List<Donati> donatilar)
        {
            var mergedDonatilar = new List<Donati>();

            var groups = donatilar.GroupBy(d => new 
            { 
                d.Cap, 
                d.Boy, 
                d.Tur, 
                d.YapiElemani, 
                d.DonatiSinifi 
            });

            foreach (var group in groups)
            {
                var firstDonati = group.First();
                var totalAdet = group.Sum(d => d.Adet);

                var mergedDonati = new Donati
                {
                    Id = Guid.NewGuid(),
                    Cap = firstDonati.Cap,
                    Boy = firstDonati.Boy,
                    Adet = totalAdet,
                    Tur = firstDonati.Tur,
                    YapiElemani = firstDonati.YapiElemani,
                    Konum = firstDonati.Konum,
                    DonatiSinifi = firstDonati.DonatiSinifi,
                    OlusturulmaTarihi = DateTime.Now,
                    GuncellenmeTarihi = DateTime.Now
                };

                mergedDonatilar.Add(mergedDonati);
            }

            return mergedDonatilar;
        }

        #region Private Methods

        /// <summary>
        /// Donatı listesini doğrular
        /// </summary>
        private void ValidateDonatilar(List<Donati> donatilar)
        {
            foreach (var donati in donatilar)
            {
                ValidateDonati(donati);
            }
        }

        /// <summary>
        /// Tek donatıyı doğrular
        /// </summary>
        private void ValidateDonati(Donati donati)
        {
            if (donati.Cap < 6 || donati.Cap > 50)
                throw new ArgumentException($"Geçersiz donatı çapı: {donati.Cap}mm. Çap 6-50mm arasında olmalıdır.");

            if (donati.Boy <= 0 || donati.Boy > 20)
                throw new ArgumentException($"Geçersiz donatı boyu: {donati.Boy}m. Boy 0-20m arasında olmalıdır.");

            if (donati.Adet <= 0)
                throw new ArgumentException($"Geçersiz donatı adedi: {donati.Adet}. Adet 0'dan büyük olmalıdır.");

            if (string.IsNullOrWhiteSpace(donati.Tur))
                throw new ArgumentException("Donatı türü boş olamaz.");
        }

        /// <summary>
        /// Donatı listesini optimize eder
        /// </summary>
        private void OptimizeDonatilar(List<Donati> donatilar)
        {
            // Standart boyları kontrol et ve düzelt
            foreach (var donati in donatilar)
            {
                // 12m'den uzun donatıları standart uzunluklara böl
                if (donati.Boy > 12.0)
                {
                    var standartBoy = 12.0;
                    var yeniAdet = (int)Math.Ceiling(donati.Boy / standartBoy) * donati.Adet;
                    donati.Boy = standartBoy;
                    donati.Adet = yeniAdet;
                }
            }
        }

        /// <summary>
        /// Çap bazında özet hesaplar
        /// </summary>
        private Dictionary<int, CapOzeti> CalculateCapBazindaOzet(List<Donati> donatilar)
        {
            return donatilar
                .GroupBy(d => d.Cap)
                .ToDictionary(
                    g => g.Key,
                    g => new CapOzeti
                    {
                        Cap = g.Key,
                        ToplamAdet = g.Sum(d => d.Adet),
                        ToplamUzunluk = g.Sum(d => d.ToplamUzunluk),
                        ToplamAgirlik = g.Sum(d => d.ToplamAgirlik)
                    }
                );
        }

        /// <summary>
        /// Yapı elemanı bazında özet hesaplar
        /// </summary>
        private Dictionary<string, YapiElemaniOzeti> CalculateYapiElemaniBazindaOzet(List<Donati> donatilar)
        {
            return donatilar
                .GroupBy(d => d.YapiElemani)
                .ToDictionary(
                    g => g.Key,
                    g => new YapiElemaniOzeti
                    {
                        YapiElemani = g.Key,
                        ToplamAdet = g.Sum(d => d.Adet),
                        ToplamUzunluk = g.Sum(d => d.ToplamUzunluk),
                        ToplamAgirlik = g.Sum(d => d.ToplamAgirlik)
                    }
                );
        }

        #endregion
    }

    /// <summary>
    /// Donatı metraj özeti
    /// </summary>
    public class DonatiMetrajOzeti
    {
        public int ToplamAdet { get; set; }
        public double ToplamUzunluk { get; set; }
        public double ToplamAgirlik { get; set; }
        public Dictionary<int, CapOzeti> CapBazindaOzet { get; set; } = new();
        public Dictionary<string, YapiElemaniOzeti> YapiElemaniBazindaOzet { get; set; } = new();
    }

    /// <summary>
    /// Çap bazında özet
    /// </summary>
    public class CapOzeti
    {
        public int Cap { get; set; }
        public int ToplamAdet { get; set; }
        public double ToplamUzunluk { get; set; }
        public double ToplamAgirlik { get; set; }
    }

    /// <summary>
    /// Yapı elemanı bazında özet
    /// </summary>
    public class YapiElemaniOzeti
    {
        public string YapiElemani { get; set; } = "";
        public int ToplamAdet { get; set; }
        public double ToplamUzunluk { get; set; }
        public double ToplamAgirlik { get; set; }
    }
}

# DonDonat <PERSON>tım Gereksinimleri

## 🛠️ Gerekli Araçlar ve Eklentiler

### Visual Studio 2022 Gereksinimleri

#### Temel Workload'lar
- ✅ **.NET desktop development** - WPF uygulaması için
- ✅ **Universal Windows Platform development** - MSIX paketleme için
- ✅ **Desktop development with C++** - Native bağımlılıklar için (opsiyonel)

#### Bireysel Bileşenler
- ✅ **Windows 10/11 SDK (latest)** - MSIX paketleme için
- ✅ **MSIX Packaging Tools** - MSIX paket oluşturma
- ✅ **ClickOnce Publishing** - ClickOnce dağıtım
- ✅ **NuGet package manager** - Paket yönetimi
- ✅ **Git for Windows** - Versiyon kontrolü

### Ek Araçlar

#### Windows App SDK
```powershell
# Windows App SDK yükleme
winget install Microsoft.WindowsAppSDK
```

#### ImageMagick (İmaj işleme için)
```powershell
# ImageMagick yükleme
winget install ImageMagick.ImageMagick
```

#### Windows SDK
- **Minimum:** Windows 10 SDK (10.0.19041.0)
- **Önerilen:** Windows 11 SDK (10.0.22621.0)

## 📦 Paketleme Seçenekleri

### 1. MSIX Paketleme (Önerilen)

#### Avantajları
- ✅ Modern Windows paketleme formatı
- ✅ Microsoft Store uyumluluğu
- ✅ Otomatik güncelleme desteği
- ✅ Güvenli kurulum/kaldırma
- ✅ Dosya sistemi sanallaştırması
- ✅ Registry sanallaştırması

#### Gereksinimler
- Windows 10 1809 veya üzeri
- Visual Studio 2019/2022 + Windows App SDK
- MSIX Packaging Tools

#### Kurulum Adımları
```powershell
# 1. MSIX imajları oluştur
.\DonDonat.Package\Scripts\Generate-AppImages.ps1 -SourceIconPath "DonDonat.ico"

# 2. MSIX paketi oluştur
.\DonDonat.Package\Scripts\Build-MSIX.ps1 -Configuration Release -Platform x64

# 3. Paketi test et
Add-AppxPackage -Path ".\AppPackages\DonDonat_*******_x64.msix"
```

### 2. ClickOnce Dağıtımı

#### Avantajları
- ✅ Kolay web tabanlı dağıtım
- ✅ Otomatik güncelleme
- ✅ Kısmi güven desteği
- ✅ Rollback özelliği
- ✅ Offline kurulum desteği

#### Gereksinimler
- .NET Framework/Core runtime
- IIS veya herhangi bir web sunucusu
- SSL sertifikası (HTTPS için)

#### Kurulum Adımları
```powershell
# 1. ClickOnce paketi oluştur
.\DonDonat.WPF\Scripts\Build-ClickOnce.ps1 -Configuration Release -Version "*******"

# 2. Web sunucusuna yükle
# ClickOnce klasörünü web sunucunuza kopyalayın

# 3. Kullanıcılar index.html'den kurulum yapabilir
```

## 🔧 Yapılandırma Dosyaları

### Package.appxmanifest (MSIX)
```xml
<!-- Uygulama kimliği ve yayımcı bilgileri -->
<Identity Name="DonDonat.App" Publisher="CN=YourCompany" Version="*******" />

<!-- Dosya ilişkilendirmeleri -->
<uap:FileTypeAssociation Name="dwgfiles">
  <uap:SupportedFileTypes>
    <uap:FileType>.dwg</uap:FileType>
    <uap:FileType>.dxf</uap:FileType>
  </uap:SupportedFileTypes>
</uap:FileTypeAssociation>
```

### ClickOnce.pubxml
```xml
<!-- Güncelleme ayarları -->
<UpdateEnabled>true</UpdateEnabled>
<UpdateMode>Foreground</UpdateMode>
<UpdateInterval>7</UpdateInterval>
<UpdateIntervalUnits>Days</UpdateIntervalUnits>
```

## 🎯 Dağıtım Stratejileri

### Kurumsal Dağıtım
1. **MSIX + App Installer**
   - Merkezi güncelleme kontrolü
   - Group Policy ile dağıtım
   - WSUS entegrasyonu

2. **ClickOnce + Intranet**
   - İç ağ üzerinden dağıtım
   - Otomatik güncelleme
   - Kullanıcı bazlı kurulum

### Genel Kullanıcı Dağıtımı
1. **Microsoft Store** (MSIX)
   - En güvenli dağıtım yöntemi
   - Otomatik güncelleme
   - Geniş erişim

2. **Web Sitesi** (ClickOnce)
   - Direkt indirme
   - Özelleştirilebilir kurulum sayfası
   - Analytics desteği

## 🔐 Güvenlik ve İmzalama

### Kod İmzalama Sertifikası
```powershell
# Self-signed sertifika oluşturma (test için)
New-SelfSignedCertificate -Type CodeSigningCert -Subject "CN=YourCompany" -KeyUsage DigitalSignature -FriendlyName "DonDonat Code Signing" -CertStoreLocation "Cert:\CurrentUser\My"

# Üretim için CA'dan sertifika alın
```

### MSIX İmzalama
```xml
<!-- DonDonat.Package.wapproj -->
<PropertyGroup>
  <AppxPackageSigningEnabled>true</AppxPackageSigningEnabled>
  <PackageCertificateKeyFile>DonDonat_TemporaryKey.pfx</PackageCertificateKeyFile>
</PropertyGroup>
```

### ClickOnce İmzalama
```xml
<!-- ClickOnce.pubxml -->
<PropertyGroup>
  <SignManifests>true</SignManifests>
  <ManifestCertificateThumbprint>CERTIFICATE_THUMBPRINT</ManifestCertificateThumbprint>
</PropertyGroup>
```

## 🚀 Otomatik Dağıtım (CI/CD)

### GitHub Actions Örneği
```yaml
name: Build and Deploy DonDonat

on:
  push:
    tags: ['v*']

jobs:
  build:
    runs-on: windows-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup .NET
      uses: actions/setup-dotnet@v3
      with:
        dotnet-version: '9.0.x'
    
    - name: Build MSIX
      run: .\Build-DonDonat.ps1 -PackageType MSIX -Configuration Release
    
    - name: Upload artifacts
      uses: actions/upload-artifact@v3
      with:
        name: DonDonat-Packages
        path: .\Packages\
```

### Azure DevOps Pipeline
```yaml
trigger:
  tags:
    include:
    - v*

pool:
  vmImage: 'windows-latest'

steps:
- task: UseDotNet@2
  inputs:
    version: '9.0.x'

- powershell: |
    .\Build-DonDonat.ps1 -PackageType Both -Configuration Release
  displayName: 'Build Packages'

- task: PublishBuildArtifacts@1
  inputs:
    pathToPublish: '.\Packages'
    artifactName: 'DonDonat-Packages'
```

## 📋 Test Checklist

### MSIX Test
- [ ] Paket başarıyla oluşturuldu
- [ ] Uygulama kurulumu çalışıyor
- [ ] Dosya ilişkilendirmeleri aktif
- [ ] Masaüstü kısayolu oluşturuldu
- [ ] Başlat menüsünde görünüyor
- [ ] Kaldırma işlemi çalışıyor

### ClickOnce Test
- [ ] Setup.exe çalışıyor
- [ ] Web sayfasından kurulum yapılabiliyor
- [ ] Otomatik güncelleme çalışıyor
- [ ] Offline kurulum mümkün
- [ ] Rollback özelliği çalışıyor

## 🔧 Sorun Giderme

### Yaygın Hatalar

#### MSIX Hataları
```
Error: Package validation failed
Çözüm: Package.appxmanifest dosyasını kontrol edin
```

```
Error: Certificate not found
Çözüm: Kod imzalama sertifikası yükleyin
```

#### ClickOnce Hataları
```
Error: Application requires .NET Framework
Çözüm: Prerequisites'i kontrol edin
```

```
Error: Unable to download application
Çözüm: Web sunucusu MIME types ayarlarını kontrol edin
```

### Log Dosyaları
- **MSIX:** `%LOCALAPPDATA%\Packages\DonDonat.App_*\LocalState\logs\`
- **ClickOnce:** `%USERPROFILE%\AppData\Local\Apps\2.0\`

## 📞 Destek

### Dokümantasyon
- [MSIX Packaging](https://docs.microsoft.com/en-us/windows/msix/)
- [ClickOnce Deployment](https://docs.microsoft.com/en-us/visualstudio/deployment/clickonce-security-and-deployment)
- [Windows App SDK](https://docs.microsoft.com/en-us/windows/apps/windows-app-sdk/)

### Topluluk
- [MSIX Tech Community](https://techcommunity.microsoft.com/t5/msix/ct-p/MSIX)
- [.NET Community](https://dotnet.microsoft.com/platform/community)

<?xml version="1.0" encoding="utf-8"?>
<Package
  xmlns="http://schemas.microsoft.com/appx/manifest/foundation/windows10"
  xmlns:mp="http://schemas.microsoft.com/appx/2014/phone/manifest"
  xmlns:uap="http://schemas.microsoft.com/appx/manifest/uap/windows10"
  xmlns:rescap="http://schemas.microsoft.com/appx/manifest/foundation/windows10/restrictedcapabilities"
  IgnorableNamespaces="uap rescap">

  <Identity
    Name="DonDonat.App"
    Publisher="CN=YourCompany"
    Version="*******" />

  <mp:PhoneIdentity PhoneProductId="6233c7dd-cc67-4cd1-84b2-8d58c4d662ff" PhonePublisherId="00000000-0000-0000-0000-000000000000"/>

  <Properties>
    <DisplayName>DonDonat - Betonarme Donatı Metraj Hesaplama</DisplayName>
    <PublisherDisplayName>YourCompany</PublisherDisplayName>
    <Logo>Images\StoreLogo.png</Logo>
    <Description>AutoCAD DWG dosyalarından betonarme donatı metrajı hesaplayan profesyonel masaüstü uygulaması</Description>
  </Properties>

  <Dependencies>
    <TargetDeviceFamily Name="Windows.Universal" MinVersion="10.0.19041.0" MaxVersionTested="10.0.22621.0" />
    <TargetDeviceFamily Name="Windows.Desktop" MinVersion="10.0.19041.0" MaxVersionTested="10.0.22621.0" />
  </Dependencies>

  <Resources>
    <Resource Language="x-generate"/>
  </Resources>

  <Applications>
    <Application Id="App"
      Executable="$targetnametoken$.exe"
      EntryPoint="$targetentrypoint$">
      <uap:VisualElements
        DisplayName="DonDonat"
        Description="Betonarme Donatı Metraj Hesaplama Uygulaması"
        BackgroundColor="transparent"
        Square150x150Logo="Images\Square150x150Logo.png"
        Square44x44Logo="Images\Square44x44Logo.png">
        <uap:DefaultTile Wide310x150Logo="Images\Wide310x150Logo.png" />
        <uap:SplashScreen Image="Images\SplashScreen.png" />
      </uap:VisualElements>
      
      <Extensions>
        <uap:Extension Category="windows.fileTypeAssociation">
          <uap:FileTypeAssociation Name="dwgfiles">
            <uap:DisplayName>DWG Dosyaları</uap:DisplayName>
            <uap:Logo>Images\Square44x44Logo.png</uap:Logo>
            <uap:InfoTip>AutoCAD DWG dosyası</uap:InfoTip>
            <uap:SupportedFileTypes>
              <uap:FileType>.dwg</uap:FileType>
              <uap:FileType>.dxf</uap:FileType>
            </uap:SupportedFileTypes>
          </uap:FileTypeAssociation>
        </uap:Extension>
        
        <uap:Extension Category="windows.protocol">
          <uap:Protocol Name="dondonat">
            <uap:DisplayName>DonDonat Protocol</uap:DisplayName>
          </uap:Protocol>
        </uap:Extension>
      </Extensions>
    </Application>
  </Applications>

  <Capabilities>
    <rescap:Capability Name="runFullTrust" />
    <Capability Name="internetClient" />
    <uap:Capability Name="documentsLibrary" />
    <uap:Capability Name="removableStorage" />
  </Capabilities>
</Package>

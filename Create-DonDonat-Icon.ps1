# DonDonat İkon Oluşturucu Script
# Bu script, DonDonat uygulaması için basit bir ikon oluşturur

param(
    [Parameter(Mandatory=$false)]
    [string]$OutputPath = "DonDonat.WPF\Resources\DonDonat.ico",
    
    [Parameter(Mandatory=$false)]
    [string]$Size = "256x256",
    
    [Parameter(Mandatory=$false)]
    [string]$BackgroundColor = "#2C3E50",
    
    [Parameter(Mandatory=$false)]
    [string]$TextColor = "#ECF0F1"
)

# Renkli çıktı fonksiyonları
function Write-Success { param($Message) Write-Host $Message -ForegroundColor Green }
function Write-Info { param($Message) Write-Host $Message -ForegroundColor Cyan }
function Write-Warning { param($Message) Write-Host $Message -ForegroundColor Yellow }
function Write-Error { param($Message) Write-Host $Message -ForegroundColor Red }

Write-Info "🎨 DonDonat İkon Oluşturucu"
Write-Info "=========================="
Write-Info "Çıktı: $OutputPath"
Write-Info "Boyut: $Size"
Write-Info "Arka Plan: $BackgroundColor"
Write-Info "Metin: $TextColor"

try {
    # ImageMagick kontrolü
    if (-not (Get-Command "magick" -ErrorAction SilentlyContinue)) {
        Write-Warning "⚠️ ImageMagick bulunamadı. Alternatif yöntemler deneniyor..."
        
        # .NET ile basit ikon oluşturma
        Add-Type -AssemblyName System.Drawing
        Add-Type -AssemblyName System.Windows.Forms
        
        # Bitmap oluştur
        $bitmap = New-Object System.Drawing.Bitmap(256, 256)
        $graphics = [System.Drawing.Graphics]::FromImage($bitmap)
        
        # Arka plan
        $bgBrush = New-Object System.Drawing.SolidBrush([System.Drawing.ColorTranslator]::FromHtml($BackgroundColor))
        $graphics.FillRectangle($bgBrush, 0, 0, 256, 256)
        
        # Metin
        $font = New-Object System.Drawing.Font("Arial", 48, [System.Drawing.FontStyle]::Bold)
        $textBrush = New-Object System.Drawing.SolidBrush([System.Drawing.ColorTranslator]::FromHtml($TextColor))
        $text = "DD"
        
        # Metin boyutunu ölç
        $textSize = $graphics.MeasureString($text, $font)
        $x = (256 - $textSize.Width) / 2
        $y = (256 - $textSize.Height) / 2
        
        # Metni çiz
        $graphics.DrawString($text, $font, $textBrush, $x, $y)
        
        # İnşaat simgesi ekle (basit çizgiler)
        $pen = New-Object System.Drawing.Pen([System.Drawing.ColorTranslator]::FromHtml($TextColor), 4)
        
        # Çubuk çizgileri
        $graphics.DrawLine($pen, 50, 200, 206, 200)   # Alt çizgi
        $graphics.DrawLine($pen, 70, 180, 186, 180)   # Orta çizgi
        $graphics.DrawLine($pen, 90, 160, 166, 160)   # Üst çizgi
        
        # Dikey çubuklar
        $graphics.DrawLine($pen, 80, 160, 80, 200)    # Sol dikey
        $graphics.DrawLine($pen, 128, 140, 128, 200)  # Orta dikey
        $graphics.DrawLine($pen, 176, 160, 176, 200)  # Sağ dikey
        
        # Cleanup
        $graphics.Dispose()
        $bgBrush.Dispose()
        $textBrush.Dispose()
        $font.Dispose()
        $pen.Dispose()
        
        # ICO formatında kaydet
        $outputDir = Split-Path $OutputPath -Parent
        if (-not (Test-Path $outputDir)) {
            New-Item -ItemType Directory -Path $outputDir -Force | Out-Null
        }
        
        # PNG olarak kaydet (ICO için)
        $pngPath = $OutputPath -replace "\.ico$", ".png"
        $bitmap.Save($pngPath, [System.Drawing.Imaging.ImageFormat]::Png)
        
        # ICO dosyası oluştur (basit yöntem)
        $bitmap.Save($OutputPath, [System.Drawing.Imaging.ImageFormat]::Icon)
        
        $bitmap.Dispose()
        
        Write-Success "✅ İkon başarıyla oluşturuldu (.NET ile)"
        
    } else {
        # ImageMagick ile gelişmiş ikon oluşturma
        Write-Info "🎨 ImageMagick ile ikon oluşturuluyor..."
        
        $outputDir = Split-Path $OutputPath -Parent
        if (-not (Test-Path $outputDir)) {
            New-Item -ItemType Directory -Path $outputDir -Force | Out-Null
        }
        
        # Çoklu boyutlu ICO dosyası oluştur
        $tempFiles = @()
        $sizes = @("16", "32", "48", "64", "128", "256")
        
        foreach ($iconSize in $sizes) {
            $tempFile = "$env:TEMP\dondonat_$iconSize.png"
            $tempFiles += $tempFile
            
            # Her boyut için ikon oluştur
            magick -size "${iconSize}x${iconSize}" xc:$BackgroundColor `
                -gravity center `
                -pointsize $([math]::Floor($iconSize * 0.3)) `
                -fill $TextColor `
                -font Arial-Bold `
                -annotate +0+$([math]::Floor($iconSize * 0.1)) "DD" `
                -stroke $TextColor `
                -strokewidth $([math]::Max(1, [math]::Floor($iconSize / 64))) `
                -fill none `
                -draw "rectangle $([math]::Floor($iconSize * 0.1)),$([math]::Floor($iconSize * 0.7)) $([math]::Floor($iconSize * 0.9)),$([math]::Floor($iconSize * 0.9))" `
                -draw "line $([math]::Floor($iconSize * 0.2)),$([math]::Floor($iconSize * 0.8)) $([math]::Floor($iconSize * 0.8)),$([math]::Floor($iconSize * 0.8))" `
                "$tempFile"
        }
        
        # ICO dosyasını oluştur
        magick $tempFiles "$OutputPath"
        
        # PNG versiyonu da oluştur
        $pngPath = $OutputPath -replace "\.ico$", ".png"
        magick -size "256x256" xc:$BackgroundColor `
            -gravity center `
            -pointsize 72 `
            -fill $TextColor `
            -font Arial-Bold `
            -annotate +0+20 "DD" `
            -stroke $TextColor `
            -strokewidth 4 `
            -fill none `
            -draw "rectangle 25,180 231,230" `
            -draw "line 50,205 206,205" `
            "$pngPath"
        
        # Geçici dosyaları temizle
        foreach ($tempFile in $tempFiles) {
            if (Test-Path $tempFile) {
                Remove-Item $tempFile -Force
            }
        }
        
        Write-Success "✅ İkon başarıyla oluşturuldu (ImageMagick ile)"
    }
    
    # Dosya bilgilerini göster
    if (Test-Path $OutputPath) {
        $fileInfo = Get-Item $OutputPath
        Write-Info "📄 Dosya: $($fileInfo.Name)"
        Write-Info "📏 Boyut: $([math]::Round($fileInfo.Length / 1KB, 2)) KB"
        Write-Info "📅 Oluşturma: $($fileInfo.CreationTime)"
        
        # PNG dosyası da varsa
        $pngPath = $OutputPath -replace "\.ico$", ".png"
        if (Test-Path $pngPath) {
            $pngInfo = Get-Item $pngPath
            Write-Info "🖼️ PNG: $([math]::Round($pngInfo.Length / 1KB, 2)) KB"
        }
    }
    
    Write-Success "🎉 İkon oluşturma tamamlandı!"
    Write-Info "📁 Konum: $OutputPath"
    
    # Kullanım talimatları
    Write-Info "`n📋 Kullanım Talimatları:"
    Write-Host "1. Proje dosyasında ApplicationIcon ayarlandı" -ForegroundColor White
    Write-Host "2. MainWindow.xaml'de Icon özelliği ayarlandı" -ForegroundColor White
    Write-Host "3. Projeyi derleyip çalıştırın" -ForegroundColor White
    Write-Host "4. Özel ikon için DonDonat.ico dosyasını değiştirin" -ForegroundColor White

} catch {
    Write-Error "❌ İkon oluşturma hatası: $($_.Exception.Message)"
    Write-Error "Detaylar: $($_.Exception.StackTrace)"
    
    Write-Warning "`n💡 Alternatif Çözümler:"
    Write-Host "1. Online ikon oluşturucu kullanın (favicon.io, iconarchive.com)" -ForegroundColor Yellow
    Write-Host "2. Mevcut bir .ico dosyasını Resources klasörüne kopyalayın" -ForegroundColor Yellow
    Write-Host "3. ImageMagick yükleyin: winget install ImageMagick.ImageMagick" -ForegroundColor Yellow
    
    exit 1
}

Write-Success "`n✅ Script tamamlandı!"

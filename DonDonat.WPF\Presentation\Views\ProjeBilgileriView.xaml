<UserControl x:Class="DonDonat.WPF.Presentation.Views.ProjeBilgileriView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:vm="clr-namespace:DonDonat.WPF.Presentation.ViewModels"
             xmlns:local="clr-namespace:DonDonat.WPF"
             mc:Ignorable="d"
             d:DesignHeight="800" d:DesignWidth="1000">

    <UserControl.DataContext>
        <vm:ProjeBilgileriViewModel />
    </UserControl.DataContext>

    <UserControl.Resources>
        <!-- Stil tanımlamaları -->
        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#2C3E50"/>
            <Setter Property="Margin" Value="5,10,5,5"/>
        </Style>

        <Style x:Key="LabelStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="#34495E"/>
            <Setter Property="Margin" Value="5,5,5,2"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>

        <Style x:Key="TextBoxStyle" TargetType="TextBox">
            <Setter Property="Padding" Value="8,5"/>
            <Setter Property="Margin" Value="5,2,5,10"/>
            <Setter Property="BorderBrush" Value="#BDC3C7"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="FontSize" Value="12"/>
            <Style.Triggers>
                <Trigger Property="IsFocused" Value="True">
                    <Setter Property="BorderBrush" Value="#3498DB"/>
                    <Setter Property="BorderThickness" Value="2"/>
                </Trigger>
                <Trigger Property="Validation.HasError" Value="True">
                    <Setter Property="BorderBrush" Value="#E74C3C"/>
                    <Setter Property="BorderThickness" Value="2"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="ComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Padding" Value="8,5"/>
            <Setter Property="Margin" Value="5,2,5,10"/>
            <Setter Property="BorderBrush" Value="#BDC3C7"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="FontSize" Value="12"/>
            <Style.Triggers>
                <Trigger Property="IsFocused" Value="True">
                    <Setter Property="BorderBrush" Value="#3498DB"/>
                    <Setter Property="BorderThickness" Value="2"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="CheckBoxStyle" TargetType="CheckBox">
            <Setter Property="Margin" Value="5,5,5,10"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="#34495E"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>

        <Style x:Key="ButtonStyle" TargetType="Button">
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Background" Value="#3498DB"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="4" 
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#2980B9"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#21618C"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Background" Value="#BDC3C7"/>
                                <Setter Property="Cursor" Value="Arrow"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="GroupBoxStyle" TargetType="GroupBox">
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Padding" Value="10"/>
            <Setter Property="BorderBrush" Value="#BDC3C7"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#2C3E50"/>
        </Style>

        <Style x:Key="CalculatedValueStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#27AE60"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Başlık -->
        <Border Grid.Row="0" Background="#34495E" Padding="15">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="📋" FontSize="24" Margin="0,0,10,0" VerticalAlignment="Center"/>
                <TextBlock Text="Proje Bilgileri" 
                           FontSize="20" FontWeight="Bold" Foreground="White" 
                           VerticalAlignment="Center"/>
                <TextBlock Text="*" 
                           FontSize="16" Foreground="#E74C3C" 
                           Margin="5,0,0,0" VerticalAlignment="Center"
                           Visibility="{Binding HasUnsavedChanges, Converter={x:Static local:BooleanToVisibilityConverter.Instance}}"/>
            </StackPanel>
        </Border>

        <!-- Ana İçerik -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Sol Kolon -->
                <StackPanel Grid.Column="0" Margin="0,0,10,0">
                    
                    <!-- Temel Proje Bilgileri -->
                    <GroupBox Header="Temel Proje Bilgileri" Style="{StaticResource GroupBoxStyle}">
                        <StackPanel>
                            <TextBlock Text="Proje Adı *" Style="{StaticResource LabelStyle}"/>
                            <TextBox Text="{Binding ProjeAdi, UpdateSourceTrigger=PropertyChanged}" 
                                     Style="{StaticResource TextBoxStyle}"/>

                            <TextBlock Text="Proje Lokasyonu" Style="{StaticResource LabelStyle}"/>
                            <TextBox Text="{Binding ProjeLokasyonu, UpdateSourceTrigger=PropertyChanged}" 
                                     Style="{StaticResource TextBoxStyle}"/>

                            <TextBlock Text="Müteahhit Firma" Style="{StaticResource LabelStyle}"/>
                            <TextBox Text="{Binding MuteahhitFirma, UpdateSourceTrigger=PropertyChanged}" 
                                     Style="{StaticResource TextBoxStyle}"/>

                            <TextBlock Text="Proje Mühendisi" Style="{StaticResource LabelStyle}"/>
                            <TextBox Text="{Binding ProjeMuhendisi, UpdateSourceTrigger=PropertyChanged}" 
                                     Style="{StaticResource TextBoxStyle}"/>
                        </StackPanel>
                    </GroupBox>

                    <!-- Temel Bilgileri -->
                    <GroupBox Header="Temel Bilgileri" Style="{StaticResource GroupBoxStyle}">
                        <StackPanel>
                            <TextBlock Text="Temel Türü *" Style="{StaticResource LabelStyle}"/>
                            <ComboBox ItemsSource="{Binding TemelTurleri}"
                                      SelectedItem="{Binding TemelTuru}"
                                      Style="{StaticResource ComboBoxStyle}"/>

                            <TextBlock Text="Temel Derinliği (m)" Style="{StaticResource LabelStyle}"/>
                            <TextBox Text="{Binding TemelDerinligi, UpdateSourceTrigger=PropertyChanged}" 
                                     Style="{StaticResource TextBoxStyle}"/>
                        </StackPanel>
                    </GroupBox>

                    <!-- Birimler -->
                    <GroupBox Header="Birimler" Style="{StaticResource GroupBoxStyle}">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0" Margin="0,0,5,0">
                                <TextBlock Text="Uzunluk Birimi" Style="{StaticResource LabelStyle}"/>
                                <ComboBox ItemsSource="{Binding UzunlukBirimleri}"
                                          SelectedItem="{Binding UzunlukBirimi}"
                                          Style="{StaticResource ComboBoxStyle}"/>
                            </StackPanel>

                            <StackPanel Grid.Column="1" Margin="5,0,0,0">
                                <TextBlock Text="Alan Birimi" Style="{StaticResource LabelStyle}"/>
                                <ComboBox ItemsSource="{Binding AlanBirimleri}"
                                          SelectedItem="{Binding AlanBirimi}"
                                          Style="{StaticResource ComboBoxStyle}"/>
                            </StackPanel>
                        </Grid>
                    </GroupBox>
                </StackPanel>

                <!-- Sağ Kolon -->
                <StackPanel Grid.Column="1" Margin="10,0,0,0">
                    
                    <!-- Kat Bilgileri -->
                    <GroupBox Header="Kat Bilgileri" Style="{StaticResource GroupBoxStyle}">
                        <StackPanel>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0" Margin="0,0,5,0">
                                    <TextBlock Text="Kat Sayısı *" Style="{StaticResource LabelStyle}"/>
                                    <TextBox Text="{Binding KatSayisi, UpdateSourceTrigger=PropertyChanged}" 
                                             Style="{StaticResource TextBoxStyle}"/>
                                </StackPanel>

                                <StackPanel Grid.Column="1" Margin="5,0,0,0">
                                    <TextBlock Text="Kat Yüksekliği (m)" Style="{StaticResource LabelStyle}"/>
                                    <TextBox Text="{Binding KatYuksekligi, UpdateSourceTrigger=PropertyChanged}" 
                                             Style="{StaticResource TextBoxStyle}"/>
                                </StackPanel>
                            </Grid>

                            <CheckBox Content="Bodrum Var" 
                                      IsChecked="{Binding BodrumVarMi}"
                                      Style="{StaticResource CheckBoxStyle}"/>

                            <StackPanel Visibility="{Binding BodrumVarMi, Converter={x:Static local:BooleanToVisibilityConverter.Instance}}">
                                <TextBlock Text="Bodrum Kat Sayısı" Style="{StaticResource LabelStyle}"/>
                                <TextBox Text="{Binding BodrumKatSayisi, UpdateSourceTrigger=PropertyChanged}" 
                                         Style="{StaticResource TextBoxStyle}"/>
                            </StackPanel>
                        </StackPanel>
                    </GroupBox>

                    <!-- Döşeme Bilgileri -->
                    <GroupBox Header="Döşeme Bilgileri" Style="{StaticResource GroupBoxStyle}">
                        <StackPanel>
                            <TextBlock Text="Döşeme Kalınlığı (cm)" Style="{StaticResource LabelStyle}"/>
                            <TextBox Text="{Binding DosemeKalinligi, UpdateSourceTrigger=PropertyChanged}" 
                                     Style="{StaticResource TextBoxStyle}"/>
                        </StackPanel>
                    </GroupBox>

                    <!-- Hesaplanan Değerler -->
                    <GroupBox Header="Hesaplanan Değerler" Style="{StaticResource GroupBoxStyle}">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" 
                                       Text="Toplam Kat Sayısı:" 
                                       Style="{StaticResource LabelStyle}"/>
                            <TextBlock Grid.Row="0" Grid.Column="1" 
                                       Text="{Binding ToplamKatSayisi}" 
                                       Style="{StaticResource CalculatedValueStyle}"/>

                            <TextBlock Grid.Row="1" Grid.Column="0" 
                                       Text="Toplam Yapı Yüksekliği:" 
                                       Style="{StaticResource LabelStyle}"/>
                            <TextBlock Grid.Row="1" Grid.Column="1" 
                                       Text="{Binding ToplamYapiYuksekligi, StringFormat='{}{0:F2} m'}" 
                                       Style="{StaticResource CalculatedValueStyle}"/>
                        </Grid>
                    </GroupBox>

                    <!-- Proje Açıklaması -->
                    <GroupBox Header="Proje Açıklaması" Style="{StaticResource GroupBoxStyle}">
                        <TextBox Text="{Binding ProjeAciklamasi, UpdateSourceTrigger=PropertyChanged}"
                                 Style="{StaticResource TextBoxStyle}"
                                 Height="80"
                                 TextWrapping="Wrap"
                                 AcceptsReturn="True"
                                 VerticalScrollBarVisibility="Auto"/>
                    </GroupBox>
                </StackPanel>
            </Grid>
        </ScrollViewer>

        <!-- Butonlar -->
        <Border Grid.Row="2" Background="#ECF0F1" Padding="15">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Content="💾 Kaydet" 
                        Command="{Binding SaveCommand}"
                        Style="{StaticResource ButtonStyle}"
                        Background="#27AE60"/>

                <Button Content="📁 Yükle" 
                        Command="{Binding LoadCommand}"
                        Style="{StaticResource ButtonStyle}"
                        Background="#3498DB"/>

                <Button Content="💾 Farklı Kaydet" 
                        Command="{Binding SaveAsCommand}"
                        Style="{StaticResource ButtonStyle}"
                        Background="#F39C12"/>

                <Button Content="📄 Yeni Proje" 
                        Command="{Binding NewProjectCommand}"
                        Style="{StaticResource ButtonStyle}"
                        Background="#9B59B6"/>

                <Button Content="✅ Doğrula" 
                        Command="{Binding ValidateCommand}"
                        Style="{StaticResource ButtonStyle}"
                        Background="#E67E22"/>
            </StackPanel>
        </Border>

        <!-- Durum Çubuğu -->
        <Border Grid.Row="3" Background="#34495E" Padding="10">
            <Grid>
                <TextBlock Text="{Binding StatusMessage}" 
                           Foreground="White" 
                           FontWeight="SemiBold"
                           VerticalAlignment="Center"/>
                
                <!-- Loading Indicator -->
                <StackPanel Orientation="Horizontal" 
                            HorizontalAlignment="Right"
                            Visibility="{Binding IsLoading, Converter={x:Static local:BooleanToVisibilityConverter.Instance}}">
                    <ProgressBar IsIndeterminate="True" 
                                 Width="100" Height="15" 
                                 Margin="0,0,10,0"/>
                    <TextBlock Text="İşleniyor..." 
                               Foreground="White" 
                               VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</UserControl>

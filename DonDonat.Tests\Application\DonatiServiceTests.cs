using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using DonDonat.Tests.Helpers;
using DonDonat.WPF.Application.Services;
using DonDonat.WPF.Domain.Entities;

namespace DonDonat.Tests.Application
{
    /// <summary>
    /// DonatiService için unit testler
    /// Metraj hesaplama mantığını ve iş kurallarını test eder
    /// </summary>
    public class DonatiServiceTests : TestBase
    {
        private readonly DonatiService _donatiService;

        public DonatiServiceTests()
        {
            _donatiService = new DonatiService();
        }

        [Fact]
        public async Task GetAllAsync_ShouldReturnEmptyList_WhenNoDonatiExists()
        {
            // Act
            var result = await _donatiService.GetAllAsync();

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }

        [Fact]
        public async Task AddAsync_ShouldAddDonati_WhenValidDonatiProvided()
        {
            // Arrange
            var donati = TestDataBuilder.CreateDefaultDonati();

            // Act
            var result = await _donatiService.AddAsync(donati);

            // Assert
            result.Should().BeTrue();
            
            var allDonatilar = await _donatiService.GetAllAsync();
            allDonatilar.Should().Contain(donati);
        }

        [Fact]
        public async Task AddAsync_ShouldReturnFalse_WhenNullDonatiProvided()
        {
            // Act
            var result = await _donatiService.AddAsync(null!);

            // Assert
            result.Should().BeFalse();
        }

        [Fact]
        public async Task UpdateAsync_ShouldUpdateExistingDonati()
        {
            // Arrange
            var donati = TestDataBuilder.CreateDefaultDonati();
            await _donatiService.AddAsync(donati);

            // Act
            donati.Cap = 16;
            donati.Adet = 20;
            var result = await _donatiService.UpdateAsync(donati);

            // Assert
            result.Should().BeTrue();
            
            var updatedDonati = await _donatiService.GetByIdAsync(donati.Id);
            updatedDonati.Should().NotBeNull();
            updatedDonati!.Cap.Should().Be(16);
            updatedDonati.Adet.Should().Be(20);
        }

        [Fact]
        public async Task UpdateAsync_ShouldReturnFalse_WhenDonatiNotExists()
        {
            // Arrange
            var donati = TestDataBuilder.CreateDefaultDonati();

            // Act
            var result = await _donatiService.UpdateAsync(donati);

            // Assert
            result.Should().BeFalse();
        }

        [Fact]
        public async Task DeleteAsync_ShouldRemoveDonati_WhenExists()
        {
            // Arrange
            var donati = TestDataBuilder.CreateDefaultDonati();
            await _donatiService.AddAsync(donati);

            // Act
            var result = await _donatiService.DeleteAsync(donati.Id);

            // Assert
            result.Should().BeTrue();
            
            var deletedDonati = await _donatiService.GetByIdAsync(donati.Id);
            deletedDonati.Should().BeNull();
        }

        [Fact]
        public async Task DeleteAsync_ShouldReturnFalse_WhenDonatiNotExists()
        {
            // Arrange
            var nonExistentId = Guid.NewGuid();

            // Act
            var result = await _donatiService.DeleteAsync(nonExistentId);

            // Assert
            result.Should().BeFalse();
        }

        [Fact]
        public async Task GetByIdAsync_ShouldReturnDonati_WhenExists()
        {
            // Arrange
            var donati = TestDataBuilder.CreateDefaultDonati();
            await _donatiService.AddAsync(donati);

            // Act
            var result = await _donatiService.GetByIdAsync(donati.Id);

            // Assert
            result.Should().NotBeNull();
            result!.Id.Should().Be(donati.Id);
            result.Cap.Should().Be(donati.Cap);
            result.Boy.Should().Be(donati.Boy);
        }

        [Fact]
        public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
        {
            // Arrange
            var nonExistentId = Guid.NewGuid();

            // Act
            var result = await _donatiService.GetByIdAsync(nonExistentId);

            // Assert
            result.Should().BeNull();
        }

        [Fact]
        public async Task CalculateTotalWeight_ShouldReturnCorrectSum()
        {
            // Arrange
            var donatilar = new List<Donati>
            {
                TestDataBuilder.CreateDonati(cap: 12, boy: 6.0, adet: 10), // 53.28 kg
                TestDataBuilder.CreateDonati(cap: 16, boy: 4.0, adet: 5),  // 31.56 kg
                TestDataBuilder.CreateDonati(cap: 20, boy: 8.0, adet: 3)   // 59.18 kg
            };

            foreach (var donati in donatilar)
            {
                await _donatiService.AddAsync(donati);
            }

            // Act
            var totalWeight = await _donatiService.CalculateTotalWeightAsync();

            // Assert
            var expectedWeight = donatilar.Sum(d => d.TotalWeight);
            totalWeight.Should().BeApproximately(expectedWeight, 0.01);
        }

        [Fact]
        public async Task CalculateTotalLength_ShouldReturnCorrectSum()
        {
            // Arrange
            var donatilar = new List<Donati>
            {
                TestDataBuilder.CreateDonati(cap: 12, boy: 6.0, adet: 10), // 60 m
                TestDataBuilder.CreateDonati(cap: 16, boy: 4.0, adet: 5),  // 20 m
                TestDataBuilder.CreateDonati(cap: 20, boy: 8.0, adet: 3)   // 24 m
            };

            foreach (var donati in donatilar)
            {
                await _donatiService.AddAsync(donati);
            }

            // Act
            var totalLength = await _donatiService.CalculateTotalLengthAsync();

            // Assert
            var expectedLength = donatilar.Sum(d => d.TotalLength);
            totalLength.Should().BeApproximately(expectedLength, 0.01);
        }

        [Fact]
        public async Task GetDonatilarByCapAsync_ShouldReturnFilteredResults()
        {
            // Arrange
            var donatilar = new List<Donati>
            {
                TestDataBuilder.CreateDonati(cap: 12),
                TestDataBuilder.CreateDonati(cap: 16),
                TestDataBuilder.CreateDonati(cap: 12),
                TestDataBuilder.CreateDonati(cap: 20)
            };

            foreach (var donati in donatilar)
            {
                await _donatiService.AddAsync(donati);
            }

            // Act
            var cap12Donatilar = await _donatiService.GetDonatilarByCapAsync(12);

            // Assert
            cap12Donatilar.Should().HaveCount(2);
            cap12Donatilar.Should().OnlyContain(d => d.Cap == 12);
        }

        [Fact]
        public async Task GetDonatilarByYapiElemaniAsync_ShouldReturnFilteredResults()
        {
            // Arrange
            var donatilar = new List<Donati>
            {
                TestDataBuilder.CreateDonati(yapiElemani: "KİRİŞ"),
                TestDataBuilder.CreateDonati(yapiElemani: "KOLON"),
                TestDataBuilder.CreateDonati(yapiElemani: "KİRİŞ"),
                TestDataBuilder.CreateDonati(yapiElemani: "DÖŞEME")
            };

            foreach (var donati in donatilar)
            {
                await _donatiService.AddAsync(donati);
            }

            // Act
            var kirisDonatilar = await _donatiService.GetDonatilarByYapiElemaniAsync("KİRİŞ");

            // Assert
            kirisDonatilar.Should().HaveCount(2);
            kirisDonatilar.Should().OnlyContain(d => d.YapiElemani == "KİRİŞ");
        }

        [Fact]
        public async Task GetCapSummaryAsync_ShouldReturnCorrectGrouping()
        {
            // Arrange
            var donatilar = new List<Donati>
            {
                TestDataBuilder.CreateDonati(cap: 12, boy: 6.0, adet: 10),
                TestDataBuilder.CreateDonati(cap: 12, boy: 4.0, adet: 5),
                TestDataBuilder.CreateDonati(cap: 16, boy: 8.0, adet: 3),
                TestDataBuilder.CreateDonati(cap: 16, boy: 5.0, adet: 7)
            };

            foreach (var donati in donatilar)
            {
                await _donatiService.AddAsync(donati);
            }

            // Act
            var summary = await _donatiService.GetCapSummaryAsync();

            // Assert
            summary.Should().HaveCount(2);
            
            var cap12Summary = summary.FirstOrDefault(s => s.Cap == 12);
            cap12Summary.Should().NotBeNull();
            cap12Summary!.TotalLength.Should().Be(80.0); // (6*10) + (4*5) = 80
            cap12Summary.TotalAdet.Should().Be(15); // 10 + 5 = 15

            var cap16Summary = summary.FirstOrDefault(s => s.Cap == 16);
            cap16Summary.Should().NotBeNull();
            cap16Summary!.TotalLength.Should().Be(59.0); // (8*3) + (5*7) = 59
            cap16Summary.TotalAdet.Should().Be(10); // 3 + 7 = 10
        }

        [Fact]
        public async Task ValidateDonati_ShouldReturnTrue_ForValidDonati()
        {
            // Arrange
            var validDonati = TestDataBuilder.CreateDefaultDonati();

            // Act
            var result = await _donatiService.ValidateDonatiAsync(validDonati);

            // Assert
            result.IsValid.Should().BeTrue();
            result.ErrorMessages.Should().BeEmpty();
        }

        [Theory]
        [InlineData(0, 6.0, 10)] // Geçersiz çap
        [InlineData(12, 0, 10)]  // Geçersiz boy
        [InlineData(12, 6.0, 0)] // Geçersiz adet
        [InlineData(-1, 6.0, 10)] // Negatif çap
        [InlineData(12, -1, 10)]  // Negatif boy
        [InlineData(12, 6.0, -1)] // Negatif adet
        public async Task ValidateDonati_ShouldReturnFalse_ForInvalidDonati(int cap, double boy, int adet)
        {
            // Arrange
            var invalidDonati = TestDataBuilder.CreateDonati(cap: cap, boy: boy, adet: adet);

            // Act
            var result = await _donatiService.ValidateDonatiAsync(invalidDonati);

            // Assert
            result.IsValid.Should().BeFalse();
            result.ErrorMessages.Should().NotBeEmpty();
        }

        [Fact]
        public async Task ClearAllAsync_ShouldRemoveAllDonatilar()
        {
            // Arrange
            var donatilar = TestDataBuilder.CreateDonatiList(5);
            foreach (var donati in donatilar)
            {
                await _donatiService.AddAsync(donati);
            }

            // Act
            await _donatiService.ClearAllAsync();

            // Assert
            var remainingDonatilar = await _donatiService.GetAllAsync();
            remainingDonatilar.Should().BeEmpty();
        }

        [Fact]
        public async Task GetCountAsync_ShouldReturnCorrectCount()
        {
            // Arrange
            var donatilar = TestDataBuilder.CreateDonatiList(7);
            foreach (var donati in donatilar)
            {
                await _donatiService.AddAsync(donati);
            }

            // Act
            var count = await _donatiService.GetCountAsync();

            // Assert
            count.Should().Be(7);
        }
    }
}

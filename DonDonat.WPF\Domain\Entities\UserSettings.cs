using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace DonDonat.WPF.Domain.Entities
{
    /// <summary>
    /// Kullanıcı ayarları domain modeli
    /// Uygulama genelindeki kullanıcı tercihlerini tutar
    /// </summary>
    public class UserSettings : INotifyPropertyChanged
    {
        #region Private Fields

        private string _theme = "Light";
        private string _language = "tr-TR";
        private string _defaultFolderPath = "";
        private string _lastOpenedFilePath = "";
        private bool _autoSaveEnabled = true;
        private int _autoSaveIntervalMinutes = 5;
        private bool _showGridLines = true;
        private bool _showRowNumbers = true;
        private bool _enableAlternatingRowColors = true;
        private double _fontSize = 12.0;
        private string _fontFamily = "Segoe UI";
        private bool _confirmOnDelete = true;
        private bool _confirmOnExit = true;
        private bool _rememberWindowSize = true;
        private double _windowWidth = 1200;
        private double _windowHeight = 800;
        private bool _windowMaximized = false;
        private bool _showStatusBar = true;
        private bool _showToolbar = true;
        private string _exportDefaultFormat = "Excel";
        private bool _openFileAfterExport = true;
        private string _recentFilesJson = "[]";
        private int _maxRecentFiles = 10;
        private bool _enableNotifications = true;
        private string _backupFolderPath = "";
        private bool _createBackupOnSave = false;
        private int _backupRetentionDays = 30;

        #endregion

        #region Public Properties

        /// <summary>
        /// Uygulama teması (Light, Dark)
        /// </summary>
        public string Theme
        {
            get => _theme;
            set
            {
                _theme = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Uygulama dili
        /// </summary>
        public string Language
        {
            get => _language;
            set
            {
                _language = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Varsayılan klasör yolu
        /// </summary>
        public string DefaultFolderPath
        {
            get => _defaultFolderPath;
            set
            {
                _defaultFolderPath = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Son açılan dosya yolu
        /// </summary>
        public string LastOpenedFilePath
        {
            get => _lastOpenedFilePath;
            set
            {
                _lastOpenedFilePath = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Otomatik kaydetme etkin mi
        /// </summary>
        public bool AutoSaveEnabled
        {
            get => _autoSaveEnabled;
            set
            {
                _autoSaveEnabled = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Otomatik kaydetme aralığı (dakika)
        /// </summary>
        public int AutoSaveIntervalMinutes
        {
            get => _autoSaveIntervalMinutes;
            set
            {
                _autoSaveIntervalMinutes = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Tablo ızgara çizgileri gösterilsin mi
        /// </summary>
        public bool ShowGridLines
        {
            get => _showGridLines;
            set
            {
                _showGridLines = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Satır numaraları gösterilsin mi
        /// </summary>
        public bool ShowRowNumbers
        {
            get => _showRowNumbers;
            set
            {
                _showRowNumbers = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Alternatif satır renkleri etkin mi
        /// </summary>
        public bool EnableAlternatingRowColors
        {
            get => _enableAlternatingRowColors;
            set
            {
                _enableAlternatingRowColors = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Font boyutu
        /// </summary>
        public double FontSize
        {
            get => _fontSize;
            set
            {
                _fontSize = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Font ailesi
        /// </summary>
        public string FontFamily
        {
            get => _fontFamily;
            set
            {
                _fontFamily = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Silme işleminde onay iste
        /// </summary>
        public bool ConfirmOnDelete
        {
            get => _confirmOnDelete;
            set
            {
                _confirmOnDelete = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Çıkış işleminde onay iste
        /// </summary>
        public bool ConfirmOnExit
        {
            get => _confirmOnExit;
            set
            {
                _confirmOnExit = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Pencere boyutunu hatırla
        /// </summary>
        public bool RememberWindowSize
        {
            get => _rememberWindowSize;
            set
            {
                _rememberWindowSize = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Pencere genişliği
        /// </summary>
        public double WindowWidth
        {
            get => _windowWidth;
            set
            {
                _windowWidth = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Pencere yüksekliği
        /// </summary>
        public double WindowHeight
        {
            get => _windowHeight;
            set
            {
                _windowHeight = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Pencere maksimize durumda mı
        /// </summary>
        public bool WindowMaximized
        {
            get => _windowMaximized;
            set
            {
                _windowMaximized = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Durum çubuğu gösterilsin mi
        /// </summary>
        public bool ShowStatusBar
        {
            get => _showStatusBar;
            set
            {
                _showStatusBar = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Araç çubuğu gösterilsin mi
        /// </summary>
        public bool ShowToolbar
        {
            get => _showToolbar;
            set
            {
                _showToolbar = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Varsayılan export formatı
        /// </summary>
        public string ExportDefaultFormat
        {
            get => _exportDefaultFormat;
            set
            {
                _exportDefaultFormat = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Export sonrası dosyayı aç
        /// </summary>
        public bool OpenFileAfterExport
        {
            get => _openFileAfterExport;
            set
            {
                _openFileAfterExport = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Son dosyalar listesi (JSON formatında)
        /// </summary>
        public string RecentFilesJson
        {
            get => _recentFilesJson;
            set
            {
                _recentFilesJson = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Maksimum son dosya sayısı
        /// </summary>
        public int MaxRecentFiles
        {
            get => _maxRecentFiles;
            set
            {
                _maxRecentFiles = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Bildirimler etkin mi
        /// </summary>
        public bool EnableNotifications
        {
            get => _enableNotifications;
            set
            {
                _enableNotifications = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Yedek klasör yolu
        /// </summary>
        public string BackupFolderPath
        {
            get => _backupFolderPath;
            set
            {
                _backupFolderPath = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Kaydetme sırasında yedek oluştur
        /// </summary>
        public bool CreateBackupOnSave
        {
            get => _createBackupOnSave;
            set
            {
                _createBackupOnSave = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Yedek saklama süresi (gün)
        /// </summary>
        public int BackupRetentionDays
        {
            get => _backupRetentionDays;
            set
            {
                _backupRetentionDays = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Ayarların son güncellenme tarihi
        /// </summary>
        public DateTime LastUpdated { get; set; } = DateTime.Now;

        /// <summary>
        /// Ayarlar versiyonu (uyumluluk için)
        /// </summary>
        public string Version { get; set; } = "1.0";

        #endregion

        #region Helper Properties

        /// <summary>
        /// Son dosyalar listesi (deserialize edilmiş)
        /// </summary>
        public List<string> RecentFiles
        {
            get
            {
                try
                {
                    return System.Text.Json.JsonSerializer.Deserialize<List<string>>(RecentFilesJson) ?? new List<string>();
                }
                catch
                {
                    return new List<string>();
                }
            }
            set
            {
                try
                {
                    RecentFilesJson = System.Text.Json.JsonSerializer.Serialize(value ?? new List<string>());
                }
                catch
                {
                    RecentFilesJson = "[]";
                }
            }
        }

        /// <summary>
        /// Koyu tema aktif mi
        /// </summary>
        public bool IsDarkTheme => Theme?.ToLower() == "dark";

        /// <summary>
        /// Açık tema aktif mi
        /// </summary>
        public bool IsLightTheme => Theme?.ToLower() == "light";

        #endregion

        #region Methods

        /// <summary>
        /// Son dosyalar listesine yeni dosya ekler
        /// </summary>
        public void AddRecentFile(string filePath)
        {
            if (string.IsNullOrWhiteSpace(filePath))
                return;

            var recentFiles = RecentFiles;
            
            // Eğer dosya zaten listede varsa kaldır
            recentFiles.Remove(filePath);
            
            // Listenin başına ekle
            recentFiles.Insert(0, filePath);
            
            // Maksimum sayıyı aş
            while (recentFiles.Count > MaxRecentFiles)
            {
                recentFiles.RemoveAt(recentFiles.Count - 1);
            }
            
            RecentFiles = recentFiles;
        }

        /// <summary>
        /// Son dosyalar listesinden dosya kaldırır
        /// </summary>
        public void RemoveRecentFile(string filePath)
        {
            if (string.IsNullOrWhiteSpace(filePath))
                return;

            var recentFiles = RecentFiles;
            recentFiles.Remove(filePath);
            RecentFiles = recentFiles;
        }

        /// <summary>
        /// Son dosyalar listesini temizler
        /// </summary>
        public void ClearRecentFiles()
        {
            RecentFiles = new List<string>();
        }

        /// <summary>
        /// Varsayılan ayarlara sıfırlar
        /// </summary>
        public void ResetToDefaults()
        {
            Theme = "Light";
            Language = "tr-TR";
            DefaultFolderPath = "";
            LastOpenedFilePath = "";
            AutoSaveEnabled = true;
            AutoSaveIntervalMinutes = 5;
            ShowGridLines = true;
            ShowRowNumbers = true;
            EnableAlternatingRowColors = true;
            FontSize = 12.0;
            FontFamily = "Segoe UI";
            ConfirmOnDelete = true;
            ConfirmOnExit = true;
            RememberWindowSize = true;
            WindowWidth = 1200;
            WindowHeight = 800;
            WindowMaximized = false;
            ShowStatusBar = true;
            ShowToolbar = true;
            ExportDefaultFormat = "Excel";
            OpenFileAfterExport = true;
            ClearRecentFiles();
            MaxRecentFiles = 10;
            EnableNotifications = true;
            BackupFolderPath = "";
            CreateBackupOnSave = false;
            BackupRetentionDays = 30;
            LastUpdated = DateTime.Now;
        }

        #endregion

        #region INotifyPropertyChanged Implementation

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }
}

using System;

namespace DonDonat.WPF.Domain.Commands
{
    /// <summary>
    /// Undo/Redo işlemleri için temel command interface
    /// Command Pattern implementasyonu
    /// </summary>
    public interface ICommandAction
    {
        /// <summary>
        /// Command'ın benzersiz kimliği
        /// </summary>
        Guid Id { get; }

        /// <summary>
        /// Command'ın açıklaması (UI'da gösterilmek için)
        /// </summary>
        string Description { get; }

        /// <summary>
        /// Command'ın oluşturulma zamanı
        /// </summary>
        DateTime Timestamp { get; }

        /// <summary>
        /// Command'ı çalıştırır
        /// </summary>
        /// <returns>İşlemin başarılı olup olmadığı</returns>
        bool Execute();

        /// <summary>
        /// Command'ı geri alır (Undo)
        /// </summary>
        /// <returns>İşlemin başarılı olup olmadığı</returns>
        bool Undo();

        /// <summary>
        /// Command'ın tekrar çalıştırılabilir olup olmadığını kontrol eder
        /// </summary>
        /// <returns>Tekrar çalıştırılabilir mi</returns>
        bool CanExecute();

        /// <summary>
        /// Command'ın geri alınabilir olup olmadığını kontrol eder
        /// </summary>
        /// <returns>Geri alınabilir mi</returns>
        bool CanUndo();

        /// <summary>
        /// Command'ın başka bir command ile birleştirilebilir olup olmadığını kontrol eder
        /// Aynı nesne üzerinde ardışık düzenlemeler için kullanılır
        /// </summary>
        /// <param name="other">Diğer command</param>
        /// <returns>Birleştirilebilir mi</returns>
        bool CanMergeWith(ICommandAction other);

        /// <summary>
        /// Command'ı başka bir command ile birleştirir
        /// </summary>
        /// <param name="other">Birleştirilecek command</param>
        /// <returns>Birleştirilmiş command</returns>
        ICommandAction MergeWith(ICommandAction other);

        /// <summary>
        /// Command'ın geçerli olup olmadığını kontrol eder
        /// Örneğin: Silinecek nesne hala var mı?
        /// </summary>
        /// <returns>Geçerli mi</returns>
        bool IsValid();
    }
}

using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using DonDonat.WPF.Application.Interfaces;
using DonDonat.WPF.Domain.Entities;

namespace DonDonat.WPF.Application.Services
{
    /// <summary>
    /// Proje bilgileri servisi implementasyonu
    /// JSON dosya işlemleri ve validation işlemlerini gerçekleştirir
    /// </summary>
    public class ProjeBilgileriService : IProjeBilgileriService
    {
        private readonly string _defaultFileName = "proje_bilgileri.json";
        private readonly string _defaultDirectory;

        public ProjeBilgileriService()
        {
            // Varsayılan dizin: Uygulamanın çalıştığı dizin altında "Data" klasörü
            _defaultDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data");
            
            // Dizin yoksa oluştur
            if (!Directory.Exists(_defaultDirectory))
            {
                Directory.CreateDirectory(_defaultDirectory);
            }
        }

        /// <summary>
        /// Proje bilgilerini JSON dosyasına kaydeder
        /// </summary>
        public async Task<bool> SaveProjeBilgileriAsync(ProjeBilgileri projeBilgileri, string? filePath = null)
        {
            try
            {
                if (projeBilgileri == null)
                    throw new ArgumentNullException(nameof(projeBilgileri));

                // Validation kontrolü
                var (isValid, errorMessages) = ValidateProjeBilgileri(projeBilgileri);
                if (!isValid)
                {
                    throw new ValidationException($"Proje bilgileri geçersiz: {string.Join(", ", errorMessages)}");
                }

                // Dosya yolu belirleme
                var targetFilePath = filePath ?? GetDefaultFilePath();
                
                // Dizin yoksa oluştur
                var directory = Path.GetDirectoryName(targetFilePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // Güncellenme tarihini ayarla
                projeBilgileri.GuncellenmeTarihi = DateTime.Now;

                // JSON'a serialize et
                var jsonString = SerializeToJson(projeBilgileri);

                // Dosyaya yaz
                await File.WriteAllTextAsync(targetFilePath, jsonString);

                return true;
            }
            catch (Exception ex)
            {
                // Log the exception (gerçek uygulamada logging framework kullanılabilir)
                System.Diagnostics.Debug.WriteLine($"Proje bilgileri kaydedilirken hata: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// JSON dosyasından proje bilgilerini yükler
        /// </summary>
        public async Task<ProjeBilgileri?> LoadProjeBilgileriAsync(string? filePath = null)
        {
            try
            {
                var targetFilePath = filePath ?? GetDefaultFilePath();

                if (!File.Exists(targetFilePath))
                    return null;

                var jsonString = await File.ReadAllTextAsync(targetFilePath);
                
                if (string.IsNullOrWhiteSpace(jsonString))
                    return null;

                return DeserializeFromJson(jsonString);
            }
            catch (Exception ex)
            {
                // Log the exception
                System.Diagnostics.Debug.WriteLine($"Proje bilgileri yüklenirken hata: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Proje bilgilerini doğrular
        /// </summary>
        public (bool IsValid, string[] ErrorMessages) ValidateProjeBilgileri(ProjeBilgileri projeBilgileri)
        {
            if (projeBilgileri == null)
                return (false, new[] { "Proje bilgileri null olamaz" });

            var validationResults = new List<ValidationResult>();
            var validationContext = new ValidationContext(projeBilgileri);

            bool isValid = Validator.TryValidateObject(projeBilgileri, validationContext, validationResults, true);

            // Ek business rule validasyonları
            var additionalErrors = new List<string>();

            // Bodrum varsa bodrum kat sayısı 0'dan büyük olmalı
            if (projeBilgileri.BodrumVarMi && projeBilgileri.BodrumKatSayisi <= 0)
            {
                additionalErrors.Add("Bodrum varsa bodrum kat sayısı 0'dan büyük olmalıdır");
                isValid = false;
            }

            // Bodrum yoksa bodrum kat sayısı 0 olmalı
            if (!projeBilgileri.BodrumVarMi && projeBilgileri.BodrumKatSayisi > 0)
            {
                additionalErrors.Add("Bodrum yoksa bodrum kat sayısı 0 olmalıdır");
                isValid = false;
            }

            var allErrors = validationResults.Select(vr => vr.ErrorMessage ?? "Bilinmeyen hata")
                                           .Concat(additionalErrors)
                                           .ToArray();

            return (isValid, allErrors);
        }

        /// <summary>
        /// Varsayılan proje bilgileri oluşturur
        /// </summary>
        public ProjeBilgileri CreateDefaultProjeBilgileri()
        {
            return new ProjeBilgileri
            {
                Id = Guid.NewGuid(),
                ProjeAdi = "Yeni Proje",
                TemelTuru = "Radye",
                TemelDerinligi = 1.5,
                KatSayisi = 1,
                KatYuksekligi = 3.0,
                BodrumVarMi = false,
                BodrumKatSayisi = 0,
                DosemeKalinligi = 20,
                UzunlukBirimi = "m",
                AlanBirimi = "m²",
                ProjeAciklamasi = "",
                ProjeLokasyonu = "",
                MuteahhitFirma = "",
                ProjeMuhendisi = "",
                OlusturulmaTarihi = DateTime.Now,
                GuncellenmeTarihi = DateTime.Now
            };
        }

        /// <summary>
        /// Proje bilgilerini JSON string'e dönüştürür
        /// </summary>
        public string SerializeToJson(ProjeBilgileri projeBilgileri)
        {
            var options = new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            };

            return JsonSerializer.Serialize(projeBilgileri, options);
        }

        /// <summary>
        /// JSON string'den proje bilgilerini oluşturur
        /// </summary>
        public ProjeBilgileri? DeserializeFromJson(string jsonString)
        {
            try
            {
                var options = new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    PropertyNameCaseInsensitive = true
                };

                return JsonSerializer.Deserialize<ProjeBilgileri>(jsonString, options);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"JSON deserialize hatası: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Varsayılan dosya yolunu döndürür
        /// </summary>
        public string GetDefaultFilePath()
        {
            return Path.Combine(_defaultDirectory, _defaultFileName);
        }

        /// <summary>
        /// Dosya var mı kontrol eder
        /// </summary>
        public bool FileExists(string filePath)
        {
            return File.Exists(filePath);
        }
    }
}

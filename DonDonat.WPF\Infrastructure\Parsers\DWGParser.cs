using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using DonDonat.WPF.Domain.Entities;

namespace DonDonat.WPF.Infrastructure.Parsers
{
    /// <summary>
    /// DXF dosyalarından donatı bilgilerini okuyup parse eden sınıf
    /// AutoCAD DWG dosyaları DXF formatına çevrilerek işlenir
    /// </summary>
    public class DWGParser
    {
        private readonly List<string> _supportedDonatiTypes = new()
        {
            "NERVÜRLÜ", "DÜZ", "SPIRAL", "ETRIYE", "ÇIROZ"
        };

        private readonly Dictionary<string, int> _standardCaplar = new()
        {
            { "Ø8", 8 }, { "Ø10", 10 }, { "Ø12", 12 }, { "Ø14", 14 },
            { "Ø16", 16 }, { "Ø18", 18 }, { "Ø20", 20 }, { "Ø22", 22 },
            { "Ø25", 25 }, { "Ø28", 28 }, { "Ø32", 32 }
        };

        /// <summary>
        /// DXF dosyasından donatı bilgilerini okur ve Donati nesnelerine dönüştürür
        /// </summary>
        /// <param name="dxfFilePath">DXF dosya yolu</param>
        /// <returns>Donatı nesneleri listesi</returns>
        public async Task<List<Donati>> ParseDXFFileAsync(string dxfFilePath)
        {
            if (!File.Exists(dxfFilePath))
                throw new FileNotFoundException($"DXF dosyası bulunamadı: {dxfFilePath}");

            var donatilar = new List<Donati>();

            try
            {
                var lines = await File.ReadAllLinesAsync(dxfFilePath);
                var textEntities = ExtractTextEntities(lines);
                var lineEntities = ExtractLineEntities(lines);

                // Metin varlıklarından donatı bilgilerini çıkar
                foreach (var textEntity in textEntities)
                {
                    var donati = ParseDonatiFromText(textEntity);
                    if (donati != null)
                    {
                        donatilar.Add(donati);
                    }
                }

                // Çizgi varlıklarından donatı uzunluklarını hesapla
                UpdateDonatiLengthsFromLines(donatilar, lineEntities);

                return donatilar;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"DXF dosyası parse edilirken hata oluştu: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// DXF dosyasından TEXT ve MTEXT varlıklarını çıkarır
        /// </summary>
        private List<DXFTextEntity> ExtractTextEntities(string[] lines)
        {
            var textEntities = new List<DXFTextEntity>();
            var currentEntity = new DXFTextEntity();
            bool inTextSection = false;

            for (int i = 0; i < lines.Length; i++)
            {
                var line = lines[i].Trim();

                // TEXT veya MTEXT başlangıcı
                if (line == "TEXT" || line == "MTEXT")
                {
                    inTextSection = true;
                    currentEntity = new DXFTextEntity { Type = line };
                    continue;
                }

                if (!inTextSection) continue;

                // Koordinat bilgileri
                if (line == "10" && i + 1 < lines.Length) // X koordinatı
                {
                    if (double.TryParse(lines[i + 1], out double x))
                        currentEntity.X = x;
                }
                else if (line == "20" && i + 1 < lines.Length) // Y koordinatı
                {
                    if (double.TryParse(lines[i + 1], out double y))
                        currentEntity.Y = y;
                }
                else if (line == "1" && i + 1 < lines.Length) // Metin içeriği
                {
                    currentEntity.Text = lines[i + 1];
                }
                else if (line == "40" && i + 1 < lines.Length) // Metin yüksekliği
                {
                    if (double.TryParse(lines[i + 1], out double height))
                        currentEntity.Height = height;
                }

                // Varlık sonu
                if (line == "0" && !string.IsNullOrEmpty(currentEntity.Text))
                {
                    textEntities.Add(currentEntity);
                    currentEntity = new DXFTextEntity();
                    inTextSection = false;
                }
            }

            return textEntities;
        }

        /// <summary>
        /// DXF dosyasından LINE varlıklarını çıkarır
        /// </summary>
        private List<DXFLineEntity> ExtractLineEntities(string[] lines)
        {
            var lineEntities = new List<DXFLineEntity>();
            var currentEntity = new DXFLineEntity();
            bool inLineSection = false;

            for (int i = 0; i < lines.Length; i++)
            {
                var line = lines[i].Trim();

                if (line == "LINE")
                {
                    inLineSection = true;
                    currentEntity = new DXFLineEntity();
                    continue;
                }

                if (!inLineSection) continue;

                // Başlangıç noktası
                if (line == "10" && i + 1 < lines.Length)
                {
                    if (double.TryParse(lines[i + 1], out double startX))
                        currentEntity.StartX = startX;
                }
                else if (line == "20" && i + 1 < lines.Length)
                {
                    if (double.TryParse(lines[i + 1], out double startY))
                        currentEntity.StartY = startY;
                }
                // Bitiş noktası
                else if (line == "11" && i + 1 < lines.Length)
                {
                    if (double.TryParse(lines[i + 1], out double endX))
                        currentEntity.EndX = endX;
                }
                else if (line == "21" && i + 1 < lines.Length)
                {
                    if (double.TryParse(lines[i + 1], out double endY))
                        currentEntity.EndY = endY;
                }

                // Varlık sonu
                if (line == "0")
                {
                    if (currentEntity.StartX != 0 || currentEntity.StartY != 0 ||
                        currentEntity.EndX != 0 || currentEntity.EndY != 0)
                    {
                        lineEntities.Add(currentEntity);
                    }
                    currentEntity = new DXFLineEntity();
                    inLineSection = false;
                }
            }

            return lineEntities;
        }

        /// <summary>
        /// Metin varlığından donatı bilgisini parse eder
        /// </summary>
        private Donati? ParseDonatiFromText(DXFTextEntity textEntity)
        {
            if (string.IsNullOrWhiteSpace(textEntity.Text))
                return null;

            var text = textEntity.Text.ToUpperInvariant();

            // Donatı çapını bul (Ø8, Ø10, vb.)
            var capMatch = Regex.Match(text, @"Ø(\d+)");
            if (!capMatch.Success)
                return null;

            int cap = int.Parse(capMatch.Groups[1].Value);

            // Adet bilgisini bul (5x, 10 ADET, vb.)
            int adet = 1;
            var adetMatch = Regex.Match(text, @"(\d+)\s*(?:X|ADET|AD)");
            if (adetMatch.Success)
                adet = int.Parse(adetMatch.Groups[1].Value);

            // Boy bilgisini bul (2.50m, 250cm, vb.)
            double boy = 0;
            var boyMatch = Regex.Match(text, @"(\d+(?:\.\d+)?)\s*(?:M|CM)");
            if (boyMatch.Success)
            {
                boy = double.Parse(boyMatch.Groups[1].Value);
                if (text.Contains("CM"))
                    boy /= 100; // cm'den metreye çevir
            }

            // Donatı türünü bul
            string tur = "NERVÜRLÜ"; // Varsayılan
            foreach (var supportedType in _supportedDonatiTypes)
            {
                if (text.Contains(supportedType))
                {
                    tur = supportedType;
                    break;
                }
            }

            // Yapı elemanını bul
            string yapiElemani = "";
            var yapiElemanlari = new[] { "KİRİŞ", "KOLON", "PLAK", "DÖŞEME", "DUVAR", "TEMEL" };
            foreach (var eleman in yapiElemanlari)
            {
                if (text.Contains(eleman))
                {
                    yapiElemani = eleman;
                    break;
                }
            }

            return new Donati
            {
                Id = Guid.NewGuid(),
                Cap = cap,
                Boy = boy > 0 ? boy : 1.0, // Varsayılan boy
                Adet = adet,
                Tur = tur,
                YapiElemani = yapiElemani,
                Konum = DetermineKonum(textEntity.Y), // Y koordinatına göre konum belirle
                DonatiSinifi = "S420"
            };
        }

        /// <summary>
        /// Y koordinatına göre donatının konumunu belirler
        /// </summary>
        private string DetermineKonum(double yCoordinate)
        {
            // Bu basit bir örnek - gerçek uygulamada daha karmaşık mantık gerekebilir
            if (yCoordinate > 1000)
                return "ÜST";
            else if (yCoordinate < -1000)
                return "ALT";
            else
                return "ORTA";
        }

        /// <summary>
        /// Çizgi varlıklarından donatı uzunluklarını günceller
        /// </summary>
        private void UpdateDonatiLengthsFromLines(List<Donati> donatilar, List<DXFLineEntity> lineEntities)
        {
            // Bu metod, donatı çizgilerinin uzunluklarını hesaplayarak
            // donatı nesnelerinin boy bilgilerini günceller
            // Gerçek uygulamada daha karmaşık geometrik hesaplamalar gerekebilir

            foreach (var donati in donatilar.Where(d => d.Boy <= 1.0))
            {
                // En yakın çizgiyi bul ve uzunluğunu hesapla
                var nearestLine = FindNearestLine(lineEntities, donati);
                if (nearestLine != null)
                {
                    double length = CalculateLineLength(nearestLine);
                    if (length > 0.1) // Minimum uzunluk kontrolü
                    {
                        donati.Boy = Math.Round(length, 2);
                    }
                }
            }
        }

        /// <summary>
        /// En yakın çizgiyi bulur
        /// </summary>
        private DXFLineEntity? FindNearestLine(List<DXFLineEntity> lineEntities, Donati donati)
        {
            // Basit yakınlık hesabı - gerçek uygulamada daha gelişmiş algoritma kullanılabilir
            return lineEntities.FirstOrDefault();
        }

        /// <summary>
        /// Çizgi uzunluğunu hesaplar
        /// </summary>
        private double CalculateLineLength(DXFLineEntity line)
        {
            double dx = line.EndX - line.StartX;
            double dy = line.EndY - line.StartY;
            return Math.Sqrt(dx * dx + dy * dy) / 1000; // mm'den metreye çevir
        }
    }

    /// <summary>
    /// DXF metin varlığını temsil eden sınıf
    /// </summary>
    public class DXFTextEntity
    {
        public string Type { get; set; } = "";
        public double X { get; set; }
        public double Y { get; set; }
        public string Text { get; set; } = "";
        public double Height { get; set; }
    }

    /// <summary>
    /// DXF çizgi varlığını temsil eden sınıf
    /// </summary>
    public class DXFLineEntity
    {
        public double StartX { get; set; }
        public double StartY { get; set; }
        public double EndX { get; set; }
        public double EndY { get; set; }
    }
}

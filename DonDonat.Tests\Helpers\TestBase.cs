using System;
using System.IO;
using System.Reflection;

namespace DonDonat.Tests.Helpers
{
    /// <summary>
    /// Test sınıfları için temel sınıf
    /// Ortak test utilities ve setup metodları sağlar
    /// </summary>
    public abstract class TestBase : IDisposable
    {
        protected readonly string TestDataPath;
        protected readonly string TempPath;

        protected TestBase()
        {
            // Test data klasörünün yolunu belirle
            var assemblyLocation = Assembly.GetExecutingAssembly().Location;
            var assemblyDirectory = Path.GetDirectoryName(assemblyLocation);
            TestDataPath = Path.Combine(assemblyDirectory!, "TestData");

            // Geçici dosyalar için klasör oluştur
            TempPath = Path.Combine(Path.GetTempPath(), "DonDonatTests", Guid.NewGuid().ToString());
            Directory.CreateDirectory(TempPath);
        }

        /// <summary>
        /// Test data dosyasının tam yolunu döndürür
        /// </summary>
        /// <param name="fileName">Dosya adı</param>
        /// <returns>Tam dosya yolu</returns>
        protected string GetTestDataPath(string fileName)
        {
            return Path.Combine(TestDataPath, fileName);
        }

        /// <summary>
        /// Geçici dosya yolu oluşturur
        /// </summary>
        /// <param name="fileName">Dosya adı</param>
        /// <returns>Geçici dosya yolu</returns>
        protected string GetTempFilePath(string fileName)
        {
            return Path.Combine(TempPath, fileName);
        }

        /// <summary>
        /// Test dosyasının var olup olmadığını kontrol eder
        /// </summary>
        /// <param name="fileName">Dosya adı</param>
        /// <returns>Dosya var mı</returns>
        protected bool TestDataExists(string fileName)
        {
            return File.Exists(GetTestDataPath(fileName));
        }

        /// <summary>
        /// Test dosyasını geçici klasöre kopyalar
        /// </summary>
        /// <param name="fileName">Dosya adı</param>
        /// <returns>Kopyalanan dosyanın yolu</returns>
        protected string CopyTestDataToTemp(string fileName)
        {
            var sourcePath = GetTestDataPath(fileName);
            var destPath = GetTempFilePath(fileName);
            
            if (File.Exists(sourcePath))
            {
                File.Copy(sourcePath, destPath, true);
                return destPath;
            }
            
            throw new FileNotFoundException($"Test data file not found: {fileName}");
        }

        /// <summary>
        /// Geçici test dosyası oluşturur
        /// </summary>
        /// <param name="fileName">Dosya adı</param>
        /// <param name="content">Dosya içeriği</param>
        /// <returns>Oluşturulan dosyanın yolu</returns>
        protected string CreateTempFile(string fileName, string content)
        {
            var filePath = GetTempFilePath(fileName);
            File.WriteAllText(filePath, content);
            return filePath;
        }

        /// <summary>
        /// Cleanup işlemleri
        /// </summary>
        public virtual void Dispose()
        {
            try
            {
                if (Directory.Exists(TempPath))
                {
                    Directory.Delete(TempPath, true);
                }
            }
            catch (Exception ex)
            {
                // Cleanup hatalarını logla ama test'i başarısız yapma
                System.Diagnostics.Debug.WriteLine($"Cleanup error: {ex.Message}");
            }
        }
    }
}

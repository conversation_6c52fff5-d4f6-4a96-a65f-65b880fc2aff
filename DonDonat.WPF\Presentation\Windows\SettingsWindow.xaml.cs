using System;
using System.ComponentModel;
using System.Windows;
using DonDonat.WPF.Application.Interfaces;
using DonDonat.WPF.Application.Services;
using DonDonat.WPF.Presentation.ViewModels;

namespace DonDonat.WPF.Presentation.Windows
{
    /// <summary>
    /// SettingsWindow.xaml için et<PERSON>
    /// </summary>
    public partial class SettingsWindow : Window
    {
        private readonly SettingsViewModel _viewModel;
        private readonly ISettingsService _settingsService;

        public SettingsWindow()
        {
            InitializeComponent();
            
            // Settings service'i oluştur
            _settingsService = new SettingsService();
            
            // ViewModel'i oluştur ve ayarla
            _viewModel = new SettingsViewModel(_settingsService);
            SettingsViewControl.DataContext = _viewModel;
            
            // Window events
            Loaded += OnWindowLoaded;
            Closing += OnWindowClosing;
        }

        public SettingsWindow(ISettingsService settingsService) : this()
        {
            _settingsService = settingsService;
            _viewModel = new SettingsViewModel(_settingsService);
            SettingsViewControl.DataContext = _viewModel;
        }

        /// <summary>
        /// Pencere yüklendiğinde
        /// </summary>
        private async void OnWindowLoaded(object sender, RoutedEventArgs e)
        {
            try
            {
                // Ayarları yükle
                await _settingsService.LoadSettingsAsync();
                
                // Pencere boyutunu ayarla
                if (_settingsService.CurrentSettings.RememberWindowSize)
                {
                    // Ana pencere boyutlarını kullan (ayarlar penceresi için sabit boyut)
                    Width = 900;
                    Height = 700;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Ayarlar yüklenirken hata oluştu:\n{ex.Message}", 
                    "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Pencere kapanırken
        /// </summary>
        private void OnWindowClosing(object sender, CancelEventArgs e)
        {
            try
            {
                // Kaydedilmemiş değişiklikler varsa uyar
                if (_viewModel.HasUnsavedChanges)
                {
                    var result = MessageBox.Show(
                        "Kaydedilmemiş değişiklikler var. Kaydetmeden çıkmak istediğinizden emin misiniz?",
                        "Onay", MessageBoxButton.YesNo, MessageBoxImage.Question);

                    if (result == MessageBoxResult.No)
                    {
                        e.Cancel = true;
                        return;
                    }
                }

                // Settings service'i dispose et
                if (_settingsService is IDisposable disposable)
                {
                    disposable.Dispose();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Pencere kapatılırken hata oluştu:\n{ex.Message}", 
                    "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Kapat butonu tıklandığında
        /// </summary>
        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        /// <summary>
        /// Settings service'e erişim
        /// </summary>
        public ISettingsService SettingsService => _settingsService;

        /// <summary>
        /// ViewModel'e erişim
        /// </summary>
        public SettingsViewModel ViewModel => _viewModel;
    }
}

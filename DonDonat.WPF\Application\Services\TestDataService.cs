using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using DonDonat.WPF.Application.Interfaces;
using DonDonat.WPF.Domain.Entities;

namespace DonDonat.WPF.Application.Services
{
    /// <summary>
    /// Test verisi oluşturma servisi
    /// Raporlama özelliklerini test etmek için örnek veriler oluşturur
    /// </summary>
    public class TestDataService
    {
        private readonly IDonatiService _donatiService;
        private readonly IProjeBilgileriService _projeBilgileriService;

        public TestDataService(IDonatiService donatiService, IProjeBilgileriService projeBilgileriService)
        {
            _donatiService = donatiService;
            _projeBilgileriService = projeBilgileriService;
        }

        /// <summary>
        /// Test donatıları oluşturur
        /// </summary>
        public async Task<List<Donati>> CreateTestDonatilarAsync()
        {
            var testDonatilar = new List<Donati>
            {
                // Temel donatıları
                new Donati
                {
                    Id = Guid.NewGuid(),
                    Cap = 16,
                    Boy = 12.0,
                    Adet = 24,
                    Tur = "NERVÜRLÜ",
                    YapiElemani = "TEMEL",
                    Konum = "ALT",
                    DonatiSinifi = "S420",
                    OlusturulmaTarihi = DateTime.Now,
                    GuncellenmeTarihi = DateTime.Now
                },
                new Donati
                {
                    Id = Guid.NewGuid(),
                    Cap = 20,
                    Boy = 8.5,
                    Adet = 16,
                    Tur = "NERVÜRLÜ",
                    YapiElemani = "TEMEL",
                    Konum = "ÜST",
                    DonatiSinifi = "S420",
                    OlusturulmaTarihi = DateTime.Now,
                    GuncellenmeTarihi = DateTime.Now
                },

                // Kolon donatıları
                new Donati
                {
                    Id = Guid.NewGuid(),
                    Cap = 18,
                    Boy = 3.2,
                    Adet = 32,
                    Tur = "NERVÜRLÜ",
                    YapiElemani = "KOLON",
                    Konum = "BOYUNA",
                    DonatiSinifi = "S420",
                    OlusturulmaTarihi = DateTime.Now,
                    GuncellenmeTarihi = DateTime.Now
                },
                new Donati
                {
                    Id = Guid.NewGuid(),
                    Cap = 8,
                    Boy = 0.8,
                    Adet = 120,
                    Tur = "NERVÜRLÜ",
                    YapiElemani = "KOLON",
                    Konum = "ENINE",
                    DonatiSinifi = "S420",
                    OlusturulmaTarihi = DateTime.Now,
                    GuncellenmeTarihi = DateTime.Now
                },

                // Kiriş donatıları
                new Donati
                {
                    Id = Guid.NewGuid(),
                    Cap = 14,
                    Boy = 6.0,
                    Adet = 18,
                    Tur = "NERVÜRLÜ",
                    YapiElemani = "KİRİŞ",
                    Konum = "ÜST",
                    DonatiSinifi = "S420",
                    OlusturulmaTarihi = DateTime.Now,
                    GuncellenmeTarihi = DateTime.Now
                },
                new Donati
                {
                    Id = Guid.NewGuid(),
                    Cap = 16,
                    Boy = 6.0,
                    Adet = 12,
                    Tur = "NERVÜRLÜ",
                    YapiElemani = "KİRİŞ",
                    Konum = "ALT",
                    DonatiSinifi = "S420",
                    OlusturulmaTarihi = DateTime.Now,
                    GuncellenmeTarihi = DateTime.Now
                },
                new Donati
                {
                    Id = Guid.NewGuid(),
                    Cap = 10,
                    Boy = 0.6,
                    Adet = 80,
                    Tur = "NERVÜRLÜ",
                    YapiElemani = "KİRİŞ",
                    Konum = "ENINE",
                    DonatiSinifi = "S420",
                    OlusturulmaTarihi = DateTime.Now,
                    GuncellenmeTarihi = DateTime.Now
                },

                // Döşeme donatıları
                new Donati
                {
                    Id = Guid.NewGuid(),
                    Cap = 12,
                    Boy = 4.5,
                    Adet = 45,
                    Tur = "NERVÜRLÜ",
                    YapiElemani = "DÖŞEME",
                    Konum = "ALT X",
                    DonatiSinifi = "S420",
                    OlusturulmaTarihi = DateTime.Now,
                    GuncellenmeTarihi = DateTime.Now
                },
                new Donati
                {
                    Id = Guid.NewGuid(),
                    Cap = 12,
                    Boy = 5.2,
                    Adet = 38,
                    Tur = "NERVÜRLÜ",
                    YapiElemani = "DÖŞEME",
                    Konum = "ALT Y",
                    DonatiSinifi = "S420",
                    OlusturulmaTarihi = DateTime.Now,
                    GuncellenmeTarihi = DateTime.Now
                },
                new Donati
                {
                    Id = Guid.NewGuid(),
                    Cap = 10,
                    Boy = 4.5,
                    Adet = 45,
                    Tur = "NERVÜRLÜ",
                    YapiElemani = "DÖŞEME",
                    Konum = "ÜST X",
                    DonatiSinifi = "S420",
                    OlusturulmaTarihi = DateTime.Now,
                    GuncellenmeTarihi = DateTime.Now
                },
                new Donati
                {
                    Id = Guid.NewGuid(),
                    Cap = 10,
                    Boy = 5.2,
                    Adet = 38,
                    Tur = "NERVÜRLÜ",
                    YapiElemani = "DÖŞEME",
                    Konum = "ÜST Y",
                    DonatiSinifi = "S420",
                    OlusturulmaTarihi = DateTime.Now,
                    GuncellenmeTarihi = DateTime.Now
                },

                // Perde donatıları
                new Donati
                {
                    Id = Guid.NewGuid(),
                    Cap = 14,
                    Boy = 2.8,
                    Adet = 28,
                    Tur = "NERVÜRLÜ",
                    YapiElemani = "PERDE",
                    Konum = "DİKEY",
                    DonatiSinifi = "S420",
                    OlusturulmaTarihi = DateTime.Now,
                    GuncellenmeTarihi = DateTime.Now
                },
                new Donati
                {
                    Id = Guid.NewGuid(),
                    Cap = 12,
                    Boy = 3.5,
                    Adet = 22,
                    Tur = "NERVÜRLÜ",
                    YapiElemani = "PERDE",
                    Konum = "YATAY",
                    DonatiSinifi = "S420",
                    OlusturulmaTarihi = DateTime.Now,
                    GuncellenmeTarihi = DateTime.Now
                }
            };

            return testDonatilar;
        }

        /// <summary>
        /// Test proje bilgisi oluşturur
        /// </summary>
        public ProjeBilgileri CreateTestProjeBilgileri()
        {
            return new ProjeBilgileri
            {
                Id = Guid.NewGuid(),
                ProjeAdi = "Örnek Betonarme Yapı Projesi",
                TemelTuru = "RADYE",
                TemelDerinligi = 2.5,
                KatSayisi = 5,
                KatYuksekligi = 3.0,
                BodrumVarMi = true,
                BodrumKatSayisi = 1,
                DosemeKalinligi = 20,
                UzunlukBirimi = "m",
                AlanBirimi = "m²",
                ProjeAciklamasi = "5 katlı betonarme konut projesi. Radye temel üzerine oturan yapı.",
                ProjeLokasyonu = "İstanbul / Kadıköy",
                MuteahhitFirma = "ABC İnşaat Ltd. Şti.",
                ProjeMuhendisi = "İnş. Müh. Ahmet YILMAZ",
                OlusturulmaTarihi = DateTime.Now,
                GuncellenmeTarihi = DateTime.Now
            };
        }

        /// <summary>
        /// Kapsamlı test verisi seti oluşturur
        /// </summary>
        public async Task<(List<Donati> Donatilar, ProjeBilgileri ProjeBilgileri)> CreateComprehensiveTestDataAsync()
        {
            var donatilar = await CreateTestDonatilarAsync();
            var projeBilgileri = CreateTestProjeBilgileri();

            return (donatilar, projeBilgileri);
        }

        /// <summary>
        /// Test verilerini veritabanına kaydeder
        /// </summary>
        public async Task<bool> SaveTestDataToDatabaseAsync()
        {
            try
            {
                // Test proje bilgisini kaydet
                var projeBilgileri = CreateTestProjeBilgileri();
                var savedProject = await _projeBilgileriService.SaveProjeBilgisiToDbAsync(projeBilgileri);

                // Test donatılarını kaydet
                var donatilar = await CreateTestDonatilarAsync();
                await _donatiService.AddMultipleDonatiAsync(donatilar);

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Test verisi kaydedilirken hata: {ex.Message}");
                return false;
            }
        }
    }
}

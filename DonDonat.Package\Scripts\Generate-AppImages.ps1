# DonDonat MSIX Uygulama İmajları Oluşturucu
# Bu script, DonDonat.ico dosyasından MSIX için gerekli tüm boyutlarda imajlar oluşturur

param(
    [Parameter(Mandatory=$true)]
    [string]$SourceIconPath,
    
    [Parameter(Mandatory=$false)]
    [string]$OutputPath = "..\Images"
)

# Gerekli modülleri kontrol et
if (-not (Get-Command "magick" -ErrorAction SilentlyContinue)) {
    Write-Host "ImageMagick bulunamadı. Lütfen ImageMagick'i yükleyin:" -ForegroundColor Red
    Write-Host "https://imagemagick.org/script/download.php#windows" -ForegroundColor Yellow
    Write-Host "Alternatif olarak, imajları manuel olarak oluşturabilirsiniz." -ForegroundColor Yellow
    exit 1
}

# Çıktı klasörünü oluştur
if (-not (Test-Path $OutputPath)) {
    New-Item -ItemType Directory -Path $OutputPath -Force
}

Write-Host "DonDonat MSIX imajları oluşturuluyor..." -ForegroundColor Green

# MSIX için gerekli imaj boyutları
$ImageSizes = @{
    "Square44x44Logo.scale-200.png" = "88x88"
    "Square44x44Logo.targetsize-24_altform-unplated.png" = "24x24"
    "Square150x150Logo.scale-200.png" = "300x300"
    "Wide310x150Logo.scale-200.png" = "620x300"
    "LockScreenLogo.scale-200.png" = "48x48"
    "SplashScreen.scale-200.png" = "1240x600"
    "StoreLogo.png" = "50x50"
}

foreach ($imageName in $ImageSizes.Keys) {
    $size = $ImageSizes[$imageName]
    $outputFile = Join-Path $OutputPath $imageName
    
    Write-Host "Oluşturuluyor: $imageName ($size)" -ForegroundColor Cyan
    
    if ($imageName -eq "Wide310x150Logo.scale-200.png") {
        # Geniş logo için özel işlem
        magick convert "$SourceIconPath" -resize "150x150" -background "#2C3E50" -gravity center -extent "620x300" "$outputFile"
    }
    elseif ($imageName -eq "SplashScreen.scale-200.png") {
        # Splash screen için özel işlem
        magick convert "$SourceIconPath" -resize "200x200" -background "#34495E" -gravity center -extent "1240x600" "$outputFile"
    }
    else {
        # Normal boyutlandırma
        magick convert "$SourceIconPath" -resize "$size" "$outputFile"
    }
    
    if (Test-Path $outputFile) {
        Write-Host "✓ $imageName oluşturuldu" -ForegroundColor Green
    } else {
        Write-Host "✗ $imageName oluşturulamadı" -ForegroundColor Red
    }
}

Write-Host "`nTüm imajlar oluşturuldu: $OutputPath" -ForegroundColor Green
Write-Host "MSIX paketleme işlemine devam edebilirsiniz." -ForegroundColor Yellow

# İmaj dosyalarını listele
Write-Host "`nOluşturulan dosyalar:" -ForegroundColor Cyan
Get-ChildItem -Path $OutputPath -Filter "*.png" | ForEach-Object {
    $size = (Get-ItemProperty $_.FullName | Select-Object -ExpandProperty Length)
    Write-Host "  $($_.Name) - $([math]::Round($size/1KB, 2)) KB" -ForegroundColor White
}

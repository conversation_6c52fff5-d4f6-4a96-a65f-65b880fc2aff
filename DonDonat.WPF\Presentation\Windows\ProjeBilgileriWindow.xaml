<Window x:Class="DonDonat.WPF.Presentation.Windows.ProjeBilgileriWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:views="clr-namespace:DonDonat.WPF.Presentation.Views"
        mc:Ignorable="d"
        Title="Proje Bilgileri - DonDonat" 
        Height="800" Width="1000"
        WindowStartupLocation="CenterScreen"
        ResizeMode="CanResize"
        Icon="pack://application:,,,/Resources/icon.ico">

    <Window.Resources>
        <!-- Window için özel stiller -->
        <Style x:Key="WindowButtonStyle" TargetType="Button">
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Background" Value="#34495E"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="4" 
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#2C3E50"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#1B2631"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Ana İçerik - ProjeBilgileriView -->
        <views:ProjeBilgileriView Grid.Row="0"/>

        <!-- Alt Butonlar -->
        <Border Grid.Row="1" Background="#ECF0F1" Padding="15">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Content="✅ Tamam" 
                        Click="TamamButton_Click"
                        Style="{StaticResource WindowButtonStyle}"
                        Background="#27AE60"/>
                
                <Button Content="❌ İptal" 
                        Click="IptalButton_Click"
                        Style="{StaticResource WindowButtonStyle}"
                        Background="#E74C3C"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>

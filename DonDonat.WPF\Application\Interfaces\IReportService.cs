using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using DonDonat.WPF.Domain.Entities;

namespace DonDonat.WPF.Application.Interfaces
{
    /// <summary>
    /// Raporlama servisi için interface
    /// Excel ve PDF export işlemlerini yönetir
    /// </summary>
    public interface IReportService
    {
        /// <summary>
        /// Donatı listesini Excel formatında export eder
        /// </summary>
        /// <param name="donatilar">Export edilecek donatı listesi</param>
        /// <param name="projeBilgileri">Proje bilgileri (opsiyonel)</param>
        /// <param name="filePath">Kaydedilecek dosya yolu</param>
        /// <returns>Export işleminin başarılı olup olmadığı</returns>
        Task<bool> ExportToExcelAsync(List<Donati> donatilar, ProjeBilgileri? projeBilgileri, string filePath);

        /// <summary>
        /// Donatı listesini PDF formatında export eder
        /// </summary>
        /// <param name="donatilar">Export edilecek donatı listesi</param>
        /// <param name="projeBilgileri">Proje bilgileri (opsiyonel)</param>
        /// <param name="filePath">Kaydedilecek dosya yolu</param>
        /// <returns>Export işleminin başarılı olup olmadığı</returns>
        Task<bool> ExportToPdfAsync(List<Donati> donatilar, ProjeBilgileri? projeBilgileri, string filePath);

        /// <summary>
        /// Excel export için varsayılan dosya yolunu oluşturur
        /// </summary>
        /// <param name="projeAdi">Proje adı (dosya adında kullanılır)</param>
        /// <returns>Varsayılan Excel dosya yolu</returns>
        string GetDefaultExcelFilePath(string projeAdi = "");

        /// <summary>
        /// PDF export için varsayılan dosya yolunu oluşturur
        /// </summary>
        /// <param name="projeAdi">Proje adı (dosya adında kullanılır)</param>
        /// <returns>Varsayılan PDF dosya yolu</returns>
        string GetDefaultPdfFilePath(string projeAdi = "");

        /// <summary>
        /// Export işlemi için donatı listesini doğrular
        /// </summary>
        /// <param name="donatilar">Doğrulanacak donatı listesi</param>
        /// <returns>Doğrulama sonucu ve hata mesajları</returns>
        (bool IsValid, string[] ErrorMessages) ValidateExportData(List<Donati> donatilar);

        /// <summary>
        /// Export işlemi için rapor özeti oluşturur
        /// </summary>
        /// <param name="donatilar">Donatı listesi</param>
        /// <returns>Rapor özet bilgileri</returns>
        ReportSummary CreateReportSummary(List<Donati> donatilar);

        /// <summary>
        /// Dosya yolunun geçerli olup olmadığını kontrol eder
        /// </summary>
        /// <param name="filePath">Kontrol edilecek dosya yolu</param>
        /// <returns>Dosya yolu geçerli mi</returns>
        bool IsValidFilePath(string filePath);

        /// <summary>
        /// Dosya yazma izni olup olmadığını kontrol eder
        /// </summary>
        /// <param name="filePath">Kontrol edilecek dosya yolu</param>
        /// <returns>Yazma izni var mı</returns>
        bool HasWritePermission(string filePath);
    }

    /// <summary>
    /// Rapor özet bilgileri
    /// </summary>
    public class ReportSummary
    {
        /// <summary>
        /// Toplam donatı adedi
        /// </summary>
        public int ToplamAdet { get; set; }

        /// <summary>
        /// Toplam donatı uzunluğu (metre)
        /// </summary>
        public double ToplamUzunluk { get; set; }

        /// <summary>
        /// Toplam donatı ağırlığı (kg)
        /// </summary>
        public double ToplamAgirlik { get; set; }

        /// <summary>
        /// Farklı çap sayısı
        /// </summary>
        public int FarkliCapSayisi { get; set; }

        /// <summary>
        /// Farklı yapı elemanı sayısı
        /// </summary>
        public int FarkliYapiElemaniSayisi { get; set; }

        /// <summary>
        /// En çok kullanılan çap
        /// </summary>
        public int EnCokKullanilanCap { get; set; }

        /// <summary>
        /// Çap bazında dağılım
        /// </summary>
        public Dictionary<int, int> CapDagilimi { get; set; } = new();

        /// <summary>
        /// Yapı elemanı bazında dağılım
        /// </summary>
        public Dictionary<string, int> YapiElemaniDagilimi { get; set; } = new();

        /// <summary>
        /// Rapor oluşturulma tarihi
        /// </summary>
        public DateTime RaporTarihi { get; set; } = DateTime.Now;
    }
}

using System.Threading.Tasks;

namespace DonDonat.WPF.Application.Services
{
    /// <summary>
    /// Asenkron başlatma desteği için interface
    /// ViewModels ve servisler için kullanılır
    /// </summary>
    public interface IAsyncInitializable
    {
        /// <summary>
        /// Asenkron başlatma işlemi
        /// </summary>
        /// <returns>Başlatma görevi</returns>
        Task InitializeAsync();

        /// <summary>
        /// Başlatma durumu
        /// </summary>
        bool IsInitialized { get; }
    }
}

using System.Windows;
using DonDonat.WPF.Presentation.ViewModels;

namespace DonDonat.WPF.Presentation.Windows
{
    /// <summary>
    /// ProjeBilgileriWindow.xaml için etkileşim mantığı
    /// </summary>
    public partial class ProjeBilgileriWindow : Window
    {
        private ProjeBilgileriViewModel? _viewModel;

        public ProjeBilgileriWindow()
        {
            InitializeComponent();
            
            // ViewModel'i al
            if (DataContext is ProjeBilgileriViewModel vm)
            {
                _viewModel = vm;
            }
        }

        /// <summary>
        /// Tamam butonu click event handler
        /// </summary>
        private void TamamButton_Click(object sender, RoutedEventArgs e)
        {
            // Kaydedilmemiş değişiklikler varsa kullanıcıya sor
            if (_viewModel?.HasUnsavedChanges == true)
            {
                var result = MessageBox.Show(
                    "Kaydedilmemiş değişiklikler var. Kaydetmek istiyor musunuz?",
                    "Kaydedilmemiş <PERSON>ğişik<PERSON>ler",
                    MessageBoxButton.YesNoCancel,
                    MessageBoxImage.Question);

                switch (result)
                {
                    case MessageBoxResult.Yes:
                        // Kaydet ve kapat
                        if (_viewModel.SaveCommand.CanExecute(null))
                        {
                            _viewModel.SaveCommand.Execute(null);
                            // Kaydetme işlemi tamamlandıktan sonra kapat
                            // (Gerçek uygulamada async işlem için event kullanılabilir)
                            this.DialogResult = true;
                            this.Close();
                        }
                        break;
                    case MessageBoxResult.No:
                        // Kaydetmeden kapat
                        this.DialogResult = true;
                        this.Close();
                        break;
                    case MessageBoxResult.Cancel:
                        // İptal et, pencereyi kapatma
                        return;
                }
            }
            else
            {
                // Değişiklik yoksa direkt kapat
                this.DialogResult = true;
                this.Close();
            }
        }

        /// <summary>
        /// İptal butonu click event handler
        /// </summary>
        private void IptalButton_Click(object sender, RoutedEventArgs e)
        {
            // Kaydedilmemiş değişiklikler varsa kullanıcıya sor
            if (_viewModel?.HasUnsavedChanges == true)
            {
                var result = MessageBox.Show(
                    "Kaydedilmemiş değişiklikler var. Çıkmak istediğinizden emin misiniz?",
                    "Kaydedilmemiş Değişiklikler",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result == MessageBoxResult.No)
                    return;
            }

            this.DialogResult = false;
            this.Close();
        }

        /// <summary>
        /// Window kapatılırken çağrılır
        /// </summary>
        protected override void OnClosing(System.ComponentModel.CancelEventArgs e)
        {
            // Kaydedilmemiş değişiklikler varsa kullanıcıya sor
            if (_viewModel?.HasUnsavedChanges == true)
            {
                var result = MessageBox.Show(
                    "Kaydedilmemiş değişiklikler var. Çıkmak istediğinizden emin misiniz?",
                    "Kaydedilmemiş Değişiklikler",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result == MessageBoxResult.No)
                {
                    e.Cancel = true;
                    return;
                }
            }

            base.OnClosing(e);
        }
    }
}

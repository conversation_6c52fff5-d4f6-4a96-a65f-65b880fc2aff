using System;
using DonDonat.Tests.Helpers;
using DonDonat.WPF.Domain.Entities;

namespace DonDonat.Tests.Domain
{
    /// <summary>
    /// Donati entity için unit testler
    /// <PERSON>ş kuralları, hesaplamalar ve validasyonları test eder
    /// </summary>
    public class DonatiTests : TestBase
    {
        [Fact]
        public void Donati_DefaultConstructor_ShouldCreateValidInstance()
        {
            // Act
            var donati = new Donati();

            // Assert
            donati.Should().NotBeNull();
            donati.Id.Should().NotBe(Guid.Empty);
        }

        [Theory]
        [InlineData(8, 6.0, 10, 3.77)] // 8mm çap, 6m boy, 10 adet
        [InlineData(12, 4.5, 5, 2.54)] // 12mm çap, 4.5m boy, 5 adet
        [InlineData(16, 8.0, 15, 15.08)] // 16mm çap, 8m boy, 15 adet
        [InlineData(20, 10.0, 8, 19.74)] // 20mm çap, 10m boy, 8 adet
        public void TotalWeight_ShouldCalculateCorrectly(int cap, double boy, int adet, double expectedWeight)
        {
            // Arrange
            var donati = TestDataBuilder.CreateDonati(cap: cap, boy: boy, adet: adet);

            // Act
            var actualWeight = donati.TotalWeight;

            // Assert
            actualWeight.Should().BeApproximately(expectedWeight, 0.01);
        }

        [Theory]
        [InlineData(8, 6.0, 10, 60.0)] // 8mm çap, 6m boy, 10 adet = 60m
        [InlineData(12, 4.5, 5, 22.5)] // 12mm çap, 4.5m boy, 5 adet = 22.5m
        [InlineData(16, 8.0, 15, 120.0)] // 16mm çap, 8m boy, 15 adet = 120m
        public void TotalLength_ShouldCalculateCorrectly(int cap, double boy, int adet, double expectedLength)
        {
            // Arrange
            var donati = TestDataBuilder.CreateDonati(cap: cap, boy: boy, adet: adet);

            // Act
            var actualLength = donati.TotalLength;

            // Assert
            actualLength.Should().BeApproximately(expectedLength, 0.01);
        }

        [Theory]
        [InlineData(8, 0.395)] // 8mm çap = 0.395 kg/m
        [InlineData(10, 0.617)] // 10mm çap = 0.617 kg/m
        [InlineData(12, 0.888)] // 12mm çap = 0.888 kg/m
        [InlineData(14, 1.208)] // 14mm çap = 1.208 kg/m
        [InlineData(16, 1.578)] // 16mm çap = 1.578 kg/m
        [InlineData(18, 1.998)] // 18mm çap = 1.998 kg/m
        [InlineData(20, 2.466)] // 20mm çap = 2.466 kg/m
        [InlineData(22, 2.984)] // 22mm çap = 2.984 kg/m
        [InlineData(25, 3.853)] // 25mm çap = 3.853 kg/m
        [InlineData(28, 4.834)] // 28mm çap = 4.834 kg/m
        public void UnitWeight_ShouldReturnCorrectWeightPerMeter(int cap, double expectedUnitWeight)
        {
            // Arrange
            var donati = TestDataBuilder.CreateDonati(cap: cap);

            // Act
            var actualUnitWeight = donati.UnitWeight;

            // Assert
            actualUnitWeight.Should().BeApproximately(expectedUnitWeight, 0.001);
        }

        [Fact]
        public void UnitWeight_WithInvalidDiameter_ShouldReturnZero()
        {
            // Arrange
            var donati = TestDataBuilder.CreateDonati(cap: 999); // Geçersiz çap

            // Act
            var unitWeight = donati.UnitWeight;

            // Assert
            unitWeight.Should().Be(0.0);
        }

        [Theory]
        [InlineData(0)]
        [InlineData(-1)]
        [InlineData(-10)]
        public void Cap_WithInvalidValue_ShouldHandleGracefully(int invalidCap)
        {
            // Arrange & Act
            var donati = TestDataBuilder.CreateDonati(cap: invalidCap);

            // Assert
            donati.Cap.Should().Be(invalidCap);
            donati.UnitWeight.Should().Be(0.0);
            donati.TotalWeight.Should().Be(0.0);
        }

        [Theory]
        [InlineData(0)]
        [InlineData(-1)]
        [InlineData(-5.5)]
        public void Boy_WithInvalidValue_ShouldHandleGracefully(double invalidBoy)
        {
            // Arrange & Act
            var donati = TestDataBuilder.CreateDonati(boy: invalidBoy);

            // Assert
            donati.Boy.Should().Be(invalidBoy);
            donati.TotalLength.Should().Be(0.0); // Negatif boy ile toplam uzunluk 0 olmalı
        }

        [Theory]
        [InlineData(0)]
        [InlineData(-1)]
        [InlineData(-10)]
        public void Adet_WithInvalidValue_ShouldHandleGracefully(int invalidAdet)
        {
            // Arrange & Act
            var donati = TestDataBuilder.CreateDonati(adet: invalidAdet);

            // Assert
            donati.Adet.Should().Be(invalidAdet);
            donati.TotalLength.Should().Be(0.0); // Negatif adet ile toplam uzunluk 0 olmalı
            donati.TotalWeight.Should().Be(0.0); // Negatif adet ile toplam ağırlık 0 olmalı
        }

        [Fact]
        public void PropertyChanged_ShouldBeRaisedWhenCapChanges()
        {
            // Arrange
            var donati = TestDataBuilder.CreateDefaultDonati();
            var propertyChangedRaised = false;
            string? changedPropertyName = null;

            donati.PropertyChanged += (sender, e) =>
            {
                propertyChangedRaised = true;
                changedPropertyName = e.PropertyName;
            };

            // Act
            donati.Cap = 16;

            // Assert
            propertyChangedRaised.Should().BeTrue();
            changedPropertyName.Should().Be(nameof(Donati.Cap));
        }

        [Fact]
        public void PropertyChanged_ShouldBeRaisedWhenBoyChanges()
        {
            // Arrange
            var donati = TestDataBuilder.CreateDefaultDonati();
            var propertyChangedRaised = false;
            string? changedPropertyName = null;

            donati.PropertyChanged += (sender, e) =>
            {
                propertyChangedRaised = true;
                changedPropertyName = e.PropertyName;
            };

            // Act
            donati.Boy = 8.5;

            // Assert
            propertyChangedRaised.Should().BeTrue();
            changedPropertyName.Should().Be(nameof(Donati.Boy));
        }

        [Fact]
        public void PropertyChanged_ShouldBeRaisedWhenAdetChanges()
        {
            // Arrange
            var donati = TestDataBuilder.CreateDefaultDonati();
            var propertyChangedRaised = false;
            string? changedPropertyName = null;

            donati.PropertyChanged += (sender, e) =>
            {
                propertyChangedRaised = true;
                changedPropertyName = e.PropertyName;
            };

            // Act
            donati.Adet = 25;

            // Assert
            propertyChangedRaised.Should().BeTrue();
            changedPropertyName.Should().Be(nameof(Donati.Adet));
        }

        [Fact]
        public void ToString_ShouldReturnFormattedString()
        {
            // Arrange
            var donati = TestDataBuilder.CreateDonati(cap: 12, boy: 6.0, adet: 10);

            // Act
            var result = donati.ToString();

            // Assert
            result.Should().Contain("12");
            result.Should().Contain("6");
            result.Should().Contain("10");
        }

        [Fact]
        public void Equals_WithSameId_ShouldReturnTrue()
        {
            // Arrange
            var id = Guid.NewGuid();
            var donati1 = TestDataBuilder.CreateDefaultDonati();
            var donati2 = TestDataBuilder.CreateDefaultDonati();
            
            // Aynı ID'yi ata
            donati1.Id = id;
            donati2.Id = id;

            // Act & Assert
            donati1.Equals(donati2).Should().BeTrue();
            (donati1 == donati2).Should().BeTrue();
        }

        [Fact]
        public void Equals_WithDifferentId_ShouldReturnFalse()
        {
            // Arrange
            var donati1 = TestDataBuilder.CreateDefaultDonati();
            var donati2 = TestDataBuilder.CreateDefaultDonati();

            // Act & Assert
            donati1.Equals(donati2).Should().BeFalse();
            (donati1 == donati2).Should().BeFalse();
        }

        [Fact]
        public void GetHashCode_WithSameId_ShouldReturnSameValue()
        {
            // Arrange
            var id = Guid.NewGuid();
            var donati1 = TestDataBuilder.CreateDefaultDonati();
            var donati2 = TestDataBuilder.CreateDefaultDonati();
            
            donati1.Id = id;
            donati2.Id = id;

            // Act & Assert
            donati1.GetHashCode().Should().Be(donati2.GetHashCode());
        }
    }
}

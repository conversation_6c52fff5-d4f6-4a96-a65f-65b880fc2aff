# Basit PNG'den ICO'ya Dönüştürücü
Add-Type -AssemblyName System.Drawing

try {
    Write-Host "🔄 PNG'den ICO'ya dönüştürülüyor..." -ForegroundColor Cyan
    
    $pngPath = "Resources\DonDonat.png"
    $icoPath = "Resources\DonDonat.ico"
    
    if (-not (Test-Path $pngPath)) {
        throw "PNG dosyası bulunamadı: $pngPath"
    }
    
    # PNG'yi yükle
    $image = [System.Drawing.Image]::FromFile((Resolve-Path $pngPath).Path)
    Write-Host "📂 PNG yüklendi: $($image.Width)x$($image.Height)" -ForegroundColor Green
    
    # 32x32 boyutunda bitmap oluştur
    $bitmap = New-Object System.Drawing.Bitmap(32, 32)
    $graphics = [System.Drawing.Graphics]::FromImage($bitmap)
    $graphics.InterpolationMode = [System.Drawing.Drawing2D.InterpolationMode]::HighQualityBicubic
    $graphics.DrawImage($image, 0, 0, 32, 32)
    
    # ICO olarak kaydet
    $bitmap.Save($icoPath, [System.Drawing.Imaging.ImageFormat]::Icon)
    
    Write-Host "✅ ICO dosyası oluşturuldu: $icoPath" -ForegroundColor Green
    
    # Cleanup
    $graphics.Dispose()
    $bitmap.Dispose()
    $image.Dispose()
    
    # Dosya boyutlarını göster
    if (Test-Path $icoPath) {
        $icoSize = [math]::Round((Get-Item $icoPath).Length / 1KB, 2)
        Write-Host "📄 ICO boyutu: $icoSize KB" -ForegroundColor Yellow
        Write-Host "🎉 Dönüştürme başarılı!" -ForegroundColor Green
    }
    
} catch {
    Write-Host "❌ Hata: $($_.Exception.Message)" -ForegroundColor Red
}

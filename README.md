# DonDonat - Betonarme Donatı Metraj Hesaplama Sistemi

## Proje Açıklaması

DonDonat, AutoCAD DWG dosyalarından donatı çizimlerini okuyup betonarme donatı metrajı hesaplayan bir masaüstü uygulamasıdır. Uygulama MVVM mimarisiyle C# ve WPF kullanılarak geliştirilmiştir.

## Özellikler

- ✅ DXF dosyalarından donatı bilgilerini otomatik okuma
- ✅ Donatı çapı, boyu, adedi ve türü tanımlama
- ✅ Toplam ağırlık ve uzunluk hesaplama
- ✅ Manuel donatı ekleme/silme
- ✅ **Proje Bilgileri Modülü** - Proje detaylarını yönetme
- ✅ JSON formatında proje bilgilerini kaydetme/yükleme
- ✅ **SQLite Veritabanı** - Kalıcı veri saklama sistemi
- ✅ **Entity Framework Core** - Modern ORM desteği
- ✅ **Repository Pattern** - Clean Architecture uyumlu veri erişimi
- ✅ Proje bilgileri doğrulama ve validation
- ✅ Modern ve kullanıcı dostu arayüz
- 🔄 Excel'e aktarma (geliştirilme aşamasında)

## Mimari

Proje Clean Architecture prensiplerine uygun olarak katmanlı mimari ile geliştirilmiştir:

```
DonDonat.WPF/
├── Domain/                    # Domain katmanı
│   └── Entities/
│       ├── Donati.cs         # Donatı varlık sınıfı
│       └── ProjeBilgileri.cs # Proje bilgileri varlık sınıfı
├── Application/               # Application katmanı
│   ├── Interfaces/
│   │   ├── IDonatiService.cs        # Donatı servis interface
│   │   └── IProjeBilgileriService.cs # Proje bilgileri servis interface
│   ├── Mappings/
│   │   ├── DonatiMapper.cs          # Entity-Domain mapping
│   │   └── ProjeBilgileriMapper.cs  # Entity-Domain mapping
│   └── Services/
│       ├── DonatiService.cs         # Donatı business logic servisi
│       └── ProjeBilgileriService.cs # Proje bilgileri servisi
├── Infrastructure/           # Infrastructure katmanı
│   ├── Parsers/
│   │   └── DWGParser.cs     # DXF dosya okuma sınıfı
│   └── Repositories/
│       ├── DonatiRepository.cs      # Donatı veri erişim katmanı
│       └── ProjeBilgileriRepository.cs # Proje bilgileri veri erişim katmanı
├── Persistence/             # Persistence katmanı
│   ├── Entities/
│   │   ├── DonatiEntity.cs          # Donatı veritabanı entity
│   │   └── ProjeBilgileriEntity.cs  # Proje bilgileri veritabanı entity
│   ├── Migrations/                  # EF Core migrations
│   └── DonDonatDbContext.cs         # Entity Framework DbContext
├── Presentation/            # Presentation katmanı
│   ├── ViewModels/
│   │   ├── MainViewModel.cs        # Ana görünüm modeli
│   │   └── ProjeBilgileriViewModel.cs # Proje bilgileri görünüm modeli
│   ├── Views/
│   │   └── ProjeBilgileriView.xaml # Proje bilgileri kullanıcı kontrolü
│   └── Windows/
│       └── ProjeBilgileriWindow.xaml # Proje bilgileri penceresi
├── MainWindow.xaml          # Ana pencere arayüzü
└── MainWindow.xaml.cs       # Ana pencere code-behind
```

## Teknolojiler

- **Framework**: .NET 9.0
- **UI Framework**: WPF (Windows Presentation Foundation)
- **Mimari Pattern**: MVVM (Model-View-ViewModel) + Clean Architecture
- **Dil**: C#
- **Veritabanı**: SQLite
- **ORM**: Entity Framework Core 9.0
- **Veri Erişim**: Repository Pattern
- **Dosya Formatı**: DXF (AutoCAD Drawing Exchange Format), JSON

## Kurulum ve Çalıştırma

### Gereksinimler
- .NET 9.0 SDK
- Windows 10/11
- Visual Studio 2022 veya VS Code

### Kurulum
1. Projeyi klonlayın:
   ```bash
   git clone <repository-url>
   cd DonDonat
   ```

2. Bağımlılıkları yükleyin:
   ```bash
   dotnet restore
   ```

3. Projeyi derleyin:
   ```bash
   dotnet build
   ```

4. Uygulamayı çalıştırın:
   ```bash
   dotnet run --project DonDonat.WPF
   ```

## Kullanım

### Ana Özellikler

1. **DXF Dosyası Seçme**: "DXF Dosyası Seç" butonuna tıklayarak AutoCAD DXF dosyanızı seçin.

2. **Dosyayı İşleme**: "Dosyayı İşle" butonuna tıklayarak donatı bilgilerini otomatik olarak çıkarın.

3. **Manuel Donatı Ekleme**: "Manuel Donatı Ekle" butonuyla yeni donatı ekleyebilirsiniz.

4. **Donatı Silme**: Listeden donatı seçip "Seçilen Donatıyı Sil" butonuyla silebilirsiniz.

5. **Özet Bilgiler**: Sağ panelde toplam adet, uzunluk ve ağırlık bilgilerini görüntüleyebilirsiniz.

### Proje Bilgileri Modülü

6. **Proje Bilgileri**: "📋 Proje Bilgileri" butonuna tıklayarak proje detaylarını yönetin.

7. **Proje Bilgilerini Girme**:
   - Proje adı, lokasyon, müteahhit firma bilgileri
   - Temel türü ve derinliği
   - Kat sayısı, kat yüksekliği, bodrum bilgileri
   - Döşeme kalınlığı ve birim seçimleri

8. **Kaydetme/Yükleme**:
   - "💾 Kaydet": Varsayılan konuma kaydet
   - "💾 Farklı Kaydet": Belirtilen konuma kaydet
   - "📁 Yükle": Mevcut proje dosyasını yükle

9. **Doğrulama**: "✅ Doğrula" butonuyla proje bilgilerini kontrol edin.

10. **Otomatik Hesaplamalar**: Toplam kat sayısı ve yapı yüksekliği otomatik hesaplanır.

## Donatı Sınıfı Özellikleri

`Donati.cs` sınıfı aşağıdaki özellikleri içerir:

- **Cap**: Donatı çapı (mm)
- **Boy**: Donatı boyu (metre)
- **Adet**: Donatı adedi
- **Tur**: Donatı türü (Nervürlü, Düz, vb.)
- **YapiElemani**: Yapı elemanı (Kiriş, Kolon, vb.)
- **Konum**: Donatının konumu (Üst, Alt, vb.)
- **DonatiSinifi**: Donatı sınıfı (S420, S500, vb.)
- **ToplamAgirlik**: Hesaplanan toplam ağırlık (kg)
- **ToplamUzunluk**: Hesaplanan toplam uzunluk (m)

## Proje Bilgileri Sınıfı Özellikleri

`ProjeBilgileri.cs` sınıfı aşağıdaki özellikleri içerir:

- **ProjeAdi**: Proje adı
- **TemelTuru**: Temel türü (Radye, Tekil, Sürekli, vb.)
- **TemelDerinligi**: Temel derinliği (metre)
- **KatSayisi**: Kat sayısı (bodrum hariç)
- **KatYuksekligi**: Kat yüksekliği (metre)
- **BodrumVarMi**: Bodrum var mı (boolean)
- **BodrumKatSayisi**: Bodrum kat sayısı
- **DosemeKalinligi**: Döşeme kalınlığı (cm)
- **UzunlukBirimi**: Uzunluk birimi (m, cm, mm)
- **AlanBirimi**: Alan birimi (m², cm², mm²)
- **ToplamKatSayisi**: Hesaplanan toplam kat sayısı
- **ToplamYapiYuksekligi**: Hesaplanan toplam yapı yüksekliği

## Veritabanı Yapısı

### Tablolar

#### Donatilar Tablosu
- **Id**: Primary Key (GUID)
- **Cap**: Donatı çapı (mm)
- **Boy**: Donatı boyu (metre)
- **Adet**: Donatı adedi
- **Tur**: Donatı türü
- **YapiElemani**: Yapı elemanı
- **Konum**: Donatı konumu
- **DonatiSinifi**: Donatı sınıfı
- **ProjeId**: Foreign Key (ProjeBilgileri tablosuna)
- **OlusturulmaTarihi**: Oluşturulma tarihi
- **GuncellenmeTarihi**: Güncellenme tarihi

#### ProjeBilgileri Tablosu
- **Id**: Primary Key (GUID)
- **ProjeAdi**: Proje adı
- **TemelTuru**: Temel türü
- **TemelDerinligi**: Temel derinliği (metre)
- **KatSayisi**: Kat sayısı
- **KatYuksekligi**: Kat yüksekliği (metre)
- **BodrumVarMi**: Bodrum var mı (boolean)
- **BodrumKatSayisi**: Bodrum kat sayısı
- **DosemeKalinligi**: Döşeme kalınlığı (cm)
- **UzunlukBirimi**: Uzunluk birimi
- **AlanBirimi**: Alan birimi
- **ProjeAciklamasi**: Proje açıklaması
- **ProjeLokasyonu**: Proje lokasyonu
- **MuteahhitFirma**: Müteahhit firma
- **ProjeMuhendisi**: Proje mühendisi
- **AktifProje**: Aktif proje mi (boolean)
- **OlusturulmaTarihi**: Oluşturulma tarihi
- **GuncellenmeTarihi**: Güncellenme tarihi

### Veritabanı Konumu
- **Windows**: `%APPDATA%\DonDonat\DonDonat.db`
- **Otomatik Oluşturma**: İlk çalıştırmada otomatik olarak oluşturulur
- **Test Verileri**: Örnek proje ve donatı verileri otomatik eklenir

## DXF Parser Özellikleri

`DWGParser.cs` sınıfı aşağıdaki işlevleri gerçekleştirir:

- DXF dosyalarından TEXT ve MTEXT varlıklarını okuma
- LINE varlıklarından donatı uzunluklarını hesaplama
- Regex ile donatı bilgilerini parse etme
- Donatı çapı, adet ve boy bilgilerini otomatik tanıma

## Gelecek Geliştirmeler

- [ ] Excel export işlevselliği
- [ ] Donatı düzenleme formu
- [ ] Proje kaydetme/yükleme
- [ ] Rapor oluşturma
- [ ] Donatı çizimleri görselleştirme
- [ ] Veritabanı entegrasyonu
- [ ] Çoklu dosya işleme

## Katkıda Bulunma

1. Fork edin
2. Feature branch oluşturun (`git checkout -b feature/AmazingFeature`)
3. Değişikliklerinizi commit edin (`git commit -m 'Add some AmazingFeature'`)
4. Branch'inizi push edin (`git push origin feature/AmazingFeature`)
5. Pull Request oluşturun

## Lisans

Bu proje MIT lisansı altında lisanslanmıştır. Detaylar için `LICENSE` dosyasına bakın.

## İletişim

Proje Sahibi - [İsim] - [<EMAIL>]

Proje Linki: [https://github.com/username/DonDonat](https://github.com/username/DonDonat)

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using DonDonat.WPF.Domain.Entities;
using iTextSharp.text;
using iTextSharp.text.pdf;

namespace DonDonat.WPF.Infrastructure.Exporters
{
    /// <summary>
    /// PDF dosyası oluşturma servisi
    /// iTextSharp kütüphanesi kullanılarak PDF raporları oluşturur
    /// </summary>
    public class PdfExporter
    {
        private readonly BaseFont _baseFont;
        private readonly Font _titleFont;
        private readonly Font _headerFont;
        private readonly Font _normalFont;
        private readonly Font _boldFont;
        private readonly Font _smallFont;

        public PdfExporter()
        {
            // Türkçe karakter desteği için font ayarları
            _baseFont = BaseFont.CreateFont(BaseFont.HELVETICA, BaseFont.CP1252, BaseFont.NOT_EMBEDDED);

            _titleFont = new Font(_baseFont, 18, Font.BOLD, BaseColor.DarkGray);
            _headerFont = new Font(_baseFont, 12, Font.BOLD, BaseColor.Black);
            _normalFont = new Font(_baseFont, 10, Font.NORMAL, BaseColor.Black);
            _boldFont = new Font(_baseFont, 10, Font.BOLD, BaseColor.Black);
            _smallFont = new Font(_baseFont, 8, Font.NORMAL, BaseColor.Gray);
        }

        /// <summary>
        /// Donatı listesinden PDF raporu oluşturur
        /// </summary>
        /// <param name="donatilar">Donatı listesi</param>
        /// <param name="projeBilgileri">Proje bilgileri</param>
        /// <param name="filePath">Kaydedilecek dosya yolu</param>
        /// <returns>İşlem başarılı mı</returns>
        public Task<bool> CreateDonatiReportAsync(
            List<Donati> donatilar,
            ProjeBilgileri? projeBilgileri,
            string filePath)
        {
            try
            {
                // PDF dokümanı oluştur
                var document = new Document(PageSize.A4.Rotate(), 25, 25, 30, 30); // Landscape
                var writer = PdfWriter.GetInstance(document, new FileStream(filePath, FileMode.Create));
                
                // Sayfa numarası için event handler
                writer.PageEvent = new PdfPageEventHelper();
                
                document.Open();

                // Başlık ve proje bilgileri
                AddHeaderSection(document, projeBilgileri);

                // Donatı tablosu
                AddDonatiTable(document, donatilar);

                // Özet bilgiler
                AddSummarySection(document, donatilar);

                // Alt bilgi
                AddFooter(document);

                document.Close();
                return Task.FromResult(true);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"PDF export hatası: {ex.Message}");
                return Task.FromResult(false);
            }
        }

        /// <summary>
        /// Başlık ve proje bilgileri bölümünü ekler
        /// </summary>
        private void AddHeaderSection(Document document, ProjeBilgileri? projeBilgileri)
        {
            // Ana başlık
            var titleParagraph = new Paragraph("BETONARME DONATI METRAJ RAPORU", _titleFont)
            {
                Alignment = Element.ALIGN_CENTER,
                SpacingAfter = 20
            };
            document.Add(titleParagraph);

            // Proje bilgileri tablosu
            if (projeBilgileri != null)
            {
                var infoTable = new PdfPTable(4) { WidthPercentage = 100 };
                infoTable.SetWidths(new float[] { 20, 30, 20, 30 });

                // Proje bilgileri satırları
                AddInfoRow(infoTable, "Proje Adı:", projeBilgileri.ProjeAdi, "Temel Türü:", projeBilgileri.TemelTuru);
                AddInfoRow(infoTable, "Lokasyon:", projeBilgileri.ProjeLokasyonu, "Kat Sayısı:", 
                    $"{projeBilgileri.KatSayisi} + {projeBilgileri.BodrumKatSayisi} Bodrum");
                AddInfoRow(infoTable, "Müteahhit:", projeBilgileri.MuteahhitFirma, "Mühendis:", projeBilgileri.ProjeMuhendisi);
                AddInfoRow(infoTable, "Rapor Tarihi:", DateTime.Now.ToString("dd.MM.yyyy HH:mm"), "", "");

                infoTable.SpacingAfter = 20;
                document.Add(infoTable);
            }
            else
            {
                var dateParagraph = new Paragraph($"Rapor Tarihi: {DateTime.Now:dd.MM.yyyy HH:mm}", _normalFont)
                {
                    Alignment = Element.ALIGN_RIGHT,
                    SpacingAfter = 20
                };
                document.Add(dateParagraph);
            }
        }

        /// <summary>
        /// Bilgi satırı ekler
        /// </summary>
        private void AddInfoRow(PdfPTable table, string label1, string value1, string label2, string value2)
        {
            table.AddCell(new PdfPCell(new Phrase(label1, _boldFont)) { Border = Rectangle.NO_BORDER, PaddingBottom = 5 });
            table.AddCell(new PdfPCell(new Phrase(value1, _normalFont)) { Border = Rectangle.NO_BORDER, PaddingBottom = 5 });
            table.AddCell(new PdfPCell(new Phrase(label2, _boldFont)) { Border = Rectangle.NO_BORDER, PaddingBottom = 5 });
            table.AddCell(new PdfPCell(new Phrase(value2, _normalFont)) { Border = Rectangle.NO_BORDER, PaddingBottom = 5 });
        }

        /// <summary>
        /// Donatı tablosunu ekler
        /// </summary>
        private void AddDonatiTable(Document document, List<Donati> donatilar)
        {
            // Tablo başlığı
            var tableTitle = new Paragraph("DONATI LİSTESİ", _headerFont)
            {
                Alignment = Element.ALIGN_LEFT,
                SpacingAfter = 10
            };
            document.Add(tableTitle);

            // Tablo oluştur
            var table = new PdfPTable(10) { WidthPercentage = 100 };
            table.SetWidths(new float[] { 6, 8, 8, 6, 12, 15, 10, 8, 12, 10 });

            // Başlık satırı
            var headers = new[]
            {
                "Sıra", "Çap\n(mm)", "Boy\n(m)", "Adet", "Tür", 
                "Yapı Elemanı", "Konum", "Sınıf", "Toplam\nUzunluk (m)", "Ağırlık\n(kg)"
            };

            foreach (var header in headers)
            {
                var cell = new PdfPCell(new Phrase(header, _boldFont))
                {
                    BackgroundColor = BaseColor.LightGray,
                    HorizontalAlignment = Element.ALIGN_CENTER,
                    VerticalAlignment = Element.ALIGN_MIDDLE,
                    Padding = 5
                };
                table.AddCell(cell);
            }

            // Donatı verileri
            int siraNo = 1;
            foreach (var donati in donatilar.OrderBy(d => d.YapiElemani).ThenBy(d => d.Cap))
            {
                // Alternatif satır rengi
                var backgroundColor = siraNo % 2 == 0 ? BaseColor.White : new BaseColor(248, 248, 248);

                AddDataCell(table, siraNo.ToString(), Element.ALIGN_CENTER, backgroundColor);
                AddDataCell(table, donati.Cap.ToString(), Element.ALIGN_CENTER, backgroundColor);
                AddDataCell(table, donati.Boy.ToString("F2"), Element.ALIGN_RIGHT, backgroundColor);
                AddDataCell(table, donati.Adet.ToString(), Element.ALIGN_CENTER, backgroundColor);
                AddDataCell(table, donati.Tur, Element.ALIGN_CENTER, backgroundColor);
                AddDataCell(table, donati.YapiElemani, Element.ALIGN_CENTER, backgroundColor);
                AddDataCell(table, donati.Konum, Element.ALIGN_CENTER, backgroundColor);
                AddDataCell(table, donati.DonatiSinifi, Element.ALIGN_CENTER, backgroundColor);
                AddDataCell(table, donati.ToplamUzunluk.ToString("F2"), Element.ALIGN_RIGHT, backgroundColor);
                AddDataCell(table, donati.ToplamAgirlik.ToString("F2"), Element.ALIGN_RIGHT, backgroundColor);

                siraNo++;
            }

            table.SpacingAfter = 20;
            document.Add(table);
        }

        /// <summary>
        /// Veri hücresi ekler
        /// </summary>
        private void AddDataCell(PdfPTable table, string text, int alignment, BaseColor backgroundColor)
        {
            var cell = new PdfPCell(new Phrase(text, _normalFont))
            {
                HorizontalAlignment = alignment,
                VerticalAlignment = Element.ALIGN_MIDDLE,
                BackgroundColor = backgroundColor,
                Padding = 3
            };
            table.AddCell(cell);
        }

        /// <summary>
        /// Özet bilgiler bölümünü ekler
        /// </summary>
        private void AddSummarySection(Document document, List<Donati> donatilar)
        {
            // Özet başlığı
            var summaryTitle = new Paragraph("ÖZET BİLGİLER", _headerFont)
            {
                Alignment = Element.ALIGN_LEFT,
                SpacingAfter = 10
            };
            document.Add(summaryTitle);

            // Toplam değerler
            var toplamAdet = donatilar.Sum(d => d.Adet);
            var toplamUzunluk = donatilar.Sum(d => d.ToplamUzunluk);
            var toplamAgirlik = donatilar.Sum(d => d.ToplamAgirlik);

            // Özet tablosu
            var summaryTable = new PdfPTable(2) { WidthPercentage = 50 };
            summaryTable.SetWidths(new float[] { 70, 30 });

            AddSummaryRow(summaryTable, "Toplam Donatı Adedi:", toplamAdet.ToString());
            AddSummaryRow(summaryTable, "Toplam Uzunluk (m):", toplamUzunluk.ToString("F2"));
            AddSummaryRow(summaryTable, "Toplam Ağırlık (kg):", toplamAgirlik.ToString("F2"));

            summaryTable.SpacingAfter = 20;
            document.Add(summaryTable);

            // Çap bazında özet
            var capOzeti = donatilar.GroupBy(d => d.Cap)
                .Select(g => new
                {
                    Cap = g.Key,
                    Adet = g.Sum(d => d.Adet),
                    Uzunluk = g.Sum(d => d.ToplamUzunluk),
                    Agirlik = g.Sum(d => d.ToplamAgirlik)
                })
                .OrderBy(x => x.Cap)
                .ToList();

            if (capOzeti.Any())
            {
                var capTitle = new Paragraph("ÇAP BAZINDA ÖZET", _headerFont)
                {
                    Alignment = Element.ALIGN_LEFT,
                    SpacingAfter = 10
                };
                document.Add(capTitle);

                var capTable = new PdfPTable(4) { WidthPercentage = 70 };
                capTable.SetWidths(new float[] { 25, 25, 25, 25 });

                // Başlıklar
                AddCapHeaderCell(capTable, "Çap (mm)");
                AddCapHeaderCell(capTable, "Adet");
                AddCapHeaderCell(capTable, "Uzunluk (m)");
                AddCapHeaderCell(capTable, "Ağırlık (kg)");

                // Veriler
                foreach (var item in capOzeti)
                {
                    AddDataCell(capTable, $"Ø{item.Cap}", Element.ALIGN_CENTER, BaseColor.White);
                    AddDataCell(capTable, item.Adet.ToString(), Element.ALIGN_RIGHT, BaseColor.White);
                    AddDataCell(capTable, item.Uzunluk.ToString("F2"), Element.ALIGN_RIGHT, BaseColor.White);
                    AddDataCell(capTable, item.Agirlik.ToString("F2"), Element.ALIGN_RIGHT, BaseColor.White);
                }

                capTable.SpacingAfter = 20;
                document.Add(capTable);
            }
        }

        /// <summary>
        /// Özet satırı ekler
        /// </summary>
        private void AddSummaryRow(PdfPTable table, string label, string value)
        {
            table.AddCell(new PdfPCell(new Phrase(label, _boldFont)) 
            { 
                Border = Rectangle.NO_BORDER, 
                PaddingBottom = 5,
                HorizontalAlignment = Element.ALIGN_LEFT
            });
            table.AddCell(new PdfPCell(new Phrase(value, _normalFont)) 
            { 
                Border = Rectangle.NO_BORDER, 
                PaddingBottom = 5,
                HorizontalAlignment = Element.ALIGN_RIGHT
            });
        }

        /// <summary>
        /// Çap özeti başlık hücresi ekler
        /// </summary>
        private void AddCapHeaderCell(PdfPTable table, string text)
        {
            var cell = new PdfPCell(new Phrase(text, _boldFont))
            {
                BackgroundColor = BaseColor.LightGray,
                HorizontalAlignment = Element.ALIGN_CENTER,
                VerticalAlignment = Element.ALIGN_MIDDLE,
                Padding = 5
            };
            table.AddCell(cell);
        }

        /// <summary>
        /// Alt bilgi ekler
        /// </summary>
        private void AddFooter(Document document)
        {
            var footer = new Paragraph($"Bu rapor DonDonat v1.0 tarafından {DateTime.Now:dd.MM.yyyy HH:mm} tarihinde oluşturulmuştur.", _smallFont)
            {
                Alignment = Element.ALIGN_CENTER,
                SpacingBefore = 30
            };
            document.Add(footer);
        }
    }

    /// <summary>
    /// PDF sayfa olayları için yardımcı sınıf
    /// </summary>
    public class PdfPageEventHelper : PdfPageEventHelperCustom
    {
        private readonly BaseFont _baseFont;
        private readonly Font _footerFont;

        public PdfPageEventHelper()
        {
            _baseFont = BaseFont.CreateFont(BaseFont.HELVETICA, BaseFont.CP1252, BaseFont.NOT_EMBEDDED);
            _footerFont = new Font(_baseFont, 8, Font.NORMAL, BaseColor.Gray);
        }

        public override void OnEndPage(PdfWriter writer, Document document)
        {
            // Sayfa numarası
            var pageNumber = writer.PageNumber;
            var text = $"Sayfa {pageNumber}";
            
            var cb = writer.DirectContent;
            var phrase = new Phrase(text, _footerFont);
            
            ColumnText.ShowTextAligned(cb, Element.ALIGN_RIGHT, phrase,
                document.Right, document.Bottom - 10, 0);
        }
    }

    /// <summary>
    /// Custom PDF page event helper base class
    /// </summary>
    public abstract class PdfPageEventHelperCustom : IPdfPageEvent
    {
        public virtual void OnOpenDocument(PdfWriter writer, Document document) { }
        public virtual void OnCloseDocument(PdfWriter writer, Document document) { }
        public virtual void OnStartPage(PdfWriter writer, Document document) { }
        public virtual void OnEndPage(PdfWriter writer, Document document) { }
        public virtual void OnParagraph(PdfWriter writer, Document document, float paragraphPosition) { }
        public virtual void OnParagraphEnd(PdfWriter writer, Document document, float paragraphPosition) { }
        public virtual void OnChapter(PdfWriter writer, Document document, float paragraphPosition, Paragraph title) { }
        public virtual void OnChapterEnd(PdfWriter writer, Document document, float paragraphPosition) { }
        public virtual void OnSection(PdfWriter writer, Document document, float paragraphPosition, int depth, Paragraph title) { }
        public virtual void OnSectionEnd(PdfWriter writer, Document document, float paragraphPosition) { }
        public virtual void OnGenericTag(PdfWriter writer, Document document, Rectangle rect, string text) { }
    }
}

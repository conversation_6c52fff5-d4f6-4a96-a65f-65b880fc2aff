# DonDonat Debug Başlatma Scripti
# Bu script, <PERSON><PERSON><PERSON><PERSON> u<PERSON>lam<PERSON>ını geliştirici modunda başlatır

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("Debug", "Release")]
    [string]$Configuration = "Debug",
    
    [Parameter(Mandatory=$false)]
    [switch]$CleanFirst,
    
    [Parameter(Mandatory=$false)]
    [switch]$Verbose,
    
    [Parameter(Mandatory=$false)]
    [switch]$WaitForDebugger
)

# Renkli çıktı fonksiyonları
function Write-Success { param($Message) Write-Host $Message -ForegroundColor Green }
function Write-Info { param($Message) Write-Host $Message -ForegroundColor Cyan }
function Write-Warning { param($Message) Write-Host $Message -ForegroundColor Yellow }
function Write-Error { param($Message) Write-Host $Message -ForegroundColor Red }

Write-Info "🏗️ DonDonat Debug Başlatma Scripti"
Write-Info "=================================="
Write-Info "Konfigürasyon: $Configuration"
Write-Info "Temizlik: $CleanFirst"
Write-Info "Verbose: $Verbose"

try {
    # Proje klasörüne git
    $ProjectPath = "DonDonat.WPF"
    if (-not (Test-Path $ProjectPath)) {
        throw "Proje klasörü bulunamadı: $ProjectPath"
    }

    Set-Location $ProjectPath
    Write-Success "✅ Proje klasörüne geçildi: $ProjectPath"

    # Temizlik yap
    if ($CleanFirst) {
        Write-Info "🧹 Proje temizleniyor..."
        dotnet clean --configuration $Configuration --verbosity minimal
        
        # Log ve temp klasörlerini temizle
        if (Test-Path "Logs") { Remove-Item "Logs\*" -Force -Recurse -ErrorAction SilentlyContinue }
        if (Test-Path "Temp") { Remove-Item "Temp\*" -Force -Recurse -ErrorAction SilentlyContinue }
        
        Write-Success "✅ Temizlik tamamlandı"
    }

    # Projeyi derle
    Write-Info "🔨 Proje derleniyor..."
    $BuildArgs = @("build", "--configuration", $Configuration)
    if ($Verbose) { $BuildArgs += "--verbosity", "detailed" }
    else { $BuildArgs += "--verbosity", "minimal" }

    & dotnet @BuildArgs
    if ($LASTEXITCODE -ne 0) {
        throw "Proje derlenemedi"
    }
    Write-Success "✅ Proje başarıyla derlendi"

    # Environment variables ayarla
    $env:DOTNET_ENVIRONMENT = "Development"
    $env:ASPNETCORE_ENVIRONMENT = "Development"
    $env:DONDONAT_DEBUG = "true"
    
    Write-Info "🔧 Environment variables ayarlandı"

    # Debugger bekleme
    if ($WaitForDebugger) {
        Write-Warning "⏳ Debugger bekleniyor... Visual Studio'dan Attach to Process yapın"
        Write-Info "Process ID: $PID"
        Read-Host "Debugger attach edildiğinde Enter'a basın"
    }

    # Uygulamayı başlat
    Write-Info "🚀 DonDonat uygulaması başlatılıyor..."
    Write-Info "📅 Başlangıç zamanı: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"
    
    $RunArgs = @("run", "--configuration", $Configuration, "--no-build")
    if ($Verbose) { $RunArgs += "--verbosity", "detailed" }
    
    & dotnet @RunArgs

} catch {
    Write-Error "❌ Hata: $($_.Exception.Message)"
    Write-Error "Detaylar: $($_.Exception.StackTrace)"
    exit 1
} finally {
    # Başlangıç klasörüne dön
    Set-Location $PSScriptRoot
}

Write-Success "✅ Script tamamlandı"

using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using DonDonat.WPF.Application.Interfaces;
using DonDonat.WPF.Application.Services;
using DonDonat.WPF.Domain.Entities;
using Microsoft.Win32;

namespace DonDonat.WPF.Presentation.ViewModels
{
    /// <summary>
    /// Proje Bilgileri için ViewModel sınıfı
    /// MVVM pattern'ine uygun olarak tasarlanmıştır
    /// </summary>
    public class ProjeBilgileriViewModel : INotifyPropertyChanged
    {
        private readonly IProjeBilgileriService _projeBilgileriService;
        private ProjeBilgileri _projeBilgileri;
        private bool _isLoading = false;
        private string _statusMessage = "Hazır";
        private bool _hasUnsavedChanges = false;

        public ProjeBilgileriViewModel()
        {
            _projeBilgileriService = new ProjeBilgileriService();
            _projeBilgileri = _projeBilgileriService.CreateDefaultProjeBilgileri();
            
            InitializeCollections();
            InitializeCommands();
            _ = LoadProjeBilgileriAsync(); // Fire and forget
        }

        #region Properties

        /// <summary>
        /// Proje bilgileri nesnesi
        /// </summary>
        public ProjeBilgileri ProjeBilgileri
        {
            get => _projeBilgileri;
            set
            {
                _projeBilgileri = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(ToplamKatSayisi));
                OnPropertyChanged(nameof(ToplamYapiYuksekligi));
                HasUnsavedChanges = true;
            }
        }

        /// <summary>
        /// Proje adı
        /// </summary>
        public string ProjeAdi
        {
            get => _projeBilgileri.ProjeAdi;
            set
            {
                _projeBilgileri.ProjeAdi = value;
                OnPropertyChanged();
                HasUnsavedChanges = true;
            }
        }

        /// <summary>
        /// Temel türü
        /// </summary>
        public string TemelTuru
        {
            get => _projeBilgileri.TemelTuru;
            set
            {
                _projeBilgileri.TemelTuru = value;
                OnPropertyChanged();
                HasUnsavedChanges = true;
            }
        }

        /// <summary>
        /// Temel derinliği
        /// </summary>
        public double TemelDerinligi
        {
            get => _projeBilgileri.TemelDerinligi;
            set
            {
                _projeBilgileri.TemelDerinligi = value;
                OnPropertyChanged();
                HasUnsavedChanges = true;
            }
        }

        /// <summary>
        /// Kat sayısı
        /// </summary>
        public int KatSayisi
        {
            get => _projeBilgileri.KatSayisi;
            set
            {
                _projeBilgileri.KatSayisi = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(ToplamKatSayisi));
                OnPropertyChanged(nameof(ToplamYapiYuksekligi));
                HasUnsavedChanges = true;
            }
        }

        /// <summary>
        /// Kat yüksekliği
        /// </summary>
        public double KatYuksekligi
        {
            get => _projeBilgileri.KatYuksekligi;
            set
            {
                _projeBilgileri.KatYuksekligi = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(ToplamYapiYuksekligi));
                HasUnsavedChanges = true;
            }
        }

        /// <summary>
        /// Bodrum var mı
        /// </summary>
        public bool BodrumVarMi
        {
            get => _projeBilgileri.BodrumVarMi;
            set
            {
                _projeBilgileri.BodrumVarMi = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(ToplamKatSayisi));
                OnPropertyChanged(nameof(ToplamYapiYuksekligi));
                
                // Bodrum yoksa bodrum kat sayısını sıfırla
                if (!value)
                {
                    BodrumKatSayisi = 0;
                }
                
                HasUnsavedChanges = true;
            }
        }

        /// <summary>
        /// Bodrum kat sayısı
        /// </summary>
        public int BodrumKatSayisi
        {
            get => _projeBilgileri.BodrumKatSayisi;
            set
            {
                _projeBilgileri.BodrumKatSayisi = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(ToplamKatSayisi));
                OnPropertyChanged(nameof(ToplamYapiYuksekligi));
                HasUnsavedChanges = true;
            }
        }

        /// <summary>
        /// Döşeme kalınlığı
        /// </summary>
        public int DosemeKalinligi
        {
            get => _projeBilgileri.DosemeKalinligi;
            set
            {
                _projeBilgileri.DosemeKalinligi = value;
                OnPropertyChanged();
                HasUnsavedChanges = true;
            }
        }

        /// <summary>
        /// Uzunluk birimi
        /// </summary>
        public string UzunlukBirimi
        {
            get => _projeBilgileri.UzunlukBirimi;
            set
            {
                _projeBilgileri.UzunlukBirimi = value;
                OnPropertyChanged();
                HasUnsavedChanges = true;
            }
        }

        /// <summary>
        /// Alan birimi
        /// </summary>
        public string AlanBirimi
        {
            get => _projeBilgileri.AlanBirimi;
            set
            {
                _projeBilgileri.AlanBirimi = value;
                OnPropertyChanged();
                HasUnsavedChanges = true;
            }
        }

        /// <summary>
        /// Proje açıklaması
        /// </summary>
        public string ProjeAciklamasi
        {
            get => _projeBilgileri.ProjeAciklamasi;
            set
            {
                _projeBilgileri.ProjeAciklamasi = value;
                OnPropertyChanged();
                HasUnsavedChanges = true;
            }
        }

        /// <summary>
        /// Proje lokasyonu
        /// </summary>
        public string ProjeLokasyonu
        {
            get => _projeBilgileri.ProjeLokasyonu;
            set
            {
                _projeBilgileri.ProjeLokasyonu = value;
                OnPropertyChanged();
                HasUnsavedChanges = true;
            }
        }

        /// <summary>
        /// Müteahhit firma
        /// </summary>
        public string MuteahhitFirma
        {
            get => _projeBilgileri.MuteahhitFirma;
            set
            {
                _projeBilgileri.MuteahhitFirma = value;
                OnPropertyChanged();
                HasUnsavedChanges = true;
            }
        }

        /// <summary>
        /// Proje mühendisi
        /// </summary>
        public string ProjeMuhendisi
        {
            get => _projeBilgileri.ProjeMuhendisi;
            set
            {
                _projeBilgileri.ProjeMuhendisi = value;
                OnPropertyChanged();
                HasUnsavedChanges = true;
            }
        }

        /// <summary>
        /// Toplam kat sayısı (hesaplanan)
        /// </summary>
        public int ToplamKatSayisi => _projeBilgileri.ToplamKatSayisi;

        /// <summary>
        /// Toplam yapı yüksekliği (hesaplanan)
        /// </summary>
        public double ToplamYapiYuksekligi => _projeBilgileri.ToplamYapiYuksekligi;

        /// <summary>
        /// Yükleme durumu
        /// </summary>
        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                _isLoading = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Durum mesajı
        /// </summary>
        public string StatusMessage
        {
            get => _statusMessage;
            set
            {
                _statusMessage = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Kaydedilmemiş değişiklikler var mı
        /// </summary>
        public bool HasUnsavedChanges
        {
            get => _hasUnsavedChanges;
            set
            {
                _hasUnsavedChanges = value;
                OnPropertyChanged();
                ((RelayCommand)SaveCommand).RaiseCanExecuteChanged();
            }
        }

        /// <summary>
        /// Temel türleri koleksiyonu
        /// </summary>
        public ObservableCollection<string> TemelTurleri { get; private set; } = new();

        /// <summary>
        /// Uzunluk birimleri koleksiyonu
        /// </summary>
        public ObservableCollection<string> UzunlukBirimleri { get; private set; } = new();

        /// <summary>
        /// Alan birimleri koleksiyonu
        /// </summary>
        public ObservableCollection<string> AlanBirimleri { get; private set; } = new();

        #endregion

        #region Commands

        public ICommand SaveCommand { get; private set; } = null!;
        public ICommand LoadCommand { get; private set; } = null!;
        public ICommand SaveAsCommand { get; private set; } = null!;
        public ICommand NewProjectCommand { get; private set; } = null!;
        public ICommand ValidateCommand { get; private set; } = null!;

        #endregion

        #region Private Methods

        /// <summary>
        /// Koleksiyonları initialize eder
        /// </summary>
        private void InitializeCollections()
        {
            // Temel türleri
            TemelTurleri.Clear();
            TemelTurleri.Add("Radye");
            TemelTurleri.Add("Tekil");
            TemelTurleri.Add("Sürekli");
            TemelTurleri.Add("Kazıklı");
            TemelTurleri.Add("Betonarme Plak");

            // Uzunluk birimleri
            UzunlukBirimleri.Clear();
            UzunlukBirimleri.Add("m");
            UzunlukBirimleri.Add("cm");
            UzunlukBirimleri.Add("mm");

            // Alan birimleri
            AlanBirimleri.Clear();
            AlanBirimleri.Add("m²");
            AlanBirimleri.Add("cm²");
            AlanBirimleri.Add("mm²");
        }

        /// <summary>
        /// Command'ları initialize eder
        /// </summary>
        private void InitializeCommands()
        {
            SaveCommand = new RelayCommand(async () => await SaveAsync(), CanSave);
            LoadCommand = new RelayCommand(async () => await LoadAsync());
            SaveAsCommand = new RelayCommand(async () => await SaveAsAsync());
            NewProjectCommand = new RelayCommand(NewProject, CanCreateNewProject);
            ValidateCommand = new RelayCommand(ValidateProject);
        }

        /// <summary>
        /// Proje bilgilerini asenkron olarak yükler
        /// </summary>
        private async Task LoadProjeBilgileriAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "Proje bilgileri yükleniyor...";

                var loadedProject = await _projeBilgileriService.LoadProjeBilgileriAsync();
                if (loadedProject != null)
                {
                    ProjeBilgileri = loadedProject;
                    HasUnsavedChanges = false;
                    StatusMessage = "Proje bilgileri yüklendi.";
                }
                else
                {
                    StatusMessage = "Varsayılan proje bilgileri kullanılıyor.";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Yükleme hatası: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        #endregion

        #region Command Methods

        /// <summary>
        /// Proje bilgilerini kaydet
        /// </summary>
        private async Task SaveAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "Proje bilgileri kaydediliyor...";

                var success = await _projeBilgileriService.SaveProjeBilgileriAsync(ProjeBilgileri);
                if (success)
                {
                    HasUnsavedChanges = false;
                    StatusMessage = "Proje bilgileri başarıyla kaydedildi.";
                }
                else
                {
                    StatusMessage = "Kaydetme işlemi başarısız oldu.";
                    MessageBox.Show("Proje bilgileri kaydedilemedi. Lütfen tekrar deneyin.",
                        "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Kaydetme hatası: {ex.Message}";
                MessageBox.Show($"Kaydetme sırasında hata oluştu:\n{ex.Message}",
                    "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// Proje bilgilerini yükle
        /// </summary>
        private async Task LoadAsync()
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "Proje Bilgileri Dosyası Seçin",
                Filter = "JSON Dosyaları (*.json)|*.json|Tüm Dosyalar (*.*)|*.*",
                FilterIndex = 1
            };

            if (openFileDialog.ShowDialog() == true)
            {
                try
                {
                    IsLoading = true;
                    StatusMessage = "Proje bilgileri yükleniyor...";

                    var loadedProject = await _projeBilgileriService.LoadProjeBilgileriAsync(openFileDialog.FileName);
                    if (loadedProject != null)
                    {
                        ProjeBilgileri = loadedProject;
                        HasUnsavedChanges = false;
                        StatusMessage = $"Proje bilgileri yüklendi: {Path.GetFileName(openFileDialog.FileName)}";
                    }
                    else
                    {
                        StatusMessage = "Dosya yüklenemedi.";
                        MessageBox.Show("Seçilen dosya yüklenemedi. Dosya formatı geçersiz olabilir.",
                            "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                catch (Exception ex)
                {
                    StatusMessage = $"Yükleme hatası: {ex.Message}";
                    MessageBox.Show($"Dosya yüklenirken hata oluştu:\n{ex.Message}",
                        "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
                }
                finally
                {
                    IsLoading = false;
                }
            }
        }

        /// <summary>
        /// Farklı kaydet
        /// </summary>
        private async Task SaveAsAsync()
        {
            var saveFileDialog = new SaveFileDialog
            {
                Title = "Proje Bilgilerini Kaydet",
                Filter = "JSON Dosyaları (*.json)|*.json|Tüm Dosyalar (*.*)|*.*",
                FilterIndex = 1,
                FileName = $"{ProjeAdi}_proje_bilgileri.json"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                try
                {
                    IsLoading = true;
                    StatusMessage = "Proje bilgileri kaydediliyor...";

                    var success = await _projeBilgileriService.SaveProjeBilgileriAsync(ProjeBilgileri, saveFileDialog.FileName);
                    if (success)
                    {
                        HasUnsavedChanges = false;
                        StatusMessage = $"Proje bilgileri kaydedildi: {Path.GetFileName(saveFileDialog.FileName)}";
                    }
                    else
                    {
                        StatusMessage = "Kaydetme işlemi başarısız oldu.";
                        MessageBox.Show("Proje bilgileri kaydedilemedi. Lütfen tekrar deneyin.",
                            "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                catch (Exception ex)
                {
                    StatusMessage = $"Kaydetme hatası: {ex.Message}";
                    MessageBox.Show($"Kaydetme sırasında hata oluştu:\n{ex.Message}",
                        "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
                }
                finally
                {
                    IsLoading = false;
                }
            }
        }

        /// <summary>
        /// Yeni proje oluştur
        /// </summary>
        private void NewProject()
        {
            if (HasUnsavedChanges)
            {
                var result = MessageBox.Show("Kaydedilmemiş değişiklikler var. Yeni proje oluşturmak istediğinizden emin misiniz?",
                    "Onay", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result != MessageBoxResult.Yes)
                    return;
            }

            ProjeBilgileri = _projeBilgileriService.CreateDefaultProjeBilgileri();
            HasUnsavedChanges = false;
            StatusMessage = "Yeni proje oluşturuldu.";
        }

        /// <summary>
        /// Proje bilgilerini doğrula
        /// </summary>
        private void ValidateProject()
        {
            var (isValid, errorMessages) = _projeBilgileriService.ValidateProjeBilgileri(ProjeBilgileri);

            if (isValid)
            {
                StatusMessage = "Proje bilgileri geçerli.";
                MessageBox.Show("Proje bilgileri geçerli ve eksiksiz.",
                    "Doğrulama Başarılı", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            else
            {
                StatusMessage = "Proje bilgilerinde hatalar var.";
                var errorMessage = "Aşağıdaki hatalar bulundu:\n\n" + string.Join("\n", errorMessages);
                MessageBox.Show(errorMessage,
                    "Doğrulama Hataları", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        #endregion

        #region Command Can Execute Methods

        private bool CanSave() => HasUnsavedChanges && !IsLoading;
        private bool CanCreateNewProject() => !IsLoading;

        #endregion

        #region INotifyPropertyChanged Implementation

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }
}

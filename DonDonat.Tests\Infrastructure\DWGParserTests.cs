using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using DonDonat.Tests.Helpers;
using DonDonat.WPF.Infrastructure.Parsers;

namespace DonDonat.Tests.Infrastructure
{
    /// <summary>
    /// DWGParser için integration testler
    /// Gerçek dosyalarla DXF okuma sürecini test eder
    /// </summary>
    public class DWGParserTests : TestBase
    {
        private readonly DWGParser _dwgParser;

        public DWGParserTests()
        {
            _dwgParser = new DWGParser();
        }

        [Fact]
        public async Task ParseDxfFileAsync_ShouldReturnEmptyList_WhenFileNotExists()
        {
            // Arrange
            var nonExistentFile = GetTempFilePath("non_existent.dxf");

            // Act
            var result = await _dwgParser.ParseDxfFileAsync(nonExistentFile);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }

        [Fact]
        public async Task ParseDxfFileAsync_ShouldReturnEmptyList_WhenInvalidDxfFile()
        {
            // Arrange
            var invalidDxfContent = TestDataBuilder.CreateInvalidDxfContent();
            var invalidDxfFile = CreateTempFile("invalid.dxf", invalidDxfContent);

            // Act
            var result = await _dwgParser.ParseDxfFileAsync(invalidDxfFile);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }

        [Fact]
        public async Task ParseDxfFileAsync_ShouldParseValidDxfFile()
        {
            // Arrange
            var validDxfContent = TestDataBuilder.CreateTestDxfContent();
            var validDxfFile = CreateTempFile("valid.dxf", validDxfContent);

            // Act
            var result = await _dwgParser.ParseDxfFileAsync(validDxfFile);

            // Assert
            result.Should().NotBeNull();
            // DXF parser implementasyonuna bağlı olarak sonuç değişebilir
            // Bu test gerçek parser implementasyonu ile güncellenmelidir
        }

        [Fact]
        public async Task ParseDxfFileAsync_ShouldHandleEmptyFile()
        {
            // Arrange
            var emptyDxfFile = CreateTempFile("empty.dxf", "");

            // Act
            var result = await _dwgParser.ParseDxfFileAsync(emptyDxfFile);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }

        [Fact]
        public async Task ParseDxfFileAsync_ShouldHandleLargeFile()
        {
            // Arrange
            var largeDxfContent = GenerateLargeDxfContent(1000); // 1000 entity
            var largeDxfFile = CreateTempFile("large.dxf", largeDxfContent);

            // Act
            var result = await _dwgParser.ParseDxfFileAsync(largeDxfFile);

            // Assert
            result.Should().NotBeNull();
            // Performance test - işlem makul sürede tamamlanmalı
        }

        [Theory]
        [InlineData("test.dxf")]
        [InlineData("test.DXF")]
        [InlineData("test.dwg")]
        [InlineData("test.DWG")]
        public async Task ParseDxfFileAsync_ShouldHandleDifferentExtensions(string fileName)
        {
            // Arrange
            var dxfContent = TestDataBuilder.CreateTestDxfContent();
            var dxfFile = CreateTempFile(fileName, dxfContent);

            // Act
            var result = await _dwgParser.ParseDxfFileAsync(dxfFile);

            // Assert
            result.Should().NotBeNull();
        }

        [Fact]
        public async Task ParseDxfFileAsync_ShouldHandleSpecialCharactersInPath()
        {
            // Arrange
            var specialPath = Path.Combine(TempPath, "özel karakter içeren dosya.dxf");
            var dxfContent = TestDataBuilder.CreateTestDxfContent();
            
            Directory.CreateDirectory(Path.GetDirectoryName(specialPath)!);
            await File.WriteAllTextAsync(specialPath, dxfContent);

            // Act
            var result = await _dwgParser.ParseDxfFileAsync(specialPath);

            // Assert
            result.Should().NotBeNull();
        }

        [Fact]
        public async Task ParseDxfFileAsync_ShouldBeThreadSafe()
        {
            // Arrange
            var dxfContent = TestDataBuilder.CreateTestDxfContent();
            var dxfFile = CreateTempFile("threadsafe.dxf", dxfContent);

            // Act - Paralel olarak aynı dosyayı parse et
            var tasks = Enumerable.Range(0, 5)
                .Select(_ => _dwgParser.ParseDxfFileAsync(dxfFile))
                .ToArray();

            var results = await Task.WhenAll(tasks);

            // Assert
            results.Should().AllSatisfy(result => result.Should().NotBeNull());
        }

        [Fact]
        public void IsValidDxfFile_ShouldReturnTrue_ForValidDxfFile()
        {
            // Arrange
            var validDxfContent = TestDataBuilder.CreateTestDxfContent();
            var validDxfFile = CreateTempFile("valid_check.dxf", validDxfContent);

            // Act
            var isValid = _dwgParser.IsValidDxfFile(validDxfFile);

            // Assert
            isValid.Should().BeTrue();
        }

        [Fact]
        public void IsValidDxfFile_ShouldReturnFalse_ForInvalidDxfFile()
        {
            // Arrange
            var invalidDxfContent = TestDataBuilder.CreateInvalidDxfContent();
            var invalidDxfFile = CreateTempFile("invalid_check.dxf", invalidDxfContent);

            // Act
            var isValid = _dwgParser.IsValidDxfFile(invalidDxfFile);

            // Assert
            isValid.Should().BeFalse();
        }

        [Fact]
        public void IsValidDxfFile_ShouldReturnFalse_WhenFileNotExists()
        {
            // Arrange
            var nonExistentFile = GetTempFilePath("non_existent_check.dxf");

            // Act
            var isValid = _dwgParser.IsValidDxfFile(nonExistentFile);

            // Assert
            isValid.Should().BeFalse();
        }

        [Fact]
        public async Task GetDxfInfoAsync_ShouldReturnFileInfo()
        {
            // Arrange
            var dxfContent = TestDataBuilder.CreateTestDxfContent();
            var dxfFile = CreateTempFile("info.dxf", dxfContent);

            // Act
            var info = await _dwgParser.GetDxfInfoAsync(dxfFile);

            // Assert
            info.Should().NotBeNull();
            info.FileName.Should().Be("info.dxf");
            info.FileSize.Should().BeGreaterThan(0);
            info.CreationDate.Should().BeCloseTo(DateTime.Now, TimeSpan.FromMinutes(1));
        }

        [Fact]
        public async Task ParseDxfFileAsync_ShouldHandleCorruptedFile()
        {
            // Arrange
            var corruptedContent = "0\nSECTION\n2\nHEADER\n"; // Eksik ENDSEC
            var corruptedFile = CreateTempFile("corrupted.dxf", corruptedContent);

            // Act
            var result = await _dwgParser.ParseDxfFileAsync(corruptedFile);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty(); // Corrupted file'dan donatı parse edilemez
        }

        [Fact]
        public async Task ParseDxfFileAsync_ShouldExtractDonatiFromComplexDxf()
        {
            // Arrange
            var complexDxfContent = CreateComplexDxfWithDonati();
            var complexDxfFile = CreateTempFile("complex.dxf", complexDxfContent);

            // Act
            var result = await _dwgParser.ParseDxfFileAsync(complexDxfFile);

            // Assert
            result.Should().NotBeNull();
            // Gerçek implementasyona göre donatı sayısı kontrol edilebilir
        }

        [Fact]
        public async Task ParseDxfFileAsync_ShouldHandleUnicodeCharacters()
        {
            // Arrange
            var unicodeDxfContent = CreateDxfWithUnicodeText();
            var unicodeDxfFile = CreateTempFile("unicode.dxf", unicodeDxfContent);

            // Act
            var result = await _dwgParser.ParseDxfFileAsync(unicodeDxfFile);

            // Assert
            result.Should().NotBeNull();
            // Unicode karakterler doğru parse edilmeli
        }

        #region Helper Methods

        /// <summary>
        /// Büyük DXF dosyası içeriği oluşturur
        /// </summary>
        /// <param name="entityCount">Entity sayısı</param>
        /// <returns>DXF içeriği</returns>
        private string GenerateLargeDxfContent(int entityCount)
        {
            var content = @"0
SECTION
2
HEADER
9
$ACADVER
1
AC1015
0
ENDSEC
0
SECTION
2
ENTITIES
";

            for (int i = 0; i < entityCount; i++)
            {
                content += $@"0
LINE
8
DONATI_{i}
10
{i * 10.0}
20
0.0
30
0.0
11
{i * 10.0 + 100.0}
21
0.0
31
0.0
";
            }

            content += @"0
ENDSEC
0
EOF";

            return content;
        }

        /// <summary>
        /// Karmaşık donatı içeren DXF oluşturur
        /// </summary>
        /// <returns>Karmaşık DXF içeriği</returns>
        private string CreateComplexDxfWithDonati()
        {
            return @"0
SECTION
2
HEADER
9
$ACADVER
1
AC1015
0
ENDSEC
0
SECTION
2
ENTITIES
0
CIRCLE
8
DONATI_12
10
100.0
20
100.0
30
0.0
40
6.0
0
CIRCLE
8
DONATI_16
10
200.0
20
200.0
30
0.0
40
8.0
0
LINE
8
DONATI_AXIS
10
0.0
20
0.0
30
0.0
11
1000.0
21
0.0
31
0.0
0
ENDSEC
0
EOF";
        }

        /// <summary>
        /// Unicode karakterler içeren DXF oluşturur
        /// </summary>
        /// <returns>Unicode DXF içeriği</returns>
        private string CreateDxfWithUnicodeText()
        {
            return @"0
SECTION
2
HEADER
9
$ACADVER
1
AC1015
0
ENDSEC
0
SECTION
2
ENTITIES
0
TEXT
8
DONATI_TEXT
10
0.0
20
0.0
30
0.0
1
Ø12 Donatı - Türkçe Karakter Test
0
ENDSEC
0
EOF";
        }

        #endregion
    }
}

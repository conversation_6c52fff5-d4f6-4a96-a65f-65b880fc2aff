# DonDonat ClickOnce Dağıtım Scripti
# Bu script, DonDonat uygulamasını ClickOnce ile paketler ve dağıtıma hazırlar

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("Debug", "Release")]
    [string]$Configuration = "Release",
    
    [Parameter(Mandatory=$false)]
    [string]$PublishUrl = "https://your-domain.com/dondonat/",
    
    [Parameter(Mandatory=$false)]
    [string]$OutputPath = "bin\ClickOnce",
    
    [Parameter(Mandatory=$false)]
    [string]$Version = "*******",
    
    [Parameter(Mandatory=$false)]
    [switch]$IncrementVersion,
    
    [Parameter(Mandatory=$false)]
    [switch]$OpenOutput
)

# Renkli çıktı fonksiyonları
function Write-Success { param($Message) Write-Host $Message -ForegroundColor Green }
function Write-Info { param($Message) Write-Host $Message -ForegroundColor Cyan }
function Write-Warning { param($Message) Write-Host $Message -ForegroundColor Yellow }
function Write-Error { param($Message) Write-Host $Message -ForegroundColor Red }

Write-Info "DonDonat ClickOnce Dağıtım Scripti"
Write-Info "=================================="
Write-Info "Konfigürasyon: $Configuration"
Write-Info "Publish URL: $PublishUrl"
Write-Info "Çıktı Yolu: $OutputPath"
Write-Info "Versiyon: $Version"

# Proje yolları
$ProjectPath = "..\DonDonat.WPF.csproj"
$PublishProfilePath = "Properties\PublishProfiles\ClickOnce.pubxml"

# MSBuild yolunu bul
$MSBuildPath = ""
$VSWhere = "${env:ProgramFiles(x86)}\Microsoft Visual Studio\Installer\vswhere.exe"

if (Test-Path $VSWhere) {
    $VSPath = & $VSWhere -latest -products * -requires Microsoft.Component.MSBuild -property installationPath
    if ($VSPath) {
        $MSBuildPath = Join-Path $VSPath "MSBuild\Current\Bin\MSBuild.exe"
        if (-not (Test-Path $MSBuildPath)) {
            $MSBuildPath = Join-Path $VSPath "MSBuild\15.0\Bin\MSBuild.exe"
        }
    }
}

if (-not (Test-Path $MSBuildPath)) {
    Write-Error "MSBuild bulunamadı. Visual Studio 2019 veya 2022 yüklü olduğundan emin olun."
    exit 1
}

Write-Info "MSBuild yolu: $MSBuildPath"

try {
    # Çıktı klasörünü temizle
    if (Test-Path $OutputPath) {
        Write-Info "Önceki çıktılar temizleniyor..."
        Remove-Item -Path $OutputPath -Recurse -Force
    }

    # Versiyon artırma
    if ($IncrementVersion) {
        $versionParts = $Version.Split('.')
        $versionParts[3] = [int]$versionParts[3] + 1
        $Version = $versionParts -join '.'
        Write-Info "Versiyon artırıldı: $Version"
    }

    # ClickOnce publish
    Write-Info "ClickOnce paketi oluşturuluyor..."
    
    $PublishArgs = @(
        $ProjectPath
        "/p:Configuration=$Configuration"
        "/p:PublishUrl=$PublishUrl"
        "/p:PublishDir=$OutputPath\"
        "/p:ApplicationVersion=$Version"
        "/p:PublishProfile=$PublishProfilePath"
        "/target:Publish"
        "/verbosity:minimal"
    )
    
    & $MSBuildPath @PublishArgs
    
    if ($LASTEXITCODE -ne 0) {
        throw "ClickOnce paketi oluşturulamadı"
    }
    
    Write-Success "✓ ClickOnce paketi başarıyla oluşturuldu"

    # Setup.exe ve manifest dosyalarını kontrol et
    $SetupExePath = Join-Path $OutputPath "setup.exe"
    $ManifestPath = Join-Path $OutputPath "DonDonat.WPF.application"
    
    if (Test-Path $SetupExePath) {
        Write-Success "✓ Setup.exe oluşturuldu"
    } else {
        Write-Warning "⚠ Setup.exe bulunamadı"
    }
    
    if (Test-Path $ManifestPath) {
        Write-Success "✓ Application manifest oluşturuldu"
    } else {
        Write-Warning "⚠ Application manifest bulunamadı"
    }

    # Web sayfası oluştur
    Write-Info "Kurulum web sayfası oluşturuluyor..."
    
    $WebPageContent = @"
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DonDonat - Kurulum</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .logo { font-size: 2.5em; color: #2C3E50; margin-bottom: 10px; }
        .subtitle { color: #7F8C8D; font-size: 1.2em; }
        .install-section { text-align: center; margin: 30px 0; }
        .install-btn { display: inline-block; background: #3498DB; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-size: 1.1em; margin: 10px; }
        .install-btn:hover { background: #2980B9; }
        .requirements { background: #ECF0F1; padding: 20px; border-radius: 5px; margin: 20px 0; }
        .features { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 30px 0; }
        .feature { background: #F8F9FA; padding: 20px; border-radius: 5px; border-left: 4px solid #3498DB; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🏗️ DonDonat</div>
            <div class="subtitle">Betonarme Donatı Metraj Hesaplama Uygulaması</div>
        </div>
        
        <div class="install-section">
            <h2>Uygulamayı Yükleyin</h2>
            <a href="setup.exe" class="install-btn">💾 Şimdi Yükle</a>
            <a href="DonDonat.WPF.application" class="install-btn">🔄 ClickOnce Kurulum</a>
        </div>
        
        <div class="requirements">
            <h3>📋 Sistem Gereksinimleri</h3>
            <ul>
                <li>Windows 10 veya üzeri</li>
                <li>.NET 9.0 Desktop Runtime</li>
                <li>Minimum 4 GB RAM</li>
                <li>500 MB boş disk alanı</li>
            </ul>
        </div>
        
        <div class="features">
            <div class="feature">
                <h4>📐 DWG/DXF Desteği</h4>
                <p>AutoCAD dosyalarından donatı verilerini otomatik okuma</p>
            </div>
            <div class="feature">
                <h4>📊 Metraj Hesaplama</h4>
                <p>Otomatik ağırlık ve uzunluk hesaplamaları</p>
            </div>
            <div class="feature">
                <h4>📄 Rapor Oluşturma</h4>
                <p>Excel ve PDF formatında detaylı raporlar</p>
            </div>
            <div class="feature">
                <h4>🔄 Otomatik Güncelleme</h4>
                <p>Yeni sürümler otomatik olarak indirilir</p>
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 30px; color: #7F8C8D;">
            <p>Versiyon: $Version | Son Güncelleme: $(Get-Date -Format 'dd.MM.yyyy')</p>
        </div>
    </div>
</body>
</html>
"@
    
    $WebPagePath = Join-Path $OutputPath "index.html"
    $WebPageContent | Out-File -FilePath $WebPagePath -Encoding UTF8
    Write-Success "✓ Kurulum web sayfası oluşturuldu"

    # Sonuçları göster
    Write-Success "`n🎉 ClickOnce dağıtımı tamamlandı!"
    Write-Info "Çıktı klasörü: $OutputPath"
    
    # Oluşturulan dosyaları listele
    $OutputFiles = Get-ChildItem -Path $OutputPath -Recurse
    if ($OutputFiles) {
        Write-Info "`nOluşturulan dosyalar:"
        foreach ($file in $OutputFiles | Where-Object { -not $_.PSIsContainer }) {
            $size = [math]::Round($file.Length / 1KB, 2)
            Write-Host "  📄 $($file.Name) - $size KB" -ForegroundColor White
        }
    }
    
    # Kurulum talimatları
    Write-Info "`n📋 Dağıtım Talimatları:"
    Write-Host "1. $OutputPath klasörünü web sunucunuza yükleyin" -ForegroundColor White
    Write-Host "2. index.html sayfasını kullanıcılarla paylaşın" -ForegroundColor White
    Write-Host "3. Kullanıcılar 'Şimdi Yükle' butonuna tıklayarak kurulum yapabilir" -ForegroundColor White
    
    Write-Info "`n🔄 Güncelleme Süreci:"
    Write-Host "1. Yeni versiyon ile bu scripti tekrar çalıştırın" -ForegroundColor White
    Write-Host "2. Çıktıları web sunucusuna yükleyin" -ForegroundColor White
    Write-Host "3. Kullanıcılar uygulama açılışında otomatik güncelleme alacak" -ForegroundColor White
    
    # Çıktı klasörünü aç
    if ($OpenOutput) {
        Start-Process -FilePath "explorer.exe" -ArgumentList $OutputPath
    }

} catch {
    Write-Error "❌ Hata: $($_.Exception.Message)"
    Write-Error "Detaylar: $($_.Exception.StackTrace)"
    exit 1
}

Write-Success "`n✅ İşlem tamamlandı!"
Write-Info "Kurulum URL'si: $PublishUrl"

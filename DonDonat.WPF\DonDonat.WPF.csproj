﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net9.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>

    <!-- Uygulama Bilgileri -->
    <AssemblyTitle>DonDonat - Betonarme Donatı Metraj Hesaplama</AssemblyTitle>
    <AssemblyDescription>AutoCAD DWG dosyalarından betonarme donatı metrajı hesaplayan profesyonel masaüstü uygulaması</AssemblyDescription>
    <AssemblyCompany>YourCompany</AssemblyCompany>
    <AssemblyProduct>DonDonat</AssemblyProduct>
    <AssemblyCopyright>Copyright © YourCompany 2024</AssemblyCopyright>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>

    <!-- Uygulama İkonu -->
    <ApplicationIcon>Resources\DonDonat.ico</ApplicationIcon>
    <Win32Resource />

    <!-- İkon kaynakları -->
    <ApplicationManifest>app.manifest</ApplicationManifest>

    <!-- ClickOnce Ayarları -->
    <IsPublishable>true</IsPublishable>
    <PublishUrl>https://your-domain.com/dondonat/</PublishUrl>
    <InstallUrl>https://your-domain.com/dondonat/</InstallUrl>
    <UpdateEnabled>true</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <CreateWebPageOnPublish>true</CreateWebPageOnPublish>
    <WebPage>index.html</WebPage>
    <ApplicationRevision>1</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="EPPlus" Version="8.0.5" />
    <PackageReference Include="iTextSharp.LGPLv2.Core" Version="3.7.4" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="9.0.5" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.5">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>

    <!-- Hosting ve DI -->
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="9.0.5" />

    <!-- File Logging -->
    <PackageReference Include="Serilog.Extensions.Hosting" Version="8.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="6.0.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="appsettings.Development.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Logs\" />
    <Folder Include="Data\" />
    <Folder Include="Temp\" />
    <Folder Include="Exports\" />
  </ItemGroup>

  <ItemGroup>
    <Resource Include="Resources\DonDonat.ico">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="Resources\DonDonat.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
  </ItemGroup>

</Project>

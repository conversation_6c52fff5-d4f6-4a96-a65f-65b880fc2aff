using System;

namespace DonDonat.WPF.Domain.Commands
{
    /// <summary>
    /// ICommandAction için temel implementasyon
    /// Ortak özellikleri ve davranışları sağlar
    /// </summary>
    public abstract class BaseCommandAction : ICommandAction
    {
        #region Properties

        /// <summary>
        /// Command'ın benzersiz kimliği
        /// </summary>
        public Guid Id { get; }

        /// <summary>
        /// Command'ın açıklaması
        /// </summary>
        public string Description { get; protected set; }

        /// <summary>
        /// Command'ın oluşturulma zamanı
        /// </summary>
        public DateTime Timestamp { get; }

        /// <summary>
        /// Command'ın çalıştırılıp çalıştırılmadığı
        /// </summary>
        protected bool IsExecuted { get; set; }

        /// <summary>
        /// Command'ın geri alınıp alınmadığı
        /// </summary>
        protected bool IsUndone { get; set; }

        #endregion

        #region Constructor

        /// <summary>
        /// BaseCommandAction constructor
        /// </summary>
        /// <param name="description">Command açıklaması</param>
        protected BaseCommandAction(string description)
        {
            Id = Guid.NewGuid();
            Description = description ?? throw new ArgumentNullException(nameof(description));
            Timestamp = DateTime.Now;
            IsExecuted = false;
            IsUndone = false;
        }

        #endregion

        #region Abstract Methods

        /// <summary>
        /// Command'ın gerçek execute implementasyonu
        /// Alt sınıflar tarafından implement edilmelidir
        /// </summary>
        /// <returns>İşlemin başarılı olup olmadığı</returns>
        protected abstract bool ExecuteCore();

        /// <summary>
        /// Command'ın gerçek undo implementasyonu
        /// Alt sınıflar tarafından implement edilmelidir
        /// </summary>
        /// <returns>İşlemin başarılı olup olmadığı</returns>
        protected abstract bool UndoCore();

        #endregion

        #region Virtual Methods

        /// <summary>
        /// Command'ın çalıştırılabilir olup olmadığını kontrol eder
        /// Alt sınıflar override edebilir
        /// </summary>
        /// <returns>Çalıştırılabilir mi</returns>
        public virtual bool CanExecute()
        {
            return !IsExecuted && IsValid();
        }

        /// <summary>
        /// Command'ın geri alınabilir olup olmadığını kontrol eder
        /// Alt sınıflar override edebilir
        /// </summary>
        /// <returns>Geri alınabilir mi</returns>
        public virtual bool CanUndo()
        {
            return IsExecuted && !IsUndone && IsValid();
        }

        /// <summary>
        /// Command'ın başka bir command ile birleştirilebilir olup olmadığını kontrol eder
        /// Alt sınıflar override edebilir
        /// </summary>
        /// <param name="other">Diğer command</param>
        /// <returns>Birleştirilebilir mi</returns>
        public virtual bool CanMergeWith(ICommandAction other)
        {
            return false; // Varsayılan olarak birleştirme desteklenmez
        }

        /// <summary>
        /// Command'ı başka bir command ile birleştirir
        /// Alt sınıflar override edebilir
        /// </summary>
        /// <param name="other">Birleştirilecek command</param>
        /// <returns>Birleştirilmiş command</returns>
        public virtual ICommandAction MergeWith(ICommandAction other)
        {
            throw new NotSupportedException("Bu command birleştirme desteklemiyor.");
        }

        /// <summary>
        /// Command'ın geçerli olup olmadığını kontrol eder
        /// Alt sınıflar override edebilir
        /// </summary>
        /// <returns>Geçerli mi</returns>
        public virtual bool IsValid()
        {
            return true; // Varsayılan olarak geçerli
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Command'ı çalıştırır
        /// </summary>
        /// <returns>İşlemin başarılı olup olmadığı</returns>
        public bool Execute()
        {
            try
            {
                if (!CanExecute())
                {
                    System.Diagnostics.Debug.WriteLine($"Command çalıştırılamıyor: {Description}");
                    return false;
                }

                var result = ExecuteCore();
                
                if (result)
                {
                    IsExecuted = true;
                    IsUndone = false;
                    System.Diagnostics.Debug.WriteLine($"Command çalıştırıldı: {Description}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"Command çalıştırılamadı: {Description}");
                }

                return result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Command çalıştırılırken hata: {Description} - {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Command'ı geri alır
        /// </summary>
        /// <returns>İşlemin başarılı olup olmadığı</returns>
        public bool Undo()
        {
            try
            {
                if (!CanUndo())
                {
                    System.Diagnostics.Debug.WriteLine($"Command geri alınamıyor: {Description}");
                    return false;
                }

                var result = UndoCore();
                
                if (result)
                {
                    IsUndone = true;
                    System.Diagnostics.Debug.WriteLine($"Command geri alındı: {Description}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"Command geri alınamadı: {Description}");
                }

                return result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Command geri alınırken hata: {Description} - {ex.Message}");
                return false;
            }
        }

        #endregion

        #region Overrides

        /// <summary>
        /// ToString override
        /// </summary>
        /// <returns>Command açıklaması</returns>
        public override string ToString()
        {
            var status = IsExecuted ? (IsUndone ? "Undone" : "Executed") : "Not Executed";
            return $"{Description} ({status}) - {Timestamp:HH:mm:ss}";
        }

        /// <summary>
        /// Equals override
        /// </summary>
        /// <param name="obj">Karşılaştırılacak nesne</param>
        /// <returns>Eşit mi</returns>
        public override bool Equals(object? obj)
        {
            if (obj is BaseCommandAction other)
            {
                return Id.Equals(other.Id);
            }
            return false;
        }

        /// <summary>
        /// GetHashCode override
        /// </summary>
        /// <returns>Hash code</returns>
        public override int GetHashCode()
        {
            return Id.GetHashCode();
        }

        #endregion
    }
}

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using DonDonat.WPF.Domain.Commands;

namespace DonDonat.WPF.Application.Services
{
    /// <summary>
    /// Undo/Redo işlemlerini yöneten command manager
    /// Command Pattern implementasyonu ile işlem geçmişini yönetir
    /// </summary>
    public class CommandManager : INotifyPropertyChanged
    {
        #region Private Fields

        private readonly Stack<ICommandAction> _undoStack;
        private readonly Stack<ICommandAction> _redoStack;
        private readonly int _maxHistorySize;
        private bool _isExecuting;

        #endregion

        #region Constructor

        /// <summary>
        /// CommandManager constructor
        /// </summary>
        /// <param name="maxHistorySize">Maksimum geçmiş boyutu (varsayılan: 50)</param>
        public CommandManager(int maxHistorySize = 50)
        {
            _maxHistorySize = Math.Max(1, maxHistorySize);
            _undoStack = new Stack<ICommandAction>();
            _redoStack = new Stack<ICommandAction>();
            _isExecuting = false;
        }

        #endregion

        #region Properties

        /// <summary>
        /// Undo yapılabilir mi
        /// </summary>
        public bool CanUndo => _undoStack.Count > 0 && !_isExecuting;

        /// <summary>
        /// Redo yapılabilir mi
        /// </summary>
        public bool CanRedo => _redoStack.Count > 0 && !_isExecuting;

        /// <summary>
        /// Undo stack'indeki command sayısı
        /// </summary>
        public int UndoCount => _undoStack.Count;

        /// <summary>
        /// Redo stack'indeki command sayısı
        /// </summary>
        public int RedoCount => _redoStack.Count;

        /// <summary>
        /// Son undo edilebilir command'ın açıklaması
        /// </summary>
        public string? LastUndoDescription => _undoStack.Count > 0 ? _undoStack.Peek().Description : null;

        /// <summary>
        /// Son redo edilebilir command'ın açıklaması
        /// </summary>
        public string? LastRedoDescription => _redoStack.Count > 0 ? _redoStack.Peek().Description : null;

        /// <summary>
        /// Command çalıştırılıyor mu
        /// </summary>
        public bool IsExecuting => _isExecuting;

        /// <summary>
        /// Maksimum geçmiş boyutu
        /// </summary>
        public int MaxHistorySize => _maxHistorySize;

        #endregion

        #region Events

        /// <summary>
        /// Command çalıştırıldığında tetiklenir
        /// </summary>
        public event EventHandler<CommandExecutedEventArgs>? CommandExecuted;

        /// <summary>
        /// Undo yapıldığında tetiklenir
        /// </summary>
        public event EventHandler<CommandUndoneEventArgs>? CommandUndone;

        /// <summary>
        /// Redo yapıldığında tetiklenir
        /// </summary>
        public event EventHandler<CommandRedoneEventArgs>? CommandRedone;

        /// <summary>
        /// Geçmiş temizlendiğinde tetiklenir
        /// </summary>
        public event EventHandler? HistoryCleared;

        #endregion

        #region Public Methods

        /// <summary>
        /// Command'ı çalıştırır ve geçmişe ekler
        /// </summary>
        /// <param name="command">Çalıştırılacak command</param>
        /// <returns>İşlemin başarılı olup olmadığı</returns>
        public bool ExecuteCommand(ICommandAction command)
        {
            if (command == null)
            {
                throw new ArgumentNullException(nameof(command));
            }

            if (_isExecuting)
            {
                System.Diagnostics.Debug.WriteLine("Başka bir command çalıştırılıyor, bekleyin.");
                return false;
            }

            try
            {
                _isExecuting = true;

                // Command'ı çalıştır
                var result = command.Execute();

                if (result)
                {
                    // Birleştirme kontrolü yap
                    if (_undoStack.Count > 0 && _undoStack.Peek().CanMergeWith(command))
                    {
                        var lastCommand = _undoStack.Pop();
                        var mergedCommand = lastCommand.MergeWith(command);
                        _undoStack.Push(mergedCommand);
                        
                        System.Diagnostics.Debug.WriteLine($"Command birleştirildi: {mergedCommand.Description}");
                    }
                    else
                    {
                        // Undo stack'e ekle
                        _undoStack.Push(command);
                        
                        // Stack boyutunu kontrol et
                        TrimUndoStack();
                    }

                    // Redo stack'i temizle
                    _redoStack.Clear();

                    // Event'i tetikle
                    CommandExecuted?.Invoke(this, new CommandExecutedEventArgs(command));

                    // Property changed event'lerini tetikle
                    OnPropertyChanged(nameof(CanUndo));
                    OnPropertyChanged(nameof(CanRedo));
                    OnPropertyChanged(nameof(UndoCount));
                    OnPropertyChanged(nameof(RedoCount));
                    OnPropertyChanged(nameof(LastUndoDescription));
                    OnPropertyChanged(nameof(LastRedoDescription));

                    System.Diagnostics.Debug.WriteLine($"Command çalıştırıldı: {command.Description}");
                }

                return result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Command çalıştırılırken hata: {ex.Message}");
                return false;
            }
            finally
            {
                _isExecuting = false;
                OnPropertyChanged(nameof(IsExecuting));
            }
        }

        /// <summary>
        /// Son command'ı geri alır (Undo)
        /// </summary>
        /// <returns>İşlemin başarılı olup olmadığı</returns>
        public bool Undo()
        {
            if (!CanUndo)
            {
                return false;
            }

            try
            {
                _isExecuting = true;

                var command = _undoStack.Pop();
                var result = command.Undo();

                if (result)
                {
                    // Redo stack'e ekle
                    _redoStack.Push(command);

                    // Event'i tetikle
                    CommandUndone?.Invoke(this, new CommandUndoneEventArgs(command));

                    System.Diagnostics.Debug.WriteLine($"Command geri alındı: {command.Description}");
                }
                else
                {
                    // Başarısız olursa geri koy
                    _undoStack.Push(command);
                }

                // Property changed event'lerini tetikle
                OnPropertyChanged(nameof(CanUndo));
                OnPropertyChanged(nameof(CanRedo));
                OnPropertyChanged(nameof(UndoCount));
                OnPropertyChanged(nameof(RedoCount));
                OnPropertyChanged(nameof(LastUndoDescription));
                OnPropertyChanged(nameof(LastRedoDescription));

                return result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Undo işleminde hata: {ex.Message}");
                return false;
            }
            finally
            {
                _isExecuting = false;
                OnPropertyChanged(nameof(IsExecuting));
            }
        }

        /// <summary>
        /// Son geri alınan command'ı tekrar çalıştırır (Redo)
        /// </summary>
        /// <returns>İşlemin başarılı olup olmadığı</returns>
        public bool Redo()
        {
            if (!CanRedo)
            {
                return false;
            }

            try
            {
                _isExecuting = true;

                var command = _redoStack.Pop();
                var result = command.Execute();

                if (result)
                {
                    // Undo stack'e ekle
                    _undoStack.Push(command);

                    // Event'i tetikle
                    CommandRedone?.Invoke(this, new CommandRedoneEventArgs(command));

                    System.Diagnostics.Debug.WriteLine($"Command tekrar çalıştırıldı: {command.Description}");
                }
                else
                {
                    // Başarısız olursa geri koy
                    _redoStack.Push(command);
                }

                // Property changed event'lerini tetikle
                OnPropertyChanged(nameof(CanUndo));
                OnPropertyChanged(nameof(CanRedo));
                OnPropertyChanged(nameof(UndoCount));
                OnPropertyChanged(nameof(RedoCount));
                OnPropertyChanged(nameof(LastUndoDescription));
                OnPropertyChanged(nameof(LastRedoDescription));

                return result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Redo işleminde hata: {ex.Message}");
                return false;
            }
            finally
            {
                _isExecuting = false;
                OnPropertyChanged(nameof(IsExecuting));
            }
        }

        /// <summary>
        /// Tüm geçmişi temizler
        /// </summary>
        public void ClearHistory()
        {
            _undoStack.Clear();
            _redoStack.Clear();

            // Event'i tetikle
            HistoryCleared?.Invoke(this, EventArgs.Empty);

            // Property changed event'lerini tetikle
            OnPropertyChanged(nameof(CanUndo));
            OnPropertyChanged(nameof(CanRedo));
            OnPropertyChanged(nameof(UndoCount));
            OnPropertyChanged(nameof(RedoCount));
            OnPropertyChanged(nameof(LastUndoDescription));
            OnPropertyChanged(nameof(LastRedoDescription));

            System.Diagnostics.Debug.WriteLine("Command geçmişi temizlendi.");
        }

        /// <summary>
        /// Undo geçmişini döndürür
        /// </summary>
        /// <returns>Undo command'ları listesi</returns>
        public IEnumerable<ICommandAction> GetUndoHistory()
        {
            return _undoStack.ToArray();
        }

        /// <summary>
        /// Redo geçmişini döndürür
        /// </summary>
        /// <returns>Redo command'ları listesi</returns>
        public IEnumerable<ICommandAction> GetRedoHistory()
        {
            return _redoStack.ToArray();
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Undo stack boyutunu sınırlar
        /// </summary>
        private void TrimUndoStack()
        {
            while (_undoStack.Count > _maxHistorySize)
            {
                // En eski command'ları kaldır
                var commands = _undoStack.ToArray();
                _undoStack.Clear();
                
                for (int i = 1; i < commands.Length; i++)
                {
                    _undoStack.Push(commands[i]);
                }
            }
        }

        #endregion

        #region INotifyPropertyChanged Implementation

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }

    #region Event Args Classes

    /// <summary>
    /// Command çalıştırıldığında event args
    /// </summary>
    public class CommandExecutedEventArgs : EventArgs
    {
        public ICommandAction Command { get; }

        public CommandExecutedEventArgs(ICommandAction command)
        {
            Command = command;
        }
    }

    /// <summary>
    /// Command geri alındığında event args
    /// </summary>
    public class CommandUndoneEventArgs : EventArgs
    {
        public ICommandAction Command { get; }

        public CommandUndoneEventArgs(ICommandAction command)
        {
            Command = command;
        }
    }

    /// <summary>
    /// Command tekrar çalıştırıldığında event args
    /// </summary>
    public class CommandRedoneEventArgs : EventArgs
    {
        public ICommandAction Command { get; }

        public CommandRedoneEventArgs(ICommandAction command)
        {
            Command = command;
        }
    }

    #endregion
}

namespace DonDonat.WPF.Infrastructure.Configuration
{
    /// <summary>
    /// Uygulama ayarları modeli
    /// </summary>
    public class AppSettings
    {
        public string ApplicationName { get; set; } = "DonDonat";
        public string Version { get; set; } = "1.0.0";
        public string Environment { get; set; } = "Production";
        public bool EnableDebugMode { get; set; } = false;
        public bool EnablePerformanceCounters { get; set; } = true;
        public int MaxRecentFiles { get; set; } = 10;
        public int AutoSaveIntervalMinutes { get; set; } = 5;
        public string DefaultLanguage { get; set; } = "tr-TR";
        public string DefaultTheme { get; set; } = "Light";
        public bool EnableDetailedLogging { get; set; } = false;
        public bool ShowDebugInfo { get; set; } = false;
        public bool EnableHotReload { get; set; } = false;
        public bool SkipLicenseCheck { get; set; } = false;
        public bool EnableTestData { get; set; } = false;
    }

    /// <summary>
    /// Veritabanı ayarları modeli
    /// </summary>
    public class DatabaseSettings
    {
        public string Provider { get; set; } = "SQLite";
        public string ConnectionString { get; set; } = string.Empty;
        public int CommandTimeout { get; set; } = 30;
        public bool EnableSensitiveDataLogging { get; set; } = false;
        public bool EnableDetailedErrors { get; set; } = false;
        public string MigrationsAssembly { get; set; } = string.Empty;
        public bool LogQueries { get; set; } = false;
        public bool EnableMigrations { get; set; } = true;
    }

    /// <summary>
    /// Loglama ayarları modeli
    /// </summary>
    public class LoggingSettings
    {
        public string DefaultLevel { get; set; } = "Information";
        public string FilePath { get; set; } = "Logs/DonDonat-{Date}.log";
        public string MinLevel { get; set; } = "Information";
        public string RollingInterval { get; set; } = "Day";
        public int RetainedFileCountLimit { get; set; } = 30;
        public long FileSizeLimitBytes { get; set; } = 10485760;
        public bool IncludeScopes { get; set; } = true;
    }
}

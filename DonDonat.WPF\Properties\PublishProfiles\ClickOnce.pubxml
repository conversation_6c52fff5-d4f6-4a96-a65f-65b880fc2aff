<?xml version="1.0" encoding="utf-8"?>
<!--
https://go.microsoft.com/fwlink/?LinkID=208121.
-->
<Project>
  <PropertyGroup>
    <ApplicationRevision>1</ApplicationRevision>
    <ApplicationVersion>1.0.0.*</ApplicationVersion>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <Configuration>Release</Configuration>
    <CreateWebPageOnPublish>true</CreateWebPageOnPublish>
    <GenerateManifests>true</GenerateManifests>
    <Install>true</Install>
    <InstallFrom>Web</InstallFrom>
    <IsRevisionIncremented>true</IsRevisionIncremented>
    <IsWebBootstrapper>true</IsWebBootstrapper>
    <MapFileExtensions>true</MapFileExtensions>
    <OpenBrowserOnPublish>false</OpenBrowserOnPublish>
    <Platform>Any CPU</Platform>
    <PublishDir>bin\ClickOnce\</PublishDir>
    <PublishUrl>https://your-domain.com/dondonat/</PublishUrl>
    <PublishProtocol>ClickOnce</PublishProtocol>
    <PublishReadyToRun>false</PublishReadyToRun>
    <PublishSingleFile>false</PublishSingleFile>
    <RuntimeIdentifier>win-x64</RuntimeIdentifier>
    <SelfContained>false</SelfContained>
    <SignatureAlgorithm>(none)</SignatureAlgorithm>
    <SignManifests>false</SignManifests>
    <SkipPublisherSecurityPrompt>false</SkipPublisherSecurityPrompt>
    <TargetFramework>net9.0-windows</TargetFramework>
    <UpdateEnabled>true</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <WebPageFileName>DonDonat.htm</WebPageFileName>
    <_TargetId>ClickOnceFolder</_TargetId>
    
    <!-- Uygulama Bilgileri -->
    <ProductName>DonDonat - Betonarme Donatı Metraj Hesaplama</ProductName>
    <PublisherName>YourCompany</PublisherName>
    <ApplicationDescription>AutoCAD DWG dosyalarından betonarme donatı metrajı hesaplayan profesyonel masaüstü uygulaması</ApplicationDescription>
    <SuiteName>DonDonat Suite</SuiteName>
    <SupportUrl>https://your-domain.com/dondonat/support</SupportUrl>
    <ErrorReportUrl>https://your-domain.com/dondonat/error-report</ErrorReportUrl>
    
    <!-- Güvenlik ve İzinler -->
    <TrustUrlParameters>false</TrustUrlParameters>
    <ApplicationManifest>Properties\app.manifest</ApplicationManifest>
    
    <!-- Dosya İlişkilendirmeleri -->
    <FileAssociations>
      <FileAssociation>
        <Extension>.dwg</Extension>
        <Description>AutoCAD Drawing File</Description>
        <ProgId>DonDonat.DWGFile</ProgId>
        <DefaultIcon>DonDonat.ico</DefaultIcon>
      </FileAssociation>
      <FileAssociation>
        <Extension>.dxf</Extension>
        <Description>AutoCAD Drawing Exchange File</Description>
        <ProgId>DonDonat.DXFFile</ProgId>
        <DefaultIcon>DonDonat.ico</DefaultIcon>
      </FileAssociation>
    </FileAssociations>
  </PropertyGroup>
  
  <ItemGroup>
    <BootstrapperPackage Include=".NETFramework,Version=v4.8">
      <Visible>false</Visible>
      <ProductName>.NET Framework 4.8</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>false</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.WindowsDesktop.App">
      <Visible>true</Visible>
      <ProductName>.NET Desktop Runtime</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
  </ItemGroup>
  
  <ItemGroup>
    <Content Include="Assets\**\*">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Reports\**\*">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\**\*">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
</Project>

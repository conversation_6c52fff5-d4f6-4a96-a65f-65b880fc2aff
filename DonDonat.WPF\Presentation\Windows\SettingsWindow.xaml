<Window x:Class="DonDonat.WPF.Presentation.Windows.SettingsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:views="clr-namespace:DonDonat.WPF.Presentation.Views"
        mc:Ignorable="d"
        Title="DonDonat - Ayarlar" 
        Height="700" 
        Width="900"
        MinHeight="600"
        MinWidth="800"
        WindowStartupLocation="CenterOwner"
        ShowInTaskbar="False"
        Icon="/Resources/settings.ico">
    
    <Window.Resources>
        <Style TargetType="Window">
            <Setter Property="Background" Value="#FFFFFF"/>
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="FontSize" Value="12"/>
        </Style>
    </Window.Resources>
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- Başlık -->
        <Border Grid.Row="0" Background="#34495E" Padding="20,15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" 
                           Text="⚙️" 
                           FontSize="24" 
                           VerticalAlignment="Center" 
                           Margin="0,0,10,0"/>
                
                <StackPanel Grid.Column="1" VerticalAlignment="Center">
                    <TextBlock Text="Ayarlar" 
                               FontSize="18" 
                               FontWeight="Bold" 
                               Foreground="White"/>
                    <TextBlock Text="Uygulama tercihlerinizi yönetin" 
                               FontSize="12" 
                               Foreground="#BDC3C7"/>
                </StackPanel>
                
                <Button Grid.Column="2" 
                        Content="✕" 
                        FontSize="16" 
                        Width="30" 
                        Height="30" 
                        Background="Transparent" 
                        Foreground="White" 
                        BorderThickness="0" 
                        Cursor="Hand"
                        Click="CloseButton_Click">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Background" Value="Transparent"/>
                            <Style.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#E74C3C"/>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </Button.Style>
                </Button>
            </Grid>
        </Border>
        
        <!-- Ana içerik -->
        <views:SettingsView Grid.Row="1" x:Name="SettingsViewControl"/>
        
    </Grid>
</Window>

# 🎨 DonDonat İkon Dönüştürme Rehberi

## 📁 Mevcut Durum
- ✅ `Resources/DonDonat.png` dosyası mevcut (1.6 MB)
- ❌ `Resources/DonDonat.ico` dosyası eksik

## 🔄 Dönüştürme Seçenekleri

### Seçenek 1: Online Dönüştürücü (En Kolay)
1. **ConvertIO** sitesine gidin: https://convertio.co/png-ico/
2. `Resources/DonDonat.png` dosyasını yükleyin
3. "Convert" butonuna tıklayın
4. İndirilen dosyayı `DonDonat.ico` olarak yeniden adlandırın
5. `Resources/` klasörüne kopyalayın

### Seçenek 2: IcoFX (Ücretsiz Program)
1. IcoFX programını indirin: https://icofx.ro/
2. PNG dosyasını açın
3. File > Export > ICO formatında kaydedin

### Seçenek 3: GIMP (Ücretsiz)
1. GIMP'i indirin: https://gimp.org/
2. PNG dosyasını açın
3. Image > Scale Image > 32x32 piksel yapın
4. File > Export As > .ico formatında kaydedin

### Seçenek 4: Paint.NET (Windows)
1. Paint.NET'i indirin
2. PNG dosyasını açın
3. Image > Resize > 32x32 piksel
4. File > Save As > ICO formatında kaydedin

### Seçenek 5: ImageMagick (Komut Satırı)
```bash
# ImageMagick yükleyin
winget install ImageMagick.ImageMagick

# Dönüştürme komutu
magick Resources\DonDonat.png -resize 32x32 Resources\DonDonat.ico
```

## 🎯 Önerilen Ayarlar
- **Boyut**: 32x32 piksel (minimum)
- **Format**: ICO
- **Kalite**: Yüksek
- **Şeffaflık**: Korunmalı

## ✅ Test Etme
Dönüştürme sonrası:
```bash
cd DonDonat.WPF
dotnet build
dotnet run
```

## 📋 Kontrol Listesi
- [ ] PNG dosyası mevcut
- [ ] ICO dosyası oluşturuldu
- [ ] Dosya boyutu makul (< 100 KB)
- [ ] Proje derleniyor
- [ ] İkon pencerede görünüyor

## 🚨 Sorun Giderme
- **Dosya çok büyük**: PNG'yi önce küçültün (32x32 veya 64x64)
- **Format hatası**: ICO formatını destekleyen program kullanın
- **Görünmüyor**: Dosya yolunu kontrol edin
- **Bozuk görünüm**: Yüksek kalite ayarları kullanın

## 💡 İpucu
En hızlı çözüm: ConvertIO.co sitesini kullanın!

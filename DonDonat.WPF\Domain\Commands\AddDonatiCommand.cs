using System;
using System.Collections.ObjectModel;
using System.Linq;
using DonDonat.WPF.Domain.Entities;

namespace DonDonat.WPF.Domain.Commands
{
    /// <summary>
    /// Donatı ekleme command'ı
    /// Undo/Redo desteği ile donatı ekleme işlemini yönetir
    /// </summary>
    public class AddDonatiCommand : BaseCommandAction
    {
        #region Private Fields

        private readonly ObservableCollection<Donati> _donatilar;
        private readonly Donati _donati;
        private readonly int _insertIndex;

        #endregion

        #region Constructor

        /// <summary>
        /// AddDonatiCommand constructor
        /// </summary>
        /// <param name="donatilar">Donatı koleksiyonu</param>
        /// <param name="donati">Eklenecek donatı</param>
        /// <param name="insertIndex">Ekleme pozisyonu (-1 ise sona ekle)</param>
        public AddDonatiCommand(ObservableCollection<Donati> donatilar, Donati donati, int insertIndex = -1)
            : base($"Donatı Eklendi: {donati?.Cap}Ø - {donati?.Boy}cm")
        {
            _donatilar = donatilar ?? throw new ArgumentNullException(nameof(donatilar));
            _donati = donati ?? throw new ArgumentNullException(nameof(donati));
            _insertIndex = insertIndex >= 0 ? insertIndex : _donatilar.Count;

            // Açıklamayı güncelle
            Description = $"Donatı Eklendi: {_donati.Cap}Ø - {_donati.Boy}cm - {_donati.Adet} adet";
        }

        #endregion

        #region Properties

        /// <summary>
        /// Eklenen donatı
        /// </summary>
        public Donati Donati => _donati;

        /// <summary>
        /// Ekleme pozisyonu
        /// </summary>
        public int InsertIndex => _insertIndex;

        #endregion

        #region Overrides

        /// <summary>
        /// Donatı ekleme işlemini gerçekleştirir
        /// </summary>
        /// <returns>İşlemin başarılı olup olmadığı</returns>
        protected override bool ExecuteCore()
        {
            try
            {
                // Donatı zaten listede var mı kontrol et
                if (_donatilar.Contains(_donati))
                {
                    System.Diagnostics.Debug.WriteLine("Donatı zaten listede mevcut.");
                    return false;
                }

                // Index geçerli mi kontrol et
                if (_insertIndex < 0 || _insertIndex > _donatilar.Count)
                {
                    System.Diagnostics.Debug.WriteLine($"Geçersiz insert index: {_insertIndex}");
                    return false;
                }

                // Donatıyı belirtilen pozisyona ekle
                if (_insertIndex == _donatilar.Count)
                {
                    _donatilar.Add(_donati);
                }
                else
                {
                    _donatilar.Insert(_insertIndex, _donati);
                }

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Donatı eklenirken hata: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Donatı ekleme işlemini geri alır
        /// </summary>
        /// <returns>İşlemin başarılı olup olmadığı</returns>
        protected override bool UndoCore()
        {
            try
            {
                // Donatı listede var mı kontrol et
                if (!_donatilar.Contains(_donati))
                {
                    System.Diagnostics.Debug.WriteLine("Geri alınacak donatı listede bulunamadı.");
                    return false;
                }

                // Donatıyı listeden kaldır
                _donatilar.Remove(_donati);
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Donatı ekleme geri alınırken hata: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Command'ın geçerli olup olmadığını kontrol eder
        /// </summary>
        /// <returns>Geçerli mi</returns>
        public override bool IsValid()
        {
            return _donatilar != null && _donati != null;
        }

        /// <summary>
        /// Command'ın çalıştırılabilir olup olmadığını kontrol eder
        /// </summary>
        /// <returns>Çalıştırılabilir mi</returns>
        public override bool CanExecute()
        {
            return base.CanExecute() && !_donatilar.Contains(_donati);
        }

        /// <summary>
        /// Command'ın geri alınabilir olup olmadığını kontrol eder
        /// </summary>
        /// <returns>Geri alınabilir mi</returns>
        public override bool CanUndo()
        {
            return base.CanUndo() && _donatilar.Contains(_donati);
        }

        /// <summary>
        /// Aynı türde donatı ekleme command'ları birleştirilebilir
        /// </summary>
        /// <param name="other">Diğer command</param>
        /// <returns>Birleştirilebilir mi</returns>
        public override bool CanMergeWith(ICommandAction other)
        {
            if (other is AddDonatiCommand addCommand)
            {
                // Aynı çap ve boy ise birleştirilebilir
                return _donati.Cap == addCommand._donati.Cap && 
                       _donati.Boy == addCommand._donati.Boy &&
                       _donati.Tur == addCommand._donati.Tur &&
                       _donati.YapiElemani == addCommand._donati.YapiElemani &&
                       _donati.Konum == addCommand._donati.Konum &&
                       Math.Abs((Timestamp - other.Timestamp).TotalSeconds) < 5; // 5 saniye içinde
            }
            return false;
        }

        /// <summary>
        /// Aynı türde donatı ekleme command'larını birleştirir
        /// </summary>
        /// <param name="other">Birleştirilecek command</param>
        /// <returns>Birleştirilmiş command</returns>
        public override ICommandAction MergeWith(ICommandAction other)
        {
            if (!CanMergeWith(other))
            {
                throw new InvalidOperationException("Command'lar birleştirilemez.");
            }

            var addCommand = (AddDonatiCommand)other;
            
            // Yeni bir donatı oluştur (adetleri topla)
            var mergedDonati = new Donati
            {
                Cap = _donati.Cap,
                Boy = _donati.Boy,
                Adet = _donati.Adet + addCommand._donati.Adet,
                Tur = _donati.Tur,
                YapiElemani = _donati.YapiElemani,
                Konum = _donati.Konum,
                DonatiSinifi = _donati.DonatiSinifi
            };

            return new AddDonatiCommand(_donatilar, mergedDonati, _insertIndex);
        }

        #endregion
    }
}

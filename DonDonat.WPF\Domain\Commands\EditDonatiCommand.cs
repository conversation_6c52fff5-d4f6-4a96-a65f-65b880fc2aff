using System;
using System.Collections.ObjectModel;
using DonDonat.WPF.Domain.Entities;

namespace DonDonat.WPF.Domain.Commands
{
    /// <summary>
    /// Donatı düzenleme command'ı
    /// Undo/Redo desteği ile donatı düzenleme işlemini yönetir
    /// </summary>
    public class EditDonatiCommand : BaseCommandAction
    {
        #region Private Fields

        private readonly ObservableCollection<Donati> _donatilar;
        private readonly Donati _targetDonati;
        private readonly Donati _oldValues;
        private readonly Donati _newValues;

        #endregion

        #region Constructor

        /// <summary>
        /// EditDonatiCommand constructor
        /// </summary>
        /// <param name="donatilar">Donatı koleksiyonu</param>
        /// <param name="targetDonati">Düzenlenecek donatı</param>
        /// <param name="newValues"><PERSON><PERSON></param>
        public EditDonatiCommand(ObservableCollection<Donati> donatilar, Donati targetDonati, Donati newValues)
            : base($"Donatı Düzenlendi: {targetDonati?.Cap}Ø")
        {
            _donatilar = donatilar ?? throw new ArgumentNullException(nameof(donatilar));
            _targetDonati = targetDonati ?? throw new ArgumentNullException(nameof(targetDonati));
            _newValues = newValues ?? throw new ArgumentNullException(nameof(newValues));

            // Eski değerleri kaydet
            _oldValues = new Donati
            {
                Cap = _targetDonati.Cap,
                Boy = _targetDonati.Boy,
                Adet = _targetDonati.Adet,
                Tur = _targetDonati.Tur,
                YapiElemani = _targetDonati.YapiElemani,
                Konum = _targetDonati.Konum,
                DonatiSinifi = _targetDonati.DonatiSinifi
            };

            // Açıklamayı güncelle
            Description = $"Donatı Düzenlendi: {_oldValues.Cap}Ø → {_newValues.Cap}Ø";
        }

        #endregion

        #region Properties

        /// <summary>
        /// Düzenlenen donatı
        /// </summary>
        public Donati TargetDonati => _targetDonati;

        /// <summary>
        /// Eski değerler
        /// </summary>
        public Donati OldValues => _oldValues;

        /// <summary>
        /// Yeni değerler
        /// </summary>
        public Donati NewValues => _newValues;

        #endregion

        #region Overrides

        /// <summary>
        /// Donatı düzenleme işlemini gerçekleştirir
        /// </summary>
        /// <returns>İşlemin başarılı olup olmadığı</returns>
        protected override bool ExecuteCore()
        {
            try
            {
                // Donatı listede var mı kontrol et
                if (!_donatilar.Contains(_targetDonati))
                {
                    System.Diagnostics.Debug.WriteLine("Düzenlenecek donatı listede bulunamadı.");
                    return false;
                }

                // Yeni değerleri uygula
                ApplyValues(_targetDonati, _newValues);
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Donatı düzenlenirken hata: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Donatı düzenleme işlemini geri alır
        /// </summary>
        /// <returns>İşlemin başarılı olup olmadığı</returns>
        protected override bool UndoCore()
        {
            try
            {
                // Donatı listede var mı kontrol et
                if (!_donatilar.Contains(_targetDonati))
                {
                    System.Diagnostics.Debug.WriteLine("Geri alınacak donatı listede bulunamadı.");
                    return false;
                }

                // Eski değerleri geri uygula
                ApplyValues(_targetDonati, _oldValues);
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Donatı düzenleme geri alınırken hata: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Command'ın geçerli olup olmadığını kontrol eder
        /// </summary>
        /// <returns>Geçerli mi</returns>
        public override bool IsValid()
        {
            return _donatilar != null && _targetDonati != null && _oldValues != null && _newValues != null;
        }

        /// <summary>
        /// Command'ın çalıştırılabilir olup olmadığını kontrol eder
        /// </summary>
        /// <returns>Çalıştırılabilir mi</returns>
        public override bool CanExecute()
        {
            return base.CanExecute() && _donatilar.Contains(_targetDonati) && !AreValuesEqual(_targetDonati, _newValues);
        }

        /// <summary>
        /// Command'ın geri alınabilir olup olmadığını kontrol eder
        /// </summary>
        /// <returns>Geri alınabilir mi</returns>
        public override bool CanUndo()
        {
            return base.CanUndo() && _donatilar.Contains(_targetDonati);
        }

        /// <summary>
        /// Aynı donatı üzerinde ardışık düzenleme command'ları birleştirilebilir
        /// </summary>
        /// <param name="other">Diğer command</param>
        /// <returns>Birleştirilebilir mi</returns>
        public override bool CanMergeWith(ICommandAction other)
        {
            if (other is EditDonatiCommand editCommand)
            {
                // Aynı donatı üzerinde ve kısa süre içinde yapılan düzenlemeler birleştirilebilir
                return _targetDonati == editCommand._targetDonati &&
                       Math.Abs((Timestamp - other.Timestamp).TotalSeconds) < 3; // 3 saniye içinde
            }
            return false;
        }

        /// <summary>
        /// Aynı donatı üzerinde düzenleme command'larını birleştirir
        /// </summary>
        /// <param name="other">Birleştirilecek command</param>
        /// <returns>Birleştirilmiş command</returns>
        public override ICommandAction MergeWith(ICommandAction other)
        {
            if (!CanMergeWith(other))
            {
                throw new InvalidOperationException("Command'lar birleştirilemez.");
            }

            var editCommand = (EditDonatiCommand)other;
            
            // Yeni bir edit command oluştur (ilk command'ın eski değerleri, son command'ın yeni değerleri)
            return new EditDonatiCommand(_donatilar, _targetDonati, editCommand._newValues)
            {
                // Eski değerleri ilk command'dan al
                _oldValues = 
                {
                    Cap = _oldValues.Cap,
                    Boy = _oldValues.Boy,
                    Adet = _oldValues.Adet,
                    Tur = _oldValues.Tur,
                    YapiElemani = _oldValues.YapiElemani,
                    Konum = _oldValues.Konum,
                    DonatiSinifi = _oldValues.DonatiSinifi
                }
            };
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Bir donatıya değerleri uygular
        /// </summary>
        /// <param name="target">Hedef donatı</param>
        /// <param name="source">Kaynak değerler</param>
        private void ApplyValues(Donati target, Donati source)
        {
            target.Cap = source.Cap;
            target.Boy = source.Boy;
            target.Adet = source.Adet;
            target.Tur = source.Tur;
            target.YapiElemani = source.YapiElemani;
            target.Konum = source.Konum;
            target.DonatiSinifi = source.DonatiSinifi;
        }

        /// <summary>
        /// İki donatının değerlerinin eşit olup olmadığını kontrol eder
        /// </summary>
        /// <param name="donati1">İlk donatı</param>
        /// <param name="donati2">İkinci donatı</param>
        /// <returns>Değerler eşit mi</returns>
        private bool AreValuesEqual(Donati donati1, Donati donati2)
        {
            return donati1.Cap == donati2.Cap &&
                   donati1.Boy == donati2.Boy &&
                   donati1.Adet == donati2.Adet &&
                   donati1.Tur == donati2.Tur &&
                   donati1.YapiElemani == donati2.YapiElemani &&
                   donati1.Konum == donati2.Konum &&
                   donati1.DonatiSinifi == donati2.DonatiSinifi;
        }

        #endregion
    }
}

using System;
using System.Collections.Generic;
using DonDonat.WPF.Domain.Entities;

namespace DonDonat.Tests.Helpers
{
    /// <summary>
    /// Test verisi oluşturmak için builder pattern implementasyonu
    /// Fluent API ile test verisi oluşturma
    /// </summary>
    public class TestDataBuilder
    {
        /// <summary>
        /// Varsayılan donatı oluşturur
        /// </summary>
        /// <returns>Test donatısı</returns>
        public static Donati CreateDefaultDonati()
        {
            return new Donati
            {
                Id = Guid.NewGuid(),
                Cap = 12,
                Boy = 6.0,
                Adet = 10,
                Tur = "NERVÜRLÜ",
                YapiElemani = "KİRİŞ",
                Konum = "ÜST",
                DonatiSinifi = "S420"
            };
        }

        /// <summary>
        /// Belirtilen parametrelerle donatı oluşturur
        /// </summary>
        /// <param name="cap">Çap</param>
        /// <param name="boy">Boy</param>
        /// <param name="adet">Adet</param>
        /// <param name="tur">Tür</param>
        /// <param name="yapiElemani">Ya<PERSON><PERSON> elemanı</param>
        /// <param name="konum">Konum</param>
        /// <param name="donatiSinifi">Donatı sınıfı</param>
        /// <returns>Test donatısı</returns>
        public static Donati CreateDonati(
            int cap = 12,
            double boy = 6.0,
            int adet = 10,
            string tur = "NERVÜRLÜ",
            string yapiElemani = "KİRİŞ",
            string konum = "ÜST",
            string donatiSinifi = "S420")
        {
            return new Donati
            {
                Id = Guid.NewGuid(),
                Cap = cap,
                Boy = boy,
                Adet = adet,
                Tur = tur,
                YapiElemani = yapiElemani,
                Konum = konum,
                DonatiSinifi = donatiSinifi
            };
        }

        /// <summary>
        /// Donatı listesi oluşturur
        /// </summary>
        /// <param name="count">Donatı sayısı</param>
        /// <returns>Test donatı listesi</returns>
        public static List<Donati> CreateDonatiList(int count = 5)
        {
            var donatilar = new List<Donati>();
            var caplar = new[] { 8, 10, 12, 14, 16, 18, 20, 22, 25, 28 };
            var turler = new[] { "NERVÜRLÜ", "DÜZ" };
            var yapiElemanlari = new[] { "KİRİŞ", "KOLON", "DÖŞEME", "PERDE" };
            var konumlar = new[] { "ÜST", "ALT", "ENINE", "BOYUNA" };

            var random = new Random(42); // Sabit seed ile tekrarlanabilir testler

            for (int i = 0; i < count; i++)
            {
                donatilar.Add(new Donati
                {
                    Id = Guid.NewGuid(),
                    Cap = caplar[random.Next(caplar.Length)],
                    Boy = Math.Round(random.NextDouble() * 10 + 1, 2), // 1-11 metre arası
                    Adet = random.Next(1, 21), // 1-20 adet arası
                    Tur = turler[random.Next(turler.Length)],
                    YapiElemani = yapiElemanlari[random.Next(yapiElemanlari.Length)],
                    Konum = konumlar[random.Next(konumlar.Length)],
                    DonatiSinifi = "S420"
                });
            }

            return donatilar;
        }

        /// <summary>
        /// Varsayılan proje bilgileri oluşturur
        /// </summary>
        /// <returns>Test proje bilgileri</returns>
        public static ProjeBilgileri CreateDefaultProjeBilgileri()
        {
            return new ProjeBilgileri
            {
                Id = Guid.NewGuid(),
                ProjeAdi = "Test Projesi",
                ProjeKodu = "TEST-001",
                Musteri = "Test Müşteri",
                Lokasyon = "Test Lokasyon",
                Aciklama = "Test projesi açıklaması",
                OlusturmaTarihi = DateTime.Now,
                GuncellemeTarihi = DateTime.Now,
                Aktif = true
            };
        }

        /// <summary>
        /// Belirtilen parametrelerle proje bilgileri oluşturur
        /// </summary>
        /// <param name="projeAdi">Proje adı</param>
        /// <param name="projeKodu">Proje kodu</param>
        /// <param name="musteri">Müşteri</param>
        /// <param name="lokasyon">Lokasyon</param>
        /// <param name="aktif">Aktif mi</param>
        /// <returns>Test proje bilgileri</returns>
        public static ProjeBilgileri CreateProjeBilgileri(
            string projeAdi = "Test Projesi",
            string projeKodu = "TEST-001",
            string musteri = "Test Müşteri",
            string lokasyon = "Test Lokasyon",
            bool aktif = true)
        {
            return new ProjeBilgileri
            {
                Id = Guid.NewGuid(),
                ProjeAdi = projeAdi,
                ProjeKodu = projeKodu,
                Musteri = musteri,
                Lokasyon = lokasyon,
                Aciklama = $"{projeAdi} test açıklaması",
                OlusturmaTarihi = DateTime.Now,
                GuncellemeTarihi = DateTime.Now,
                Aktif = aktif
            };
        }

        /// <summary>
        /// Test DXF dosyası içeriği oluşturur
        /// </summary>
        /// <returns>Basit DXF dosya içeriği</returns>
        public static string CreateTestDxfContent()
        {
            return @"0
SECTION
2
HEADER
9
$ACADVER
1
AC1015
0
ENDSEC
0
SECTION
2
ENTITIES
0
LINE
8
DONATI
10
0.0
20
0.0
30
0.0
11
100.0
21
0.0
31
0.0
0
CIRCLE
8
DONATI
10
50.0
20
50.0
30
0.0
40
6.0
0
ENDSEC
0
EOF";
        }

        /// <summary>
        /// Geçersiz DXF dosyası içeriği oluşturur
        /// </summary>
        /// <returns>Geçersiz DXF dosya içeriği</returns>
        public static string CreateInvalidDxfContent()
        {
            return @"INVALID DXF CONTENT
This is not a valid DXF file
Missing proper structure";
        }
    }
}

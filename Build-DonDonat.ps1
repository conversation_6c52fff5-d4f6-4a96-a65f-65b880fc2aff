# DonDonat Master Build Script
# Bu script, DonDonat uygulamasını hem MSIX hem de ClickOnce olarak paketler

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("MSIX", "ClickOnce", "Both")]
    [string]$PackageType = "Both",
    
    [Parameter(Mandatory=$false)]
    [ValidateSet("Debug", "Release")]
    [string]$Configuration = "Release",
    
    [Parameter(Mandatory=$false)]
    [string]$Version = "*******",
    
    [Parameter(Mandatory=$false)]
    [string]$OutputPath = ".\Packages",
    
    [Parameter(Mandatory=$false)]
    [switch]$CleanFirst,
    
    [Parameter(Mandatory=$false)]
    [switch]$OpenOutput
)

# Renkli çıktı fonksiyonları
function Write-Success { param($Message) Write-Host $Message -ForegroundColor Green }
function Write-Info { param($Message) Write-Host $Message -ForegroundColor Cyan }
function Write-Warning { param($Message) Write-Host $Message -ForegroundColor Yellow }
function Write-Error { param($Message) Write-Host $Message -ForegroundColor Red }
function Write-Header { param($Message) Write-Host "`n$('='*50)" -ForegroundColor Magenta; Write-Host $Message -ForegroundColor Magenta; Write-Host $('='*50) -ForegroundColor Magenta }

Write-Header "DonDonat Master Build Script"
Write-Info "Paket Türü: $PackageType"
Write-Info "Konfigürasyon: $Configuration"
Write-Info "Versiyon: $Version"
Write-Info "Çıktı Yolu: $OutputPath"

# Çıktı klasörünü oluştur
if (-not (Test-Path $OutputPath)) {
    New-Item -ItemType Directory -Path $OutputPath -Force | Out-Null
    Write-Info "Çıktı klasörü oluşturuldu: $OutputPath"
}

# Temizlik
if ($CleanFirst) {
    Write-Info "Önceki çıktılar temizleniyor..."
    Get-ChildItem -Path $OutputPath -Recurse | Remove-Item -Force -Recurse
    Write-Success "✓ Temizlik tamamlandı"
}

# Gerekli araçları kontrol et
Write-Info "Gerekli araçlar kontrol ediliyor..."

# MSBuild kontrolü
$MSBuildPath = ""
$VSWhere = "${env:ProgramFiles(x86)}\Microsoft Visual Studio\Installer\vswhere.exe"

if (Test-Path $VSWhere) {
    $VSPath = & $VSWhere -latest -products * -requires Microsoft.Component.MSBuild -property installationPath
    if ($VSPath) {
        $MSBuildPath = Join-Path $VSPath "MSBuild\Current\Bin\MSBuild.exe"
        if (-not (Test-Path $MSBuildPath)) {
            $MSBuildPath = Join-Path $VSPath "MSBuild\15.0\Bin\MSBuild.exe"
        }
    }
}

if (-not (Test-Path $MSBuildPath)) {
    Write-Error "MSBuild bulunamadı. Visual Studio 2019 veya 2022 yüklü olduğundan emin olun."
    exit 1
}

Write-Success "✓ MSBuild bulundu: $MSBuildPath"

# .NET SDK kontrolü
try {
    $dotnetVersion = dotnet --version
    Write-Success "✓ .NET SDK bulundu: $dotnetVersion"
} catch {
    Write-Error ".NET SDK bulunamadı. .NET 9.0 SDK'yı yükleyin."
    exit 1
}

try {
    # Ana proje derleme
    Write-Header "Ana Proje Derleniyor"
    
    & $MSBuildPath "DonDonat.WPF\DonDonat.WPF.csproj" `
        /p:Configuration=$Configuration `
        /p:Platform="Any CPU" `
        /verbosity:minimal
        
    if ($LASTEXITCODE -ne 0) {
        throw "Ana proje derlenemedi"
    }
    Write-Success "✓ Ana proje başarıyla derlendi"

    # MSIX Paketleme
    if ($PackageType -eq "MSIX" -or $PackageType -eq "Both") {
        Write-Header "MSIX Paketi Oluşturuluyor"
        
        $MSIXOutputPath = Join-Path $OutputPath "MSIX"
        
        # İmaj dosyalarını kontrol et
        $ImagesPath = "DonDonat.Package\Images"
        if (-not (Test-Path $ImagesPath)) {
            Write-Warning "MSIX imaj dosyaları bulunamadı. Varsayılan imajlar oluşturuluyor..."
            
            # Basit imaj dosyaları oluştur (gerçek projede ImageMagick kullanın)
            New-Item -ItemType Directory -Path $ImagesPath -Force | Out-Null
            
            # Placeholder imajlar oluştur
            $PlaceholderContent = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
            $PlaceholderBytes = [Convert]::FromBase64String($PlaceholderContent)
            
            @("Square44x44Logo.scale-200.png", "Square150x150Logo.scale-200.png", "Wide310x150Logo.scale-200.png", 
              "LockScreenLogo.scale-200.png", "SplashScreen.scale-200.png", "StoreLogo.png", 
              "Square44x44Logo.targetsize-24_altform-unplated.png") | ForEach-Object {
                [System.IO.File]::WriteAllBytes((Join-Path $ImagesPath $_), $PlaceholderBytes)
            }
            
            Write-Info "Placeholder imajlar oluşturuldu"
        }
        
        # MSIX build (eğer Windows App SDK yüklüyse)
        if (Test-Path "DonDonat.Package\DonDonat.Package.wapproj") {
            try {
                & $MSBuildPath "DonDonat.Package\DonDonat.Package.wapproj" `
                    /p:Configuration=$Configuration `
                    /p:Platform="x64" `
                    /p:AppxPackageDir="$MSIXOutputPath\" `
                    /verbosity:minimal
                    
                if ($LASTEXITCODE -eq 0) {
                    Write-Success "✓ MSIX paketi başarıyla oluşturuldu"
                } else {
                    Write-Warning "⚠ MSIX paketi oluşturulamadı (Windows App SDK gerekli)"
                }
            } catch {
                Write-Warning "⚠ MSIX paketleme hatası: $($_.Exception.Message)"
            }
        } else {
            Write-Warning "⚠ MSIX proje dosyası bulunamadı"
        }
    }

    # ClickOnce Paketleme
    if ($PackageType -eq "ClickOnce" -or $PackageType -eq "Both") {
        Write-Header "ClickOnce Paketi Oluşturuluyor"
        
        $ClickOnceOutputPath = Join-Path $OutputPath "ClickOnce"
        
        # ClickOnce publish
        & $MSBuildPath "DonDonat.WPF\DonDonat.WPF.csproj" `
            /p:Configuration=$Configuration `
            /p:PublishDir="$ClickOnceOutputPath\" `
            /p:ApplicationVersion=$Version `
            /p:PublishUrl="https://your-domain.com/dondonat/" `
            /target:Publish `
            /verbosity:minimal
            
        if ($LASTEXITCODE -ne 0) {
            throw "ClickOnce paketi oluşturulamadı"
        }
        
        Write-Success "✓ ClickOnce paketi başarıyla oluşturuldu"
        
        # Kurulum web sayfası oluştur
        $WebPageContent = @"
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DonDonat - Kurulum</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 40px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
        .header { text-align: center; margin-bottom: 40px; }
        .logo { font-size: 3em; margin-bottom: 10px; }
        .title { font-size: 2em; color: #2C3E50; margin-bottom: 10px; }
        .subtitle { color: #7F8C8D; font-size: 1.2em; }
        .install-section { text-align: center; margin: 40px 0; }
        .install-btn { display: inline-block; background: linear-gradient(45deg, #3498DB, #2980B9); color: white; padding: 20px 40px; text-decoration: none; border-radius: 10px; font-size: 1.2em; margin: 15px; box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3); transition: transform 0.3s; }
        .install-btn:hover { transform: translateY(-3px); box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4); }
        .features { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 40px 0; }
        .feature { background: #F8F9FA; padding: 25px; border-radius: 10px; border-left: 5px solid #3498DB; }
        .feature h4 { color: #2C3E50; margin-bottom: 10px; }
        .requirements { background: #ECF0F1; padding: 25px; border-radius: 10px; margin: 30px 0; }
        .version-info { text-align: center; margin-top: 40px; color: #7F8C8D; border-top: 1px solid #ECF0F1; padding-top: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🏗️</div>
            <div class="title">DonDonat</div>
            <div class="subtitle">Betonarme Donatı Metraj Hesaplama Uygulaması</div>
        </div>
        
        <div class="install-section">
            <h2>Uygulamayı Yükleyin</h2>
            <a href="setup.exe" class="install-btn">💾 Hemen Yükle</a>
            <a href="DonDonat.WPF.application" class="install-btn">🔄 ClickOnce Kurulum</a>
        </div>
        
        <div class="features">
            <div class="feature">
                <h4>📐 DWG/DXF Desteği</h4>
                <p>AutoCAD dosyalarından donatı verilerini otomatik okuma ve analiz</p>
            </div>
            <div class="feature">
                <h4>📊 Akıllı Hesaplama</h4>
                <p>Otomatik ağırlık, uzunluk ve metraj hesaplamaları</p>
            </div>
            <div class="feature">
                <h4>📄 Profesyonel Raporlar</h4>
                <p>Excel ve PDF formatında detaylı metraj raporları</p>
            </div>
            <div class="feature">
                <h4>🔄 Otomatik Güncelleme</h4>
                <p>Yeni özellikler ve iyileştirmeler otomatik olarak güncellenir</p>
            </div>
        </div>
        
        <div class="requirements">
            <h3>📋 Sistem Gereksinimleri</h3>
            <ul>
                <li><strong>İşletim Sistemi:</strong> Windows 10 veya üzeri</li>
                <li><strong>Framework:</strong> .NET 9.0 Desktop Runtime</li>
                <li><strong>RAM:</strong> Minimum 4 GB</li>
                <li><strong>Disk Alanı:</strong> 500 MB boş alan</li>
                <li><strong>Ekran Çözünürlüğü:</strong> 1024x768 veya üzeri</li>
            </ul>
        </div>
        
        <div class="version-info">
            <p><strong>Versiyon:</strong> $Version | <strong>Derleme Tarihi:</strong> $(Get-Date -Format 'dd.MM.yyyy HH:mm')</p>
            <p>© 2024 YourCompany. Tüm hakları saklıdır.</p>
        </div>
    </div>
</body>
</html>
"@
        
        $WebPagePath = Join-Path $ClickOnceOutputPath "index.html"
        $WebPageContent | Out-File -FilePath $WebPagePath -Encoding UTF8
        Write-Success "✓ Kurulum web sayfası oluşturuldu"
    }

    # Sonuçları göster
    Write-Header "Paketleme Tamamlandı"
    
    Write-Success "🎉 DonDonat paketleme işlemi başarıyla tamamlandı!"
    Write-Info "Çıktı klasörü: $OutputPath"
    
    # Oluşturulan dosyaları listele
    $AllFiles = Get-ChildItem -Path $OutputPath -Recurse -File
    if ($AllFiles) {
        Write-Info "`nOluşturulan dosyalar:"
        $AllFiles | Group-Object Directory | ForEach-Object {
            Write-Host "`n📁 $($_.Name):" -ForegroundColor Yellow
            $_.Group | ForEach-Object {
                $size = [math]::Round($_.Length / 1MB, 2)
                Write-Host "  📄 $($_.Name) - $size MB" -ForegroundColor White
            }
        }
    }
    
    # Dağıtım talimatları
    Write-Header "Dağıtım Talimatları"
    
    if ($PackageType -eq "MSIX" -or $PackageType -eq "Both") {
        Write-Info "📦 MSIX Dağıtımı:"
        Write-Host "• MSIX dosyasını çift tıklayarak yükleyin" -ForegroundColor White
        Write-Host "• Microsoft Store'da yayınlayabilirsiniz" -ForegroundColor White
        Write-Host "• Kurumsal dağıtım için App Installer kullanın" -ForegroundColor White
    }
    
    if ($PackageType -eq "ClickOnce" -or $PackageType -eq "Both") {
        Write-Info "`n🌐 ClickOnce Dağıtımı:"
        Write-Host "• ClickOnce klasörünü web sunucunuza yükleyin" -ForegroundColor White
        Write-Host "• index.html sayfasını kullanıcılarla paylaşın" -ForegroundColor White
        Write-Host "• Otomatik güncelleme özelliği aktif" -ForegroundColor White
    }
    
    Write-Warning "`n⚠️  Önemli Notlar:"
    Write-Host "• İlk kurulumda güvenlik uyarısı alabilirsiniz" -ForegroundColor White
    Write-Host "• Üretim ortamı için kod imzalama sertifikası kullanın" -ForegroundColor White
    Write-Host "• Güncelleme URL'lerini kendi domain'inizle değiştirin" -ForegroundColor White
    
    # Çıktı klasörünü aç
    if ($OpenOutput) {
        Start-Process -FilePath "explorer.exe" -ArgumentList $OutputPath
    }

} catch {
    Write-Error "❌ Paketleme hatası: $($_.Exception.Message)"
    Write-Error "Detaylar: $($_.Exception.StackTrace)"
    exit 1
}

Write-Success "`n✅ Tüm işlemler başarıyla tamamlandı!"
Write-Info "Paketler hazır: $OutputPath"

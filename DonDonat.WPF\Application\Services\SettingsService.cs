using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text.Json;
using System.Threading.Tasks;
using System.Timers;
using DonDonat.WPF.Application.Interfaces;
using DonDonat.WPF.Domain.Entities;

namespace DonDonat.WPF.Application.Services
{
    /// <summary>
    /// Kullanıcı ayarları servisi implementasyonu
    /// JSON dosyası kullanarak ayarları yönetir
    /// </summary>
    public class SettingsService : ISettingsService, IDisposable
    {
        #region Private Fields

        private readonly string _settingsDirectory;
        private readonly string _settingsFilePath;
        private readonly string _backupFilePath;
        private UserSettings _currentSettings;
        private System.Timers.Timer? _autoSaveTimer;
        private bool _hasUnsavedChanges;

        private readonly JsonSerializerOptions _jsonOptions = new()
        {
            WriteIndented = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            AllowTrailingCommas = true,
            ReadCommentHandling = JsonCommentHandling.Skip
        };

        #endregion

        #region Constructor

        public SettingsService()
        {
            // AppData klasöründe ayarlar dizini oluştur
            _settingsDirectory = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                "DonDonat");

            _settingsFilePath = Path.Combine(_settingsDirectory, "settings.json");
            _backupFilePath = Path.Combine(_settingsDirectory, "settings.backup.json");

            // Dizin yoksa oluştur
            if (!Directory.Exists(_settingsDirectory))
            {
                Directory.CreateDirectory(_settingsDirectory);
            }

            // Varsayılan ayarları yükle
            _currentSettings = new UserSettings();
            
            // Property changed event'ini dinle
            _currentSettings.PropertyChanged += OnSettingsPropertyChanged;
        }

        #endregion

        #region Public Properties

        public UserSettings CurrentSettings => _currentSettings;

        #endregion

        #region Events

        public event EventHandler<UserSettings>? SettingsChanged;

        #endregion

        #region Public Methods

        /// <summary>
        /// Ayarları dosyadan yükler
        /// </summary>
        public async Task<bool> LoadSettingsAsync()
        {
            try
            {
                if (!File.Exists(_settingsFilePath))
                {
                    // Dosya yoksa varsayılan ayarları kaydet
                    await SaveSettingsAsync();
                    return true;
                }

                var json = await File.ReadAllTextAsync(_settingsFilePath);
                var loadedSettings = JsonSerializer.Deserialize<UserSettings>(json, _jsonOptions);

                if (loadedSettings != null)
                {
                    // Property changed event'ini geçici olarak kaldır
                    _currentSettings.PropertyChanged -= OnSettingsPropertyChanged;
                    
                    // Ayarları kopyala
                    CopySettings(loadedSettings, _currentSettings);
                    
                    // Event'i tekrar ekle
                    _currentSettings.PropertyChanged += OnSettingsPropertyChanged;
                    
                    _hasUnsavedChanges = false;
                    
                    // Event'i tetikle
                    SettingsChanged?.Invoke(this, _currentSettings);
                    
                    System.Diagnostics.Debug.WriteLine("Ayarlar başarıyla yüklendi.");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Ayarlar yüklenirken hata: {ex.Message}");
                
                // Hata durumunda backup'tan yüklemeyi dene
                return await RestoreSettingsFromBackupAsync();
            }
        }

        /// <summary>
        /// Ayarları dosyaya kaydeder
        /// </summary>
        public async Task<bool> SaveSettingsAsync()
        {
            try
            {
                _currentSettings.LastUpdated = DateTime.Now;
                
                var json = JsonSerializer.Serialize(_currentSettings, _jsonOptions);
                await File.WriteAllTextAsync(_settingsFilePath, json);
                
                _hasUnsavedChanges = false;
                
                System.Diagnostics.Debug.WriteLine("Ayarlar başarıyla kaydedildi.");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Ayarlar kaydedilirken hata: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Ayarları günceller ve kaydeder
        /// </summary>
        public async Task<bool> UpdateSettingsAsync(UserSettings settings)
        {
            try
            {
                if (settings == null)
                    return false;

                var (isValid, errorMessages) = ValidateSettings(settings);
                if (!isValid)
                {
                    System.Diagnostics.Debug.WriteLine($"Geçersiz ayarlar: {string.Join(", ", errorMessages)}");
                    return false;
                }

                // Property changed event'ini geçici olarak kaldır
                _currentSettings.PropertyChanged -= OnSettingsPropertyChanged;
                
                // Ayarları kopyala
                CopySettings(settings, _currentSettings);
                
                // Event'i tekrar ekle
                _currentSettings.PropertyChanged += OnSettingsPropertyChanged;
                
                // Kaydet
                var result = await SaveSettingsAsync();
                
                if (result)
                {
                    SettingsChanged?.Invoke(this, _currentSettings);
                }
                
                return result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Ayarlar güncellenirken hata: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Belirli bir ayarı günceller
        /// </summary>
        public async Task<bool> UpdateSettingAsync(string propertyName, object value)
        {
            try
            {
                var property = typeof(UserSettings).GetProperty(propertyName);
                if (property == null || !property.CanWrite)
                    return false;

                property.SetValue(_currentSettings, value);
                return await SaveSettingsAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Ayar güncellenirken hata: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Ayarları varsayılan değerlere sıfırlar
        /// </summary>
        public async Task<bool> ResetToDefaultsAsync()
        {
            try
            {
                _currentSettings.PropertyChanged -= OnSettingsPropertyChanged;
                _currentSettings.ResetToDefaults();
                _currentSettings.PropertyChanged += OnSettingsPropertyChanged;
                
                var result = await SaveSettingsAsync();
                
                if (result)
                {
                    SettingsChanged?.Invoke(this, _currentSettings);
                }
                
                return result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Ayarlar sıfırlanırken hata: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Ayarları JSON dosyasına export eder
        /// </summary>
        public async Task<bool> ExportSettingsAsync(string filePath)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(filePath))
                    return false;

                var directory = Path.GetDirectoryName(filePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                var json = JsonSerializer.Serialize(_currentSettings, _jsonOptions);
                await File.WriteAllTextAsync(filePath, json);
                
                System.Diagnostics.Debug.WriteLine($"Ayarlar export edildi: {filePath}");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Ayarlar export edilirken hata: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// JSON dosyasından ayarları import eder
        /// </summary>
        public async Task<bool> ImportSettingsAsync(string filePath)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(filePath) || !File.Exists(filePath))
                    return false;

                var json = await File.ReadAllTextAsync(filePath);
                var importedSettings = JsonSerializer.Deserialize<UserSettings>(json, _jsonOptions);

                if (importedSettings != null)
                {
                    var (isValid, errorMessages) = ValidateSettings(importedSettings);
                    if (!isValid)
                    {
                        System.Diagnostics.Debug.WriteLine($"Import edilen ayarlar geçersiz: {string.Join(", ", errorMessages)}");
                        return false;
                    }

                    return await UpdateSettingsAsync(importedSettings);
                }

                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Ayarlar import edilirken hata: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Ayarlar dosyasının var olup olmadığını kontrol eder
        /// </summary>
        public bool SettingsFileExists()
        {
            return File.Exists(_settingsFilePath);
        }

        /// <summary>
        /// Ayarlar dosyasının yolunu döndürür
        /// </summary>
        public string GetSettingsFilePath()
        {
            return _settingsFilePath;
        }

        /// <summary>
        /// Ayarları yedekler
        /// </summary>
        public async Task<bool> BackupSettingsAsync()
        {
            try
            {
                if (File.Exists(_settingsFilePath))
                {
                    await File.WriteAllTextAsync(_backupFilePath, await File.ReadAllTextAsync(_settingsFilePath));
                    System.Diagnostics.Debug.WriteLine("Ayarlar yedeklendi.");
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Ayarlar yedeklenirken hata: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Ayarları yedekten geri yükler
        /// </summary>
        public async Task<bool> RestoreSettingsFromBackupAsync()
        {
            try
            {
                if (File.Exists(_backupFilePath))
                {
                    var json = await File.ReadAllTextAsync(_backupFilePath);
                    var backupSettings = JsonSerializer.Deserialize<UserSettings>(json, _jsonOptions);

                    if (backupSettings != null)
                    {
                        return await UpdateSettingsAsync(backupSettings);
                    }
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Ayarlar yedekten geri yüklenirken hata: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Son dosyalar listesine dosya ekler
        /// </summary>
        public async Task<bool> AddRecentFileAsync(string filePath)
        {
            try
            {
                _currentSettings.AddRecentFile(filePath);
                return await SaveSettingsAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Son dosya eklenirken hata: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Son dosyalar listesinden dosya kaldırır
        /// </summary>
        public async Task<bool> RemoveRecentFileAsync(string filePath)
        {
            try
            {
                _currentSettings.RemoveRecentFile(filePath);
                return await SaveSettingsAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Son dosya kaldırılırken hata: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Son dosyalar listesini temizler
        /// </summary>
        public async Task<bool> ClearRecentFilesAsync()
        {
            try
            {
                _currentSettings.ClearRecentFiles();
                return await SaveSettingsAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Son dosyalar temizlenirken hata: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Pencere boyutunu günceller
        /// </summary>
        public async Task<bool> UpdateWindowSizeAsync(double width, double height, bool isMaximized)
        {
            try
            {
                if (_currentSettings.RememberWindowSize)
                {
                    _currentSettings.WindowWidth = width;
                    _currentSettings.WindowHeight = height;
                    _currentSettings.WindowMaximized = isMaximized;
                    return await SaveSettingsAsync();
                }
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Pencere boyutu güncellenirken hata: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Tema değiştirir
        /// </summary>
        public async Task<bool> ChangeThemeAsync(string theme)
        {
            try
            {
                if (theme != "Light" && theme != "Dark")
                    return false;

                _currentSettings.Theme = theme;
                return await SaveSettingsAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Tema değiştirilirken hata: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Ayarların geçerli olup olmadığını kontrol eder
        /// </summary>
        public (bool IsValid, string[] ErrorMessages) ValidateSettings(UserSettings settings)
        {
            var errors = new List<string>();

            if (settings == null)
            {
                errors.Add("Ayarlar null olamaz");
                return (false, errors.ToArray());
            }

            // Tema kontrolü
            if (settings.Theme != "Light" && settings.Theme != "Dark")
                errors.Add("Tema 'Light' veya 'Dark' olmalıdır");

            // Font boyutu kontrolü
            if (settings.FontSize < 8 || settings.FontSize > 72)
                errors.Add("Font boyutu 8-72 arasında olmalıdır");

            // Pencere boyutu kontrolü
            if (settings.WindowWidth < 400 || settings.WindowWidth > 4000)
                errors.Add("Pencere genişliği 400-4000 arasında olmalıdır");

            if (settings.WindowHeight < 300 || settings.WindowHeight > 3000)
                errors.Add("Pencere yüksekliği 300-3000 arasında olmalıdır");

            // Otomatik kaydetme aralığı kontrolü
            if (settings.AutoSaveIntervalMinutes < 1 || settings.AutoSaveIntervalMinutes > 60)
                errors.Add("Otomatik kaydetme aralığı 1-60 dakika arasında olmalıdır");

            // Son dosya sayısı kontrolü
            if (settings.MaxRecentFiles < 1 || settings.MaxRecentFiles > 50)
                errors.Add("Maksimum son dosya sayısı 1-50 arasında olmalıdır");

            return (errors.Count == 0, errors.ToArray());
        }

        /// <summary>
        /// Ayarları otomatik kaydetme özelliğini başlatır/durdurur
        /// </summary>
        public void SetAutoSave(bool enabled)
        {
            if (enabled && _autoSaveTimer == null)
            {
                _autoSaveTimer = new System.Timers.Timer(TimeSpan.FromMinutes(1).TotalMilliseconds);
                _autoSaveTimer.Elapsed += OnAutoSaveTimerElapsed;
                _autoSaveTimer.Start();
            }
            else if (!enabled && _autoSaveTimer != null)
            {
                _autoSaveTimer.Stop();
                _autoSaveTimer.Dispose();
                _autoSaveTimer = null;
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Ayarlar değiştiğinde tetiklenir
        /// </summary>
        private void OnSettingsPropertyChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            _hasUnsavedChanges = true;
            SettingsChanged?.Invoke(this, _currentSettings);
        }

        /// <summary>
        /// Otomatik kaydetme timer'ı tetiklendiğinde
        /// </summary>
        private async void OnAutoSaveTimerElapsed(object? sender, ElapsedEventArgs e)
        {
            if (_hasUnsavedChanges)
            {
                await SaveSettingsAsync();
            }
        }

        /// <summary>
        /// Ayarları bir nesneden diğerine kopyalar
        /// </summary>
        private void CopySettings(UserSettings source, UserSettings target)
        {
            var properties = typeof(UserSettings).GetProperties(BindingFlags.Public | BindingFlags.Instance)
                .Where(p => p.CanRead && p.CanWrite);

            foreach (var property in properties)
            {
                try
                {
                    var value = property.GetValue(source);
                    property.SetValue(target, value);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Ayar kopyalanırken hata ({property.Name}): {ex.Message}");
                }
            }
        }

        #endregion

        #region IDisposable Implementation

        public void Dispose()
        {
            _autoSaveTimer?.Stop();
            _autoSaveTimer?.Dispose();
            
            if (_hasUnsavedChanges)
            {
                SaveSettingsAsync().Wait();
            }
        }

        #endregion
    }
}

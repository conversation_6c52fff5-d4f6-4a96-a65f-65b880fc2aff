﻿<Window x:Class="DonDonat.WPF.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:DonDonat.WPF"
        xmlns:vm="clr-namespace:DonDonat.WPF.Presentation.ViewModels"
        mc:Ignorable="d"
        Title="DonDonat - Betonarme Donatı Metraj Hesaplama"
        Height="800" Width="1200"
        WindowStartupLocation="CenterScreen"
        Icon="pack://application:,,,/Resources/icon.ico">

    <Window.DataContext>
        <vm:MainViewModel />
    </Window.DataContext>

    <Window.Resources>
        <!-- Stil tanımlamaları -->
        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#2C3E50"/>
            <Setter Property="Margin" Value="5"/>
        </Style>

        <Style x:Key="ButtonStyle" TargetType="Button">
            <Setter Property="Padding" Value="10,5"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Background" Value="#3498DB"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#2980B9"/>
                </Trigger>
                <Trigger Property="IsEnabled" Value="False">
                    <Setter Property="Background" Value="#BDC3C7"/>
                    <Setter Property="Cursor" Value="Arrow"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="DataGridStyle" TargetType="DataGrid">
            <Setter Property="AutoGenerateColumns" Value="False"/>
            <Setter Property="CanUserAddRows" Value="False"/>
            <Setter Property="CanUserDeleteRows" Value="False"/>
            <Setter Property="IsReadOnly" Value="True"/>
            <Setter Property="SelectionMode" Value="Single"/>
            <Setter Property="GridLinesVisibility" Value="Horizontal"/>
            <Setter Property="HeadersVisibility" Value="Column"/>
            <Setter Property="AlternatingRowBackground" Value="#F8F9FA"/>
            <Setter Property="RowBackground" Value="White"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Başlık -->
        <Border Grid.Row="0" Background="#34495E" Padding="15">
            <TextBlock Text="DonDonat - Betonarme Donatı Metraj Hesaplama Sistemi"
                       FontSize="24" FontWeight="Bold" Foreground="White"
                       HorizontalAlignment="Center"/>
        </Border>

        <!-- Dosya Seçimi ve İşlemler -->
        <Border Grid.Row="1" Background="#ECF0F1" Padding="15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <Button Content="DXF Dosyası Seç"
                            Command="{Binding SelectFileCommand}"
                            Style="{StaticResource ButtonStyle}"/>

                    <TextBlock Text="{Binding SelectedFileName}"
                               VerticalAlignment="Center"
                               Margin="10,0"
                               FontStyle="Italic"/>

                    <Button Content="Dosyayı İşle"
                            Command="{Binding ParseFileCommand}"
                            Style="{StaticResource ButtonStyle}"
                            Background="#27AE60"/>

                    <Button Content="Temizle"
                            Command="{Binding ClearDataCommand}"
                            Style="{StaticResource ButtonStyle}"
                            Background="#E74C3C"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="📋 Proje Bilgileri"
                            Command="{Binding OpenProjeBilgileriCommand}"
                            Style="{StaticResource ButtonStyle}"
                            Background="#8E44AD"/>

                    <Button Content="Manuel Donatı Ekle"
                            Command="{Binding AddManualDonatiCommand}"
                            Style="{StaticResource ButtonStyle}"
                            Background="#F39C12"/>

                    <Button Content="📊 Excel'e Aktar"
                            Command="{Binding ExportToExcelCommand}"
                            Style="{StaticResource ButtonStyle}"
                            Background="#27AE60"/>

                    <Button Content="📄 PDF'e Aktar"
                            Command="{Binding ExportToPdfCommand}"
                            Style="{StaticResource ButtonStyle}"
                            Background="#E74C3C"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Ana İçerik -->
        <Grid Grid.Row="2" Margin="15">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="300"/>
            </Grid.ColumnDefinitions>

            <!-- Donatı Listesi -->
            <GroupBox Grid.Column="0" Header="Donatı Listesi" Margin="0,0,10,0">
                <Grid>
                    <DataGrid x:Name="DonatiDataGrid"
                              ItemsSource="{Binding Donatilar}"
                              SelectedItem="{Binding SelectedDonati}"
                              Style="{StaticResource DataGridStyle}">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="Çap (mm)" Binding="{Binding Cap}" Width="80"/>
                            <DataGridTextColumn Header="Boy (m)" Binding="{Binding Boy, StringFormat=F2}" Width="80"/>
                            <DataGridTextColumn Header="Adet" Binding="{Binding Adet}" Width="60"/>
                            <DataGridTextColumn Header="Tür" Binding="{Binding Tur}" Width="100"/>
                            <DataGridTextColumn Header="Yapı Elemanı" Binding="{Binding YapiElemani}" Width="120"/>
                            <DataGridTextColumn Header="Konum" Binding="{Binding Konum}" Width="80"/>
                            <DataGridTextColumn Header="Sınıf" Binding="{Binding DonatiSinifi}" Width="60"/>
                            <DataGridTextColumn Header="Toplam Uzunluk (m)" Binding="{Binding ToplamUzunluk, StringFormat=F2}" Width="120"/>
                            <DataGridTextColumn Header="Ağırlık (kg)" Binding="{Binding ToplamAgirlik, StringFormat=F2}" Width="100"/>
                        </DataGrid.Columns>
                    </DataGrid>

                    <!-- Loading Overlay -->
                    <Border Background="#80000000" Visibility="{Binding IsLoading, Converter={x:Static local:BooleanToVisibilityConverter.Instance}}">
                        <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                            <ProgressBar IsIndeterminate="True" Width="200" Height="20" Margin="10"/>
                            <TextBlock Text="DXF dosyası işleniyor..." Foreground="White" HorizontalAlignment="Center"/>
                        </StackPanel>
                    </Border>
                </Grid>
            </GroupBox>

            <!-- Sağ Panel - Özet ve İşlemler -->
            <StackPanel Grid.Column="1">
                <!-- Aktif Proje -->
                <GroupBox Header="Aktif Proje" Margin="0,0,0,10">
                    <StackPanel>
                        <TextBlock Text="{Binding ProjectDisplayName}"
                                   FontWeight="Bold"
                                   FontSize="14"
                                   Foreground="#2C3E50"
                                   Margin="5"/>
                        <TextBlock Text="Proje bilgilerini düzenlemek için 'Proje Bilgileri' butonunu kullanın."
                                   TextWrapping="Wrap"
                                   FontStyle="Italic"
                                   FontSize="10"
                                   Foreground="Gray"
                                   Margin="5,0,5,5"/>
                    </StackPanel>
                </GroupBox>

                <!-- Özet Bilgiler -->
                <GroupBox Header="Özet Bilgiler" Margin="0,0,0,10">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Row="0" Grid.Column="0" Text="Toplam Donatı Adedi:" Style="{StaticResource HeaderTextStyle}"/>
                        <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding TotalCount}" FontWeight="Bold" Margin="5"/>

                        <TextBlock Grid.Row="1" Grid.Column="0" Text="Toplam Uzunluk:" Style="{StaticResource HeaderTextStyle}"/>
                        <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding TotalLength, StringFormat='{}{0:F2} m'}" FontWeight="Bold" Margin="5"/>

                        <TextBlock Grid.Row="2" Grid.Column="0" Text="Toplam Ağırlık:" Style="{StaticResource HeaderTextStyle}"/>
                        <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding TotalWeight, StringFormat='{}{0:F2} kg'}" FontWeight="Bold" Margin="5"/>

                        <Separator Grid.Row="3" Grid.ColumnSpan="2" Margin="5"/>
                    </Grid>
                </GroupBox>

                <!-- Seçilen Donatı İşlemleri -->
                <GroupBox Header="Seçilen Donatı İşlemleri">
                    <StackPanel>
                        <Button Content="Seçilen Donatıyı Sil"
                                Command="{Binding RemoveSelectedDonatiCommand}"
                                Style="{StaticResource ButtonStyle}"
                                Background="#E74C3C"/>

                        <TextBlock Text="Seçilen donatıyı silmek için önce listeden bir donatı seçin."
                                   TextWrapping="Wrap"
                                   Margin="5"
                                   FontStyle="Italic"
                                   Foreground="Gray"/>
                    </StackPanel>
                </GroupBox>
            </StackPanel>
        </Grid>

        <!-- Durum Çubuğu -->
        <Border Grid.Row="4" Background="#34495E" Padding="10">
            <TextBlock Text="{Binding StatusMessage}"
                       Foreground="White"
                       FontWeight="SemiBold"/>
        </Border>
    </Grid>
</Window>

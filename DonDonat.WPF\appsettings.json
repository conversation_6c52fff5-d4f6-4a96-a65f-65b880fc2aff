{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "System": "Warning", "DonDonat": "Debug"}, "Console": {"IncludeScopes": true, "TimestampFormat": "yyyy-MM-dd HH:mm:ss "}, "File": {"Path": "Logs/DonDonat-{Date}.log", "MinLevel": "Information", "RollingInterval": "Day", "RetainedFileCountLimit": 30, "FileSizeLimitBytes": 10485760, "IncludeScopes": true}}, "AppSettings": {"ApplicationName": "<PERSON><PERSON><PERSON><PERSON>", "Version": "1.0.0", "Environment": "Development", "EnableDebugMode": true, "EnablePerformanceCounters": true, "MaxRecentFiles": 10, "AutoSaveIntervalMinutes": 5, "DefaultLanguage": "tr-TR", "DefaultTheme": "Light"}, "Database": {"Provider": "SQLite", "ConnectionString": "Data Source=Data/DonDonat.db;Cache=Shared", "CommandTimeout": 30, "EnableSensitiveDataLogging": false, "EnableDetailedErrors": true, "MigrationsAssembly": "DonDonat.WPF"}, "FileSettings": {"SupportedDwgFormats": [".dwg", ".dxf"], "SupportedExportFormats": [".xlsx", ".pdf", ".csv"], "MaxFileSizeMB": 100, "TempDirectory": "Temp", "ExportDirectory": "Exports", "BackupDirectory": "Backups"}, "ReportSettings": {"DefaultExcelTemplate": "Templates/DefaultReport.xlsx", "DefaultPdfTemplate": "Templates/DefaultReport.pdf", "CompanyName": "Your Company", "CompanyLogo": "Assets/logo.png", "ReportFooter": "DonDonat - Betonarme Donatı Metraj Hesaplama", "IncludeTimestamp": true, "IncludePageNumbers": true}, "PerformanceSettings": {"MaxConcurrentOperations": 4, "LargeFileThresholdMB": 50, "CacheExpirationMinutes": 30, "EnableMemoryOptimization": true, "GarbageCollectionMode": "Interactive"}, "SecuritySettings": {"EnableAuditLogging": true, "SessionTimeoutMinutes": 480, "MaxLoginAttempts": 5, "PasswordComplexity": "Medium"}, "UpdateSettings": {"CheckForUpdatesOnStartup": true, "UpdateCheckIntervalHours": 24, "UpdateServerUrl": "https://your-domain.com/dondonat/updates", "EnableBetaUpdates": false, "AutoDownloadUpdates": false}}
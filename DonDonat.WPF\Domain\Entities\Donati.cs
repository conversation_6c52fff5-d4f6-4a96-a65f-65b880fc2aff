using System;
using System.ComponentModel.DataAnnotations;

namespace DonDonat.WPF.Domain.Entities
{
    /// <summary>
    /// Betonarme donatı varlığını temsil eden domain sınıfı
    /// </summary>
    public class Donati
    {
        /// <summary>
        /// Donatının benzersiz kimliği
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Donatı çapı (mm cinsinden)
        /// Örnek: 8, 10, 12, 14, 16, 20, 22, 25, 28, 32 mm
        /// </summary>
        [Required]
        [Range(6, 50, ErrorMessage = "Donatı çapı 6-50 mm arasında olmalıdır")]
        public int Cap { get; set; }

        /// <summary>
        /// Donatı boyu (metre cinsinden)
        /// </summary>
        [Required]
        [Range(0.1, 20.0, ErrorMessage = "Donatı boyu 0.1-20 metre arasında olmalıdır")]
        public double Boy { get; set; }

        /// <summary>
        /// Donatı adedi
        /// </summary>
        [Required]
        [Range(1, int.MaxValue, ErrorMessage = "Donatı adedi 1'den büyük olmalıdır")]
        public int Adet { get; set; }

        /// <summary>
        /// Donatı türü (Nervürlü, Düz, vb.)
        /// </summary>
        [Required]
        [StringLength(50, ErrorMessage = "Donatı türü en fazla 50 karakter olabilir")]
        public string Tur { get; set; } = string.Empty;

        /// <summary>
        /// Donatının bulunduğu yapı elemanı (Kiriş, Kolon, Plak, vb.)
        /// </summary>
        [StringLength(100, ErrorMessage = "Yapı elemanı en fazla 100 karakter olabilir")]
        public string YapiElemani { get; set; } = string.Empty;

        /// <summary>
        /// Donatının konumu/pozisyonu (Üst, Alt, Yan, vb.)
        /// </summary>
        [StringLength(50, ErrorMessage = "Konum en fazla 50 karakter olabilir")]
        public string Konum { get; set; } = string.Empty;

        /// <summary>
        /// Donatının sınıfı (S220, S420, S500, vb.)
        /// </summary>
        [StringLength(20, ErrorMessage = "Donatı sınıfı en fazla 20 karakter olabilir")]
        public string DonatiSinifi { get; set; } = "S420";

        /// <summary>
        /// Oluşturulma tarihi
        /// </summary>
        public DateTime OlusturulmaTarihi { get; set; } = DateTime.Now;

        /// <summary>
        /// Güncellenme tarihi
        /// </summary>
        public DateTime GuncellenmeTarihi { get; set; } = DateTime.Now;

        /// <summary>
        /// Toplam donatı ağırlığını hesaplar (kg cinsinden)
        /// Formül: (π × (çap/2)² × boy × adet × 7.85) / 1000000
        /// 7.85: Çelik yoğunluğu (g/cm³)
        /// </summary>
        public double ToplamAgirlik
        {
            get
            {
                double capMetre = Cap / 1000.0; // mm'den metreye çevir
                double alan = Math.PI * Math.Pow(capMetre / 2, 2); // m² cinsinden alan
                double hacim = alan * Boy * Adet; // m³ cinsinden hacim
                return hacim * 7850; // kg cinsinden ağırlık (çelik yoğunluğu 7850 kg/m³)
            }
        }

        /// <summary>
        /// Toplam donatı uzunluğunu hesaplar (metre cinsinden)
        /// </summary>
        public double ToplamUzunluk
        {
            get
            {
                return Boy * Adet;
            }
        }

        /// <summary>
        /// Donatı birim ağırlığını hesaplar (kg/m)
        /// </summary>
        public double BirimAgirlik
        {
            get
            {
                double capMetre = Cap / 1000.0; // mm'den metreye çevir
                double alan = Math.PI * Math.Pow(capMetre / 2, 2); // m² cinsinden alan
                return alan * 7850; // kg/m cinsinden birim ağırlık
            }
        }

        /// <summary>
        /// Donatı nesnesinin string temsilini döndürür
        /// </summary>
        public override string ToString()
        {
            return $"Ø{Cap}mm - {Adet} adet x {Boy:F2}m - {Tur} ({YapiElemani})";
        }

        /// <summary>
        /// İki donatı nesnesinin eşitliğini kontrol eder
        /// </summary>
        public override bool Equals(object? obj)
        {
            if (obj is not Donati other) return false;
            return Id == other.Id;
        }

        /// <summary>
        /// Hash code döndürür
        /// </summary>
        public override int GetHashCode()
        {
            return Id.GetHashCode();
        }

        /// <summary>
        /// Donatı nesnesini klonlar
        /// </summary>
        public Donati Clone()
        {
            return new Donati
            {
                Id = Guid.NewGuid(), // Yeni ID oluştur
                Cap = this.Cap,
                Boy = this.Boy,
                Adet = this.Adet,
                Tur = this.Tur,
                YapiElemani = this.YapiElemani,
                Konum = this.Konum,
                DonatiSinifi = this.DonatiSinifi,
                OlusturulmaTarihi = DateTime.Now,
                GuncellenmeTarihi = DateTime.Now
            };
        }
    }
}

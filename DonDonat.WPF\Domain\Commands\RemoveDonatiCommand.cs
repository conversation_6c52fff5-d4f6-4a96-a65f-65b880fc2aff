using System;
using System.Collections.ObjectModel;
using DonDonat.WPF.Domain.Entities;

namespace DonDonat.WPF.Domain.Commands
{
    /// <summary>
    /// Donatı silme command'ı
    /// Undo/Redo desteği ile donatı silme işlemini yönetir
    /// </summary>
    public class RemoveDonatiCommand : BaseCommandAction
    {
        #region Private Fields

        private readonly ObservableCollection<Donati> _donatilar;
        private readonly Donati _donati;
        private int _originalIndex;

        #endregion

        #region Constructor

        /// <summary>
        /// RemoveDonatiCommand constructor
        /// </summary>
        /// <param name="donatilar">Donatı koleksiyonu</param>
        /// <param name="donati">Silinecek donatı</param>
        public RemoveDonatiCommand(ObservableCollection<Donati> donatilar, Donati donati)
            : base($"Donatı Silindi: {donati?.Cap}Ø - {donati?.Boy}cm")
        {
            _donatilar = donatilar ?? throw new ArgumentNullException(nameof(donatilar));
            _donati = donati ?? throw new ArgumentNullException(nameof(donati));
            _originalIndex = -1;

            // Açıklamayı güncelle
            Description = $"Donatı Silindi: {_donati.Cap}Ø - {_donati.Boy}cm - {_donati.Adet} adet";
        }

        #endregion

        #region Properties

        /// <summary>
        /// Silinen donatı
        /// </summary>
        public Donati Donati => _donati;

        /// <summary>
        /// Orijinal pozisyon
        /// </summary>
        public int OriginalIndex => _originalIndex;

        #endregion

        #region Overrides

        /// <summary>
        /// Donatı silme işlemini gerçekleştirir
        /// </summary>
        /// <returns>İşlemin başarılı olup olmadığı</returns>
        protected override bool ExecuteCore()
        {
            try
            {
                // Donatı listede var mı kontrol et
                if (!_donatilar.Contains(_donati))
                {
                    System.Diagnostics.Debug.WriteLine("Silinecek donatı listede bulunamadı.");
                    return false;
                }

                // Orijinal pozisyonu kaydet
                _originalIndex = _donatilar.IndexOf(_donati);

                // Donatıyı listeden kaldır
                _donatilar.Remove(_donati);
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Donatı silinirken hata: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Donatı silme işlemini geri alır
        /// </summary>
        /// <returns>İşlemin başarılı olup olmadığı</returns>
        protected override bool UndoCore()
        {
            try
            {
                // Donatı zaten listede var mı kontrol et
                if (_donatilar.Contains(_donati))
                {
                    System.Diagnostics.Debug.WriteLine("Geri alınacak donatı zaten listede mevcut.");
                    return false;
                }

                // Orijinal pozisyon geçerli mi kontrol et
                if (_originalIndex < 0 || _originalIndex > _donatilar.Count)
                {
                    // Geçersiz pozisyon ise sona ekle
                    _donatilar.Add(_donati);
                }
                else
                {
                    // Orijinal pozisyona geri ekle
                    _donatilar.Insert(_originalIndex, _donati);
                }

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Donatı silme geri alınırken hata: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Command'ın geçerli olup olmadığını kontrol eder
        /// </summary>
        /// <returns>Geçerli mi</returns>
        public override bool IsValid()
        {
            return _donatilar != null && _donati != null;
        }

        /// <summary>
        /// Command'ın çalıştırılabilir olup olmadığını kontrol eder
        /// </summary>
        /// <returns>Çalıştırılabilir mi</returns>
        public override bool CanExecute()
        {
            return base.CanExecute() && _donatilar.Contains(_donati);
        }

        /// <summary>
        /// Command'ın geri alınabilir olup olmadığını kontrol eder
        /// </summary>
        /// <returns>Geri alınabilir mi</returns>
        public override bool CanUndo()
        {
            return base.CanUndo() && !_donatilar.Contains(_donati) && _originalIndex >= 0;
        }

        #endregion
    }
}

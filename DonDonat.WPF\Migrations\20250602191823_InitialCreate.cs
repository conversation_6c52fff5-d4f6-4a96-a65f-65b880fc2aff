﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace DonDonat.WPF.Migrations
{
    /// <inheritdoc />
    public partial class InitialCreate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "ProjeBilgileri",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "TEXT", nullable: false),
                    ProjeAdi = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false, comment: "Proje adı"),
                    TemelTuru = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false, comment: "Temel türü"),
                    TemelDerinligi = table.Column<double>(type: "REAL", nullable: false, defaultValue: 1.5, comment: "Temel derinliği (m)"),
                    KatSayisi = table.Column<int>(type: "INTEGER", nullable: false, defaultValue: 1, comment: "Kat sayısı"),
                    KatYuksekligi = table.Column<double>(type: "REAL", nullable: false, defaultValue: 3.0, comment: "Kat yüksekliği (m)"),
                    BodrumVarMi = table.Column<bool>(type: "INTEGER", nullable: false, defaultValue: false, comment: "Bodrum var mı"),
                    BodrumKatSayisi = table.Column<int>(type: "INTEGER", nullable: false, defaultValue: 0, comment: "Bodrum kat sayısı"),
                    DosemeKalinligi = table.Column<int>(type: "INTEGER", nullable: false, defaultValue: 20, comment: "Döşeme kalınlığı (cm)"),
                    UzunlukBirimi = table.Column<string>(type: "TEXT", maxLength: 10, nullable: false, defaultValue: "m", comment: "Uzunluk birimi"),
                    AlanBirimi = table.Column<string>(type: "TEXT", maxLength: 10, nullable: false, defaultValue: "m²", comment: "Alan birimi"),
                    ProjeAciklamasi = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: false, comment: "Proje açıklaması"),
                    ProjeLokasyonu = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false, comment: "Proje lokasyonu"),
                    MuteahhitFirma = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false, comment: "Müteahhit firma"),
                    ProjeMuhendisi = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false, comment: "Proje mühendisi"),
                    OlusturulmaTarihi = table.Column<DateTime>(type: "TEXT", nullable: false, defaultValueSql: "datetime('now')", comment: "Oluşturulma tarihi"),
                    GuncellenmeTarihi = table.Column<DateTime>(type: "TEXT", nullable: false, defaultValueSql: "datetime('now')", comment: "Güncellenme tarihi"),
                    AktifProje = table.Column<bool>(type: "INTEGER", nullable: false, defaultValue: true, comment: "Aktif proje mi")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ProjeBilgileri", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Donatilar",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "TEXT", nullable: false),
                    Cap = table.Column<int>(type: "INTEGER", nullable: false, comment: "Donatı çapı (mm)"),
                    Boy = table.Column<double>(type: "REAL", nullable: false, comment: "Donatı boyu (m)"),
                    Adet = table.Column<int>(type: "INTEGER", nullable: false, comment: "Donatı adedi"),
                    Tur = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false, comment: "Donatı türü"),
                    YapiElemani = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false, comment: "Yapı elemanı"),
                    Konum = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false, comment: "Donatı konumu"),
                    DonatiSinifi = table.Column<string>(type: "TEXT", maxLength: 20, nullable: false, defaultValue: "S420", comment: "Donatı sınıfı"),
                    OlusturulmaTarihi = table.Column<DateTime>(type: "TEXT", nullable: false, defaultValueSql: "datetime('now')", comment: "Oluşturulma tarihi"),
                    GuncellenmeTarihi = table.Column<DateTime>(type: "TEXT", nullable: false, defaultValueSql: "datetime('now')", comment: "Güncellenme tarihi"),
                    ProjeId = table.Column<Guid>(type: "TEXT", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Donatilar", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Donatilar_ProjeBilgileri_ProjeId",
                        column: x => x.ProjeId,
                        principalTable: "ProjeBilgileri",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.InsertData(
                table: "ProjeBilgileri",
                columns: new[] { "Id", "AktifProje", "AlanBirimi", "BodrumKatSayisi", "BodrumVarMi", "DosemeKalinligi", "GuncellenmeTarihi", "KatSayisi", "KatYuksekligi", "MuteahhitFirma", "OlusturulmaTarihi", "ProjeAciklamasi", "ProjeAdi", "ProjeLokasyonu", "ProjeMuhendisi", "TemelDerinligi", "TemelTuru", "UzunlukBirimi" },
                values: new object[] { new Guid("434b86a9-04d1-4506-9223-cf27de60bd22"), true, "m²", 1, true, 25, new DateTime(2025, 6, 2, 22, 18, 22, 327, DateTimeKind.Local).AddTicks(871), 3, 3.2000000000000002, "Örnek İnşaat Ltd.", new DateTime(2025, 6, 2, 22, 18, 22, 327, DateTimeKind.Local).AddTicks(246), "Örnek test projesi", "Örnek Betonarme Proje", "İstanbul", "Test Mühendisi", 2.0, "Radye", "m" });

            migrationBuilder.InsertData(
                table: "Donatilar",
                columns: new[] { "Id", "Adet", "Boy", "Cap", "DonatiSinifi", "GuncellenmeTarihi", "Konum", "OlusturulmaTarihi", "ProjeId", "Tur", "YapiElemani" },
                values: new object[,]
                {
                    { new Guid("b7900934-2c91-434a-bf49-e6ef15166e77"), 8, 2.7999999999999998, 16, "S420", new DateTime(2025, 6, 2, 22, 18, 22, 329, DateTimeKind.Local).AddTicks(2277), "BOYUNA", new DateTime(2025, 6, 2, 22, 18, 22, 329, DateTimeKind.Local).AddTicks(2276), new Guid("434b86a9-04d1-4506-9223-cf27de60bd22"), "NERVÜRLÜ", "KOLON" },
                    { new Guid("efb04067-7902-4383-85e9-e5aff9e6372b"), 10, 3.5, 12, "S420", new DateTime(2025, 6, 2, 22, 18, 22, 329, DateTimeKind.Local).AddTicks(1768), "ÜST", new DateTime(2025, 6, 2, 22, 18, 22, 329, DateTimeKind.Local).AddTicks(1249), new Guid("434b86a9-04d1-4506-9223-cf27de60bd22"), "NERVÜRLÜ", "KİRİŞ" },
                    { new Guid("fdc54211-1ffc-4b35-9818-fe209f053c38"), 15, 4.2000000000000002, 8, "S420", new DateTime(2025, 6, 2, 22, 18, 22, 329, DateTimeKind.Local).AddTicks(2287), "ETRIYE", new DateTime(2025, 6, 2, 22, 18, 22, 329, DateTimeKind.Local).AddTicks(2286), new Guid("434b86a9-04d1-4506-9223-cf27de60bd22"), "ETRIYE", "KİRİŞ" }
                });

            migrationBuilder.CreateIndex(
                name: "IX_Donatilar_Cap",
                table: "Donatilar",
                column: "Cap");

            migrationBuilder.CreateIndex(
                name: "IX_Donatilar_ProjeId",
                table: "Donatilar",
                column: "ProjeId");

            migrationBuilder.CreateIndex(
                name: "IX_Donatilar_Tur",
                table: "Donatilar",
                column: "Tur");

            migrationBuilder.CreateIndex(
                name: "IX_Donatilar_YapiElemani",
                table: "Donatilar",
                column: "YapiElemani");

            migrationBuilder.CreateIndex(
                name: "IX_ProjeBilgileri_AktifProje",
                table: "ProjeBilgileri",
                column: "AktifProje");

            migrationBuilder.CreateIndex(
                name: "IX_ProjeBilgileri_OlusturulmaTarihi",
                table: "ProjeBilgileri",
                column: "OlusturulmaTarihi");

            migrationBuilder.CreateIndex(
                name: "IX_ProjeBilgileri_ProjeAdi",
                table: "ProjeBilgileri",
                column: "ProjeAdi");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Donatilar");

            migrationBuilder.DropTable(
                name: "ProjeBilgileri");
        }
    }
}

param(
    [Parameter(Mandatory=$true)]
    [string]$PngPath,
    
    [Parameter(Mandatory=$false)]
    [string]$OutputPath = "DonDonat.WPF\Resources\DonDonat.ico"
)

# ImageMagick kontrolü
if (-not (Get-Command "magick" -ErrorAction SilentlyContinue)) {
    Write-Host "ImageMagick bulunamadı. Lütfen yükleyin: winget install ImageMagick.ImageMagick" -ForegroundColor Red
    exit 1
}

# PNG'yi ICO'ya dönüştür
magick convert "$PngPath" -define icon:auto-resize=256,128,64,48,32,16 "$OutputPath"

Write-Host "✅ PNG dosyası başarıyla ICO'ya dönüştürüldü: $OutputPath" -ForegroundColor Green
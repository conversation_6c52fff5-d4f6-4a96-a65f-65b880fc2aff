# Basit DonDonat İkonu Oluşturucu
Add-Type -AssemblyName System.Drawing
Add-Type -AssemblyName System.Windows.Forms

try {
    Write-Host "🎨 DonDonat ikonu oluşturuluyor..." -ForegroundColor Cyan
    
    # 256x256 bitmap oluştur
    $size = 256
    $bitmap = New-Object System.Drawing.Bitmap($size, $size)
    $graphics = [System.Drawing.Graphics]::FromImage($bitmap)
    
    # Anti-aliasing aktif et
    $graphics.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::AntiAlias
    $graphics.TextRenderingHint = [System.Drawing.Text.TextRenderingHint]::AntiAlias
    
    # Arka plan (koyu mavi)
    $bgBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(44, 62, 80))
    $graphics.FillRectangle($bgBrush, 0, 0, $size, $size)
    
    # Beyaz metin için brush
    $textBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::White)
    
    # Ana metin "DD"
    $font = New-Object System.Drawing.Font("Arial", 80, [System.Drawing.FontStyle]::Bold)
    $text = "DD"
    $textSize = $graphics.MeasureString($text, $font)
    $x = ($size - $textSize.Width) / 2
    $y = ($size - $textSize.Height) / 2 - 20
    $graphics.DrawString($text, $font, $textBrush, $x, $y)
    
    # Alt kısımda inşaat çizgileri
    $pen = New-Object System.Drawing.Pen([System.Drawing.Color]::White, 6)
    
    # Yatay çizgiler (donatı çubukları)
    $graphics.DrawLine($pen, 40, 200, 216, 200)   # Alt çizgi
    $graphics.DrawLine($pen, 60, 180, 196, 180)   # Orta çizgi
    $graphics.DrawLine($pen, 80, 160, 176, 160)   # Üst çizgi
    
    # Dikey bağlantılar
    $thinPen = New-Object System.Drawing.Pen([System.Drawing.Color]::White, 3)
    $graphics.DrawLine($thinPen, 70, 160, 70, 200)    # Sol
    $graphics.DrawLine($thinPen, 128, 140, 128, 200)  # Orta
    $graphics.DrawLine($thinPen, 186, 160, 186, 200)  # Sağ
    
    # PNG olarak kaydet
    $pngPath = "DonDonat.png"
    $bitmap.Save($pngPath, [System.Drawing.Imaging.ImageFormat]::Png)
    Write-Host "✅ PNG oluşturuldu: $pngPath" -ForegroundColor Green
    
    # ICO için farklı boyutlar oluştur
    $iconSizes = @(16, 32, 48, 64, 128, 256)
    $iconBitmaps = @()
    
    foreach ($iconSize in $iconSizes) {
        $iconBitmap = New-Object System.Drawing.Bitmap($iconSize, $iconSize)
        $iconGraphics = [System.Drawing.Graphics]::FromImage($iconBitmap)
        $iconGraphics.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::AntiAlias
        $iconGraphics.InterpolationMode = [System.Drawing.Drawing2D.InterpolationMode]::HighQualityBicubic
        
        # Orijinal resmi yeniden boyutlandır
        $iconGraphics.DrawImage($bitmap, 0, 0, $iconSize, $iconSize)
        $iconBitmaps += $iconBitmap
        $iconGraphics.Dispose()
    }
    
    # ICO dosyası oluştur (manuel)
    $icoPath = "DonDonat.ico"
    
    # Basit ICO header oluştur
    $iconData = @()
    
    # ICO header (6 bytes)
    $iconData += 0, 0  # Reserved
    $iconData += 1, 0  # Type (1 = ICO)
    $iconData += $iconSizes.Count, 0  # Number of images
    
    # Her boyut için directory entry (16 bytes each)
    $dataOffset = 6 + ($iconSizes.Count * 16)
    
    for ($i = 0; $i -lt $iconSizes.Count; $i++) {
        $iconSize = $iconSizes[$i]
        $iconBitmap = $iconBitmaps[$i]
        
        # PNG data'yı al
        $ms = New-Object System.IO.MemoryStream
        $iconBitmap.Save($ms, [System.Drawing.Imaging.ImageFormat]::Png)
        $pngData = $ms.ToArray()
        $ms.Dispose()
        
        # Directory entry
        $iconData += if ($iconSize -eq 256) { 0 } else { $iconSize }  # Width
        $iconData += if ($iconSize -eq 256) { 0 } else { $iconSize }  # Height
        $iconData += 0  # Color count
        $iconData += 0  # Reserved
        $iconData += 1, 0  # Planes
        $iconData += 32, 0  # Bits per pixel
        
        # Data size (4 bytes, little endian)
        $dataSize = $pngData.Length
        $iconData += $dataSize -band 0xFF
        $iconData += ($dataSize -shr 8) -band 0xFF
        $iconData += ($dataSize -shr 16) -band 0xFF
        $iconData += ($dataSize -shr 24) -band 0xFF
        
        # Data offset (4 bytes, little endian)
        $iconData += $dataOffset -band 0xFF
        $iconData += ($dataOffset -shr 8) -band 0xFF
        $iconData += ($dataOffset -shr 16) -band 0xFF
        $iconData += ($dataOffset -shr 24) -band 0xFF
        
        $dataOffset += $dataSize
    }
    
    # PNG data'ları ekle
    for ($i = 0; $i -lt $iconSizes.Count; $i++) {
        $iconBitmap = $iconBitmaps[$i]
        $ms = New-Object System.IO.MemoryStream
        $iconBitmap.Save($ms, [System.Drawing.Imaging.ImageFormat]::Png)
        $pngData = $ms.ToArray()
        $iconData += $pngData
        $ms.Dispose()
        $iconBitmap.Dispose()
    }
    
    # ICO dosyasını yaz
    [System.IO.File]::WriteAllBytes($icoPath, [byte[]]$iconData)
    Write-Host "✅ ICO oluşturuldu: $icoPath" -ForegroundColor Green
    
    # Cleanup
    $graphics.Dispose()
    $bitmap.Dispose()
    $bgBrush.Dispose()
    $textBrush.Dispose()
    $font.Dispose()
    $pen.Dispose()
    $thinPen.Dispose()
    
    # Dosya boyutlarını göster
    if (Test-Path $pngPath) {
        $pngSize = [math]::Round((Get-Item $pngPath).Length / 1KB, 2)
        Write-Host "🖼️ PNG boyutu: $pngSize KB" -ForegroundColor Yellow
    }
    
    if (Test-Path $icoPath) {
        $icoSize = [math]::Round((Get-Item $icoPath).Length / 1KB, 2)
        Write-Host "📄 ICO boyutu: $icoSize KB" -ForegroundColor Yellow
    }
    
    Write-Host "🎉 İkon başarıyla oluşturuldu!" -ForegroundColor Green
    
} catch {
    Write-Host "❌ Hata: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Detay: $($_.Exception.StackTrace)" -ForegroundColor Red
}

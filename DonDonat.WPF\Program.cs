using System;
using System.IO;
using System.Threading.Tasks;
using System.Windows;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using DonDonat.WPF.Infrastructure.Logging;
using DonDonat.WPF.Infrastructure.Services;
using DonDonat.WPF.Application.Services;
using DonDonat.WPF.Presentation.ViewModels;

namespace DonDonat.WPF
{
    /// <summary>
    /// DonDonat WPF Uygulaması Ana Giriş Noktası
    /// MVVM mimarisi ile dependency injection ve global hata yakalama
    /// </summary>
    public static class Program
    {
        private static ILogger<App>? _logger;
        private static IHost? _host;

        /// <summary>
        /// Uygulama ana giriş noktası
        /// </summary>
        /// <param name="args">Komut satırı argümanları</param>
        [STAThread]
        public static async Task<int> Main(string[] args)
        {
            try
            {
                // Uygulama başlangıç logları
                Console.WriteLine("🏗️ DonDonat Uygulaması Başlatılıyor...");
                Console.WriteLine($"📅 Başlangıç Zamanı: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                Console.WriteLine($"🔧 .NET Versiyonu: {Environment.Version}");
                Console.WriteLine($"💻 İşletim Sistemi: {Environment.OSVersion}");
                Console.WriteLine($"📁 Çalışma Dizini: {Environment.CurrentDirectory}");

                // Host ve DI container'ı oluştur
                _host = CreateHostBuilder(args).Build();
                
                // Logger'ı al
                _logger = _host.Services.GetRequiredService<ILogger<App>>();
                _logger.LogInformation("🚀 DonDonat uygulaması başlatılıyor...");

                // Servisleri başlat
                await StartServicesAsync();

                // WPF uygulamasını başlat
                var app = _host.Services.GetRequiredService<App>();
                app.InitializeComponent();
                
                _logger.LogInformation("✅ WPF uygulaması başarıyla başlatıldı");
                
                return app.Run();
            }
            catch (Exception ex)
            {
                // Kritik başlangıç hatası
                var errorMessage = $"❌ Kritik başlangıç hatası: {ex.Message}";
                Console.WriteLine(errorMessage);
                Console.WriteLine($"📋 Detaylar: {ex}");
                
                _logger?.LogCritical(ex, "Uygulama başlatılamadı");
                
                // Kullanıcıya hata mesajı göster
                MessageBox.Show(
                    $"DonDonat uygulaması başlatılamadı.\n\nHata: {ex.Message}\n\nDetaylar için log dosyalarını kontrol edin.",
                    "Kritik Hata",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
                
                return -1;
            }
            finally
            {
                // Kaynakları temizle
                await DisposeAsync();
            }
        }

        /// <summary>
        /// Host Builder yapılandırması
        /// </summary>
        /// <param name="args">Komut satırı argümanları</param>
        /// <returns>Yapılandırılmış host builder</returns>
        private static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .ConfigureAppConfiguration((context, config) =>
                {
                    // Yapılandırma dosyalarını yükle
                    config.SetBasePath(Directory.GetCurrentDirectory())
                          .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true)
                          .AddJsonFile($"appsettings.{context.HostingEnvironment.EnvironmentName}.json", optional: true)
                          .AddEnvironmentVariables()
                          .AddCommandLine(args);
                })
                .ConfigureLogging((context, logging) =>
                {
                    // Logging yapılandırması
                    logging.ClearProviders()
                           .AddConfiguration(context.Configuration.GetSection("Logging"))
                           .AddConsole()
                           .AddDebug()
                           .AddFile("Logs/DonDonat-{Date}.log"); // File logging için
                })
                .ConfigureServices((context, services) =>
                {
                    // Dependency Injection yapılandırması
                    ConfigureServices(services, context.Configuration);
                })
                .UseConsoleLifetime();

        /// <summary>
        /// Servisleri DI container'a kaydet
        /// </summary>
        /// <param name="services">Service collection</param>
        /// <param name="configuration">Yapılandırma</param>
        private static void ConfigureServices(IServiceCollection services, IConfiguration configuration)
        {
            Console.WriteLine("🔧 Servisler yapılandırılıyor...");

            // WPF Application
            services.AddSingleton<App>();

            // ViewModels
            services.AddTransient<MainViewModel>();
            services.AddTransient<SettingsViewModel>();
            services.AddTransient<AboutViewModel>();

            // Application Services
            services.AddScoped<IDonatiService, DonatiService>();
            services.AddScoped<IReportService, ReportService>();
            services.AddScoped<ISettingsService, SettingsService>();
            services.AddScoped<IProjeBilgileriService, ProjeBilgileriService>();

            // Infrastructure Services
            services.AddScoped<IDWGParserService, DWGParserService>();
            services.AddScoped<IFileService, FileService>();
            services.AddScoped<IExcelExportService, ExcelExportService>();
            services.AddScoped<IPdfExportService, PdfExportService>();

            // Persistence Services
            services.AddScoped<IApplicationDbContext, ApplicationDbContext>();
            services.AddScoped<IDonatiRepository, DonatiRepository>();
            services.AddScoped<IProjeBilgileriRepository, ProjeBilgileriRepository>();

            // Utility Services
            services.AddSingleton<IDialogService, DialogService>();
            services.AddSingleton<IMessageService, MessageService>();

            // Configuration
            services.Configure<AppSettings>(configuration.GetSection("AppSettings"));
            services.Configure<DatabaseSettings>(configuration.GetSection("Database"));
            services.Configure<LoggingSettings>(configuration.GetSection("Logging"));

            Console.WriteLine("✅ Servisler başarıyla yapılandırıldı");
        }

        /// <summary>
        /// Servisleri başlat ve bağımlılıkları kontrol et
        /// </summary>
        private static async Task StartServicesAsync()
        {
            try
            {
                _logger?.LogInformation("🔄 Servisler başlatılıyor...");

                // Database bağlantısını kontrol et
                var dbContext = _host!.Services.GetRequiredService<IApplicationDbContext>();
                await dbContext.EnsureDatabaseCreatedAsync();
                _logger?.LogInformation("✅ Veritabanı bağlantısı başarılı");

                // Settings servisini başlat
                var settingsService = _host.Services.GetRequiredService<ISettingsService>();
                await settingsService.LoadSettingsAsync();
                _logger?.LogInformation("✅ Ayarlar yüklendi");

                // Gerekli klasörleri oluştur
                EnsureDirectoriesExist();
                _logger?.LogInformation("✅ Klasör yapısı kontrol edildi");

                // Lisans kontrolü (varsa)
                await CheckLicenseAsync();

                _logger?.LogInformation("🎉 Tüm servisler başarıyla başlatıldı");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "❌ Servis başlatma hatası");
                throw;
            }
        }

        /// <summary>
        /// Gerekli klasörlerin varlığını kontrol et ve oluştur
        /// </summary>
        private static void EnsureDirectoriesExist()
        {
            var directories = new[]
            {
                "Logs",
                "Data",
                "Reports",
                "Templates",
                "Temp",
                "Exports"
            };

            foreach (var dir in directories)
            {
                if (!Directory.Exists(dir))
                {
                    Directory.CreateDirectory(dir);
                    Console.WriteLine($"📁 Klasör oluşturuldu: {dir}");
                }
            }
        }

        /// <summary>
        /// Lisans kontrolü (opsiyonel)
        /// </summary>
        private static async Task CheckLicenseAsync()
        {
            try
            {
                // Lisans kontrolü implementasyonu
                await Task.Delay(100); // Placeholder
                _logger?.LogInformation("✅ Lisans kontrolü tamamlandı");
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "⚠️ Lisans kontrolü başarısız");
            }
        }

        /// <summary>
        /// Kaynakları temizle
        /// </summary>
        private static async Task DisposeAsync()
        {
            try
            {
                if (_host != null)
                {
                    _logger?.LogInformation("🔄 Uygulama kapatılıyor...");
                    await _host.StopAsync(TimeSpan.FromSeconds(5));
                    _host.Dispose();
                    _logger?.LogInformation("✅ Uygulama başarıyla kapatıldı");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Kapatma hatası: {ex.Message}");
            }
        }
    }
}

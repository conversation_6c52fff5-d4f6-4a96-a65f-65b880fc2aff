using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using DonDonat.WPF.Domain.Entities;
using DonDonat.WPF.Infrastructure.Parsers;
using DonDonat.WPF.Presentation.Windows;
using Microsoft.Win32;

namespace DonDonat.WPF.Presentation.ViewModels
{
    /// <summary>
    /// Ana pencere için ViewModel sınıfı
    /// MVVM pattern'ine uygun olarak tasarlanmıştır
    /// </summary>
    public class MainViewModel : INotifyPropertyChanged
    {
        private readonly DWGParser _dwgParser;
        private string _selectedFilePath = "";
        private bool _isLoading = false;
        private string _statusMessage = "Hazır";
        private double _totalWeight = 0;
        private double _totalLength = 0;
        private int _totalCount = 0;

        public MainViewModel()
        {
            _dwgParser = new DWGParser();
            Donatilar = new ObservableCollection<Donati>();
            
            // Command'ları initialize et
            SelectFileCommand = new RelayCommand(SelectFile);
            ParseFileCommand = new RelayCommand(ParseFile, CanParseFile);
            ClearDataCommand = new RelayCommand(ClearData, CanClearData);
            ExportToExcelCommand = new RelayCommand(ExportToExcel, CanExportToExcel);
            AddManualDonatiCommand = new RelayCommand(AddManualDonati);
            RemoveSelectedDonatiCommand = new RelayCommand(RemoveSelectedDonati, CanRemoveSelectedDonati);
            OpenProjeBilgileriCommand = new RelayCommand(OpenProjeBilgileri);
        }

        #region Properties

        /// <summary>
        /// Donatı koleksiyonu
        /// </summary>
        public ObservableCollection<Donati> Donatilar { get; }

        /// <summary>
        /// Seçilen dosya yolu
        /// </summary>
        public string SelectedFilePath
        {
            get => _selectedFilePath;
            set
            {
                _selectedFilePath = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(SelectedFileName));
                ((RelayCommand)ParseFileCommand).RaiseCanExecuteChanged();
            }
        }

        /// <summary>
        /// Seçilen dosya adı
        /// </summary>
        public string SelectedFileName => 
            string.IsNullOrEmpty(SelectedFilePath) ? "Dosya seçilmedi" : Path.GetFileName(SelectedFilePath);

        /// <summary>
        /// Yükleme durumu
        /// </summary>
        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                _isLoading = value;
                OnPropertyChanged();
                ((RelayCommand)ParseFileCommand).RaiseCanExecuteChanged();
                ((RelayCommand)SelectFileCommand).RaiseCanExecuteChanged();
            }
        }

        /// <summary>
        /// Durum mesajı
        /// </summary>
        public string StatusMessage
        {
            get => _statusMessage;
            set
            {
                _statusMessage = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Toplam ağırlık (kg)
        /// </summary>
        public double TotalWeight
        {
            get => _totalWeight;
            set
            {
                _totalWeight = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Toplam uzunluk (m)
        /// </summary>
        public double TotalLength
        {
            get => _totalLength;
            set
            {
                _totalLength = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Toplam adet
        /// </summary>
        public int TotalCount
        {
            get => _totalCount;
            set
            {
                _totalCount = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Seçilen donatı
        /// </summary>
        private Donati? _selectedDonati;
        public Donati? SelectedDonati
        {
            get => _selectedDonati;
            set
            {
                _selectedDonati = value;
                OnPropertyChanged();
                ((RelayCommand)RemoveSelectedDonatiCommand).RaiseCanExecuteChanged();
            }
        }

        #endregion

        #region Commands

        public ICommand SelectFileCommand { get; }
        public ICommand ParseFileCommand { get; }
        public ICommand ClearDataCommand { get; }
        public ICommand ExportToExcelCommand { get; }
        public ICommand AddManualDonatiCommand { get; }
        public ICommand RemoveSelectedDonatiCommand { get; }
        public ICommand OpenProjeBilgileriCommand { get; }

        #endregion

        #region Command Methods

        /// <summary>
        /// DXF dosyası seçme
        /// </summary>
        private void SelectFile()
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "DXF Dosyası Seçin",
                Filter = "DXF Dosyaları (*.dxf)|*.dxf|Tüm Dosyalar (*.*)|*.*",
                FilterIndex = 1
            };

            if (openFileDialog.ShowDialog() == true)
            {
                SelectedFilePath = openFileDialog.FileName;
                StatusMessage = "Dosya seçildi: " + SelectedFileName;
            }
        }

        /// <summary>
        /// Dosyayı parse etme
        /// </summary>
        private async void ParseFile()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "DXF dosyası işleniyor...";

                var donatilar = await _dwgParser.ParseDXFFileAsync(SelectedFilePath);
                
                Donatilar.Clear();
                foreach (var donati in donatilar)
                {
                    Donatilar.Add(donati);
                }

                CalculateTotals();
                StatusMessage = $"İşlem tamamlandı. {donatilar.Count} donatı bulundu.";
            }
            catch (Exception ex)
            {
                StatusMessage = $"Hata: {ex.Message}";
                MessageBox.Show($"Dosya işlenirken hata oluştu:\n{ex.Message}", 
                    "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// Verileri temizleme
        /// </summary>
        private void ClearData()
        {
            Donatilar.Clear();
            SelectedFilePath = "";
            CalculateTotals();
            StatusMessage = "Veriler temizlendi.";
        }

        /// <summary>
        /// Excel'e aktarma (placeholder)
        /// </summary>
        private void ExportToExcel()
        {
            // Bu metod Excel export işlemini gerçekleştirecek
            // Şimdilik placeholder olarak bırakıyoruz
            MessageBox.Show("Excel export özelliği henüz implement edilmedi.", 
                "Bilgi", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// Manuel donatı ekleme
        /// </summary>
        private void AddManualDonati()
        {
            var yeniDonati = new Donati
            {
                Id = Guid.NewGuid(),
                Cap = 12,
                Boy = 1.0,
                Adet = 1,
                Tur = "NERVÜRLÜ",
                YapiElemani = "KİRİŞ",
                Konum = "ÜST",
                DonatiSinifi = "S420"
            };

            Donatilar.Add(yeniDonati);
            CalculateTotals();
            StatusMessage = "Yeni donatı eklendi.";
        }

        /// <summary>
        /// Seçilen donatıyı silme
        /// </summary>
        private void RemoveSelectedDonati()
        {
            if (SelectedDonati != null)
            {
                Donatilar.Remove(SelectedDonati);
                CalculateTotals();
                StatusMessage = "Donatı silindi.";
            }
        }

        /// <summary>
        /// Proje Bilgileri penceresini açma
        /// </summary>
        private void OpenProjeBilgileri()
        {
            var projeBilgileriWindow = new ProjeBilgileriWindow();
            projeBilgileriWindow.ShowDialog();
        }

        #endregion

        #region Command Can Execute Methods

        private bool CanParseFile() => !IsLoading && !string.IsNullOrEmpty(SelectedFilePath) && File.Exists(SelectedFilePath);
        private bool CanClearData() => Donatilar.Count > 0;
        private bool CanExportToExcel() => Donatilar.Count > 0;
        private bool CanRemoveSelectedDonati() => SelectedDonati != null;

        #endregion

        #region Helper Methods

        /// <summary>
        /// Toplam değerleri hesaplar
        /// </summary>
        private void CalculateTotals()
        {
            TotalWeight = Donatilar.Sum(d => d.ToplamAgirlik);
            TotalLength = Donatilar.Sum(d => d.ToplamUzunluk);
            TotalCount = Donatilar.Sum(d => d.Adet);
        }

        #endregion

        #region INotifyPropertyChanged Implementation

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }

    /// <summary>
    /// Basit RelayCommand implementasyonu
    /// </summary>
    public class RelayCommand : ICommand
    {
        private readonly Action _execute;
        private readonly Func<bool>? _canExecute;

        public RelayCommand(Action execute, Func<bool>? canExecute = null)
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
        }

        public event EventHandler? CanExecuteChanged;

        public bool CanExecute(object? parameter) => _canExecute?.Invoke() ?? true;

        public void Execute(object? parameter) => _execute();

        public void RaiseCanExecuteChanged() => CanExecuteChanged?.Invoke(this, EventArgs.Empty);
    }
}

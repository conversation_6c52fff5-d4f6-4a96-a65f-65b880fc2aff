﻿using System;
using System.IO;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using DonDonat.WPF.Presentation.ViewModels;
using DonDonat.WPF.Presentation.Views;
using DonDonat.WPF.Application.Services;

namespace DonDonat.WPF
{
    /// <summary>
    /// DonDonat WPF Application Class
    /// Global exception handling ve MVVM başlangıç yapılandırması
    /// </summary>
    public partial class App : System.Windows.Application
    {
        private IHost? _host;
        private ILogger<App>? _logger;
        private IServiceProvider? _serviceProvider;

        /// <summary>
        /// Uygulama başlangıcı
        /// </summary>
        /// <param name="e">Startup event args</param>
        protected override async void OnStartup(StartupEventArgs e)
        {
            try
            {
                // Base startup
                base.OnStartup(e);

                // Host'u al (Program.cs'den inject edilir)
                _host = (IHost)Current.Properties["Host"];
                _serviceProvider = _host?.Services;
                _logger = _serviceProvider?.GetService<ILogger<App>>();

                _logger?.LogInformation("🚀 WPF Application başlatılıyor...");

                // Global exception handlers'ı kaydet
                RegisterGlobalExceptionHandlers();

                // Ana pencereyi oluştur ve göster
                await CreateAndShowMainWindowAsync();

                _logger?.LogInformation("✅ WPF Application başarıyla başlatıldı");
            }
            catch (Exception ex)
            {
                HandleCriticalError("Uygulama başlatma hatası", ex);
            }
        }

        /// <summary>
        /// Ana pencereyi oluştur ve göster
        /// </summary>
        private async Task CreateAndShowMainWindowAsync()
        {
            try
            {
                _logger?.LogInformation("🏠 Ana pencere oluşturuluyor...");

                // MainViewModel'i DI'dan al
                var mainViewModel = _serviceProvider?.GetRequiredService<MainViewModel>();
                if (mainViewModel == null)
                {
                    throw new InvalidOperationException("MainViewModel DI container'dan alınamadı");
                }

                // MainWindow'u oluştur
                var mainWindow = new MainWindow
                {
                    DataContext = mainViewModel
                };

                // Ana pencereyi ayarla
                MainWindow = mainWindow;

                // Pencere olaylarını dinle
                mainWindow.Loaded += MainWindow_Loaded;
                mainWindow.Closing += MainWindow_Closing;

                // Pencereyi göster
                mainWindow.Show();

                _logger?.LogInformation("✅ Ana pencere başarıyla gösterildi");

                // ViewModel'i başlat
                if (mainViewModel is IAsyncInitializable asyncInit)
                {
                    await asyncInit.InitializeAsync();
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "❌ Ana pencere oluşturma hatası");
                throw;
            }
        }

        /// <summary>
        /// Global exception handler'ları kaydet
        /// </summary>
        private void RegisterGlobalExceptionHandlers()
        {
            _logger?.LogInformation("🛡️ Global exception handlers kaydediliyor...");

            // UI Thread exceptions
            DispatcherUnhandledException += App_DispatcherUnhandledException;

            // Non-UI Thread exceptions
            AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;

            // Task exceptions
            TaskScheduler.UnobservedTaskException += TaskScheduler_UnobservedTaskException;

            _logger?.LogInformation("✅ Global exception handlers kaydedildi");
        }

        /// <summary>
        /// UI Thread exception handler
        /// </summary>
        private void App_DispatcherUnhandledException(object sender, DispatcherUnhandledExceptionEventArgs e)
        {
            try
            {
                var exception = e.Exception;
                _logger?.LogError(exception, "❌ UI Thread'de yakalanmamış hata");

                // Hata detaylarını logla
                LogExceptionDetails("UI Thread Exception", exception);

                // Kullanıcıya hata mesajı göster
                var result = MessageBox.Show(
                    $"Beklenmeyen bir hata oluştu:\n\n{exception.Message}\n\nUygulama devam etsin mi?",
                    "Hata",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Error);

                if (result == MessageBoxResult.Yes)
                {
                    e.Handled = true; // Hatayı handle et, uygulama devam etsin
                    _logger?.LogInformation("🔄 Kullanıcı uygulamanın devam etmesini seçti");
                }
                else
                {
                    _logger?.LogInformation("🛑 Kullanıcı uygulamayı kapatmayı seçti");
                    Shutdown(-1);
                }
            }
            catch (Exception ex)
            {
                // Exception handler'da bile hata olursa
                Console.WriteLine($"❌ Exception handler hatası: {ex}");
                Shutdown(-1);
            }
        }

        /// <summary>
        /// Non-UI Thread exception handler
        /// </summary>
        private void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            try
            {
                var exception = e.ExceptionObject as Exception;
                _logger?.LogCritical(exception, "❌ Non-UI Thread'de yakalanmamış kritik hata");

                // Hata detaylarını logla
                LogExceptionDetails("Non-UI Thread Exception", exception);

                // Kritik hata mesajı
                var message = exception?.Message ?? "Bilinmeyen kritik hata";
                MessageBox.Show(
                    $"Kritik hata oluştu ve uygulama kapatılacak:\n\n{message}",
                    "Kritik Hata",
                    MessageBoxButton.OK,
                    MessageBoxImage.Stop);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Critical exception handler hatası: {ex}");
            }
            finally
            {
                // Kritik hatalarda uygulamayı kapat
                Environment.Exit(-1);
            }
        }

        /// <summary>
        /// Task exception handler
        /// </summary>
        private void TaskScheduler_UnobservedTaskException(object? sender, UnobservedTaskExceptionEventArgs e)
        {
            try
            {
                _logger?.LogError(e.Exception, "❌ Task'ta yakalanmamış hata");

                // Hata detaylarını logla
                LogExceptionDetails("Task Exception", e.Exception);

                // Exception'ı observed olarak işaretle
                e.SetObserved();

                _logger?.LogInformation("🔄 Task exception handled ve observed olarak işaretlendi");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Task exception handler hatası: {ex}");
            }
        }

        /// <summary>
        /// Ana pencere yüklendiğinde
        /// </summary>
        private async void MainWindow_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                _logger?.LogInformation("🏠 Ana pencere yüklendi");

                // Başlangıç işlemlerini yap
                await PerformStartupTasksAsync();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "❌ Ana pencere yükleme sonrası hata");
                HandleNonCriticalError("Ana pencere başlangıç hatası", ex);
            }
        }

        /// <summary>
        /// Ana pencere kapatılırken
        /// </summary>
        private async void MainWindow_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            try
            {
                _logger?.LogInformation("🔄 Ana pencere kapatılıyor...");

                // Kapatma işlemlerini yap
                await PerformShutdownTasksAsync();

                _logger?.LogInformation("✅ Ana pencere başarıyla kapatıldı");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "❌ Ana pencere kapatma hatası");
                // Kapatma hatalarında uygulamayı zorla kapat
            }
        }

        /// <summary>
        /// Başlangıç görevlerini gerçekleştir
        /// </summary>
        private async Task PerformStartupTasksAsync()
        {
            try
            {
                _logger?.LogInformation("🚀 Başlangıç görevleri başlatılıyor...");

                // Son kullanılan ayarları yükle
                var settingsService = _serviceProvider?.GetService<ISettingsService>();
                if (settingsService != null)
                {
                    await settingsService.LoadSettingsAsync();
                    _logger?.LogInformation("✅ Kullanıcı ayarları yüklendi");
                }

                // Son açılan projeyi yükle (varsa)
                await LoadLastProjectAsync();

                // Güncelleme kontrolü yap (arka planda)
                _ = Task.Run(CheckForUpdatesAsync);

                _logger?.LogInformation("✅ Başlangıç görevleri tamamlandı");
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "⚠️ Bazı başlangıç görevleri tamamlanamadı");
            }
        }

        /// <summary>
        /// Kapatma görevlerini gerçekleştir
        /// </summary>
        private async Task PerformShutdownTasksAsync()
        {
            try
            {
                _logger?.LogInformation("🔄 Kapatma görevleri başlatılıyor...");

                // Ayarları kaydet
                var settingsService = _serviceProvider?.GetService<ISettingsService>();
                if (settingsService != null)
                {
                    await settingsService.SaveSettingsAsync();
                    _logger?.LogInformation("✅ Kullanıcı ayarları kaydedildi");
                }

                // Geçici dosyaları temizle
                CleanupTempFiles();

                // Veritabanı bağlantılarını kapat
                await CloseDbConnectionsAsync();

                _logger?.LogInformation("✅ Kapatma görevleri tamamlandı");
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "⚠️ Bazı kapatma görevleri tamamlanamadı");
            }
        }
    }
}

